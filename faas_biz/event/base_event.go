package event

import (
	"context"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

type BaseEvent interface {
	GetEventName() string
	Handle(ctx context.Context, args map[string]interface{}) error
}

var events []BaseEvent

func InitEvent() {
	events = []BaseEvent{
		// &SyncTobsreGuarantee{},
		// &SyncProjectGuarantee{},
		// &RefreshCustomerDisplay{},
		// &RefreshCustomerC360Field{},
		// &NotifyNotClosedAlarm{},
		// &NotifyOutdatedPendingAlarm{},
		// &SyncCustomerImpactApply{},
		// &SyncCustomerImpactExecute{},
		// &NotifyCustomerAffinitySummary{},
		// &NotifyCustomerImpact{},
		// NewSyncCustomerQuota(),
		// &CustomerQuotaLark{},
		// &CustomerQuotaAlarm{},
		// &CustomerNegativeFeedback{},
	}
}

func ScheduleEvent(ctx context.Context, eventName string, args map[string]interface{}) error {
	logger := utils.NewModuleLogger("ScheduleEvent")

	logger.CtxInfo(ctx, "schedule event:%s, args:%s", eventName, utils.JsonToString(args))
	isMatch := false
	for _, event := range events {
		if event.GetEventName() != eventName {
			continue
		}

		isMatch = true
		beginTime := time.Now()
		err := event.Handle(ctx, args)
		if err != nil {
			logger.CtxError(ctx, "handle event error, event_name:%s, err:%+v", eventName, err)
			return err
		}
		logger.CtxInfo(ctx, "handle event finish, event_name:%s, cost:%s", eventName, time.Since(beginTime).String())
	}

	if !isMatch {
		logger.CtxError(ctx, "no event match, event_name:%s", eventName)
	}
	return nil
}
