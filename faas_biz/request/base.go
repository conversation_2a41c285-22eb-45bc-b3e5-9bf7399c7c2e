package request

import (
	"context"
	"net/http"

	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

type BaseRequest interface {
	GetMethod() []string
	GetPath() string
	Handler(ctx context.Context, req *events.HTTPRequest) (*events.EventResponse, error)
}

var requests = []BaseRequest{
	&DemoHTTP{},
}

func ScheduleRequest(ctx context.Context, req *events.HTTPRequest) (*events.EventResponse, error) {
	logger := utils.NewModuleLogger("ScheduleRequest")
	logger.CtxInfo(ctx, "schedule request:%s", utils.JsonToString(req))
	for _, request := range requests {
		if len(request.GetMethod()) > 0 {
			if !slicex.Contains(request.GetMethod(), req.HTTPMethod) {
				continue
			}
		}

		if request.GetPath() != req.Path {
			continue
		}

		resp, err := request.Handler(ctx, req)
		if err != nil {
			logger.CtxError(ctx, "ScheduleRequest method: %s, error: %+v", request.GetMethod(), err)
			return nil, err
		}
		return resp, nil
	}

	logger.CtxError(ctx, "no request match")
	return &events.EventResponse{
		StatusCode: http.StatusNotFound,
		Body:       []byte("no request match"),
	}, nil
}
