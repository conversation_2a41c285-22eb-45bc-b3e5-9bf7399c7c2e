package request

import (
	"context"
	"net/http"

	"code.byted.org/bytefaas/faas-go/events"
)

type DemoHTTP struct {
}

func (d DemoHTTP) GetMethod() []string {
	return []string{http.MethodGet, http.MethodPost}
}

func (d DemoHTTP) GetPath() string {
	return "/demo"
}

func (d DemoHTTP) Handler(ctx context.Context, req *events.HTTPRequest) (*events.EventResponse, error) {
	return &events.EventResponse{
		Body: []byte("Demo!!!"),
	}, nil
}
