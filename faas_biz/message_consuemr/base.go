package message_consuemr

import (
	"context"
	"fmt"
	"reflect"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

type MessageEvent struct {
	Topic         string
	ConsumerGroup string
	EventName     string
	Offset        string
	Data          []byte `json:"-"`
}

type MessageConsumer interface {
	Match(ctx context.Context, event *MessageEvent) bool
	Executor(ctx context.Context, event *MessageEvent) error
}

var MQConsumers = []MessageConsumer{
	// &C360MQConsumer{},
	// &SpacexMQConsumer{},
}

func ScheduleMessageConsumer(ctx context.Context, event *MessageEvent) (err error) {
	logger := utils.NewModuleLogger("ScheduleMessageConsumer")

	defer func() {
		if p := recover(); p != nil {
			logger.CtxError(ctx, "mq consumer execute panic error，event=%s, err=%+v", utils.JsonToString(event), p)
			err = fmt.Errorf("cronjob panic")
		}
	}()

	isMatch := false
	for _, consumer := range MQConsumers {
		if consumer.Match(ctx, event) {
			isMatch = true
			logger.CtxInfo(ctx, "trigger mq consumer topic=%s, group=%s, mq_consumer=%s", event.Topic, event.ConsumerGroup, reflect.TypeOf(consumer).Name())
			err = consumer.Executor(ctx, event)
			if err != nil {
				logger.CtxInfo(ctx, "mq consumer meet error err=%v, mq_consumer=%s, topic=%s, group=%s, offset=%s", err, reflect.TypeOf(consumer).Name(), event.Topic, event.ConsumerGroup, event.Offset)
				return err
			}
		}
	}

	if !isMatch {
		logger.CtxError(ctx, "no message consumer match, event_name:%s, event_topic:%s, consumer_group:%s", event.EventName, event.Topic, event.ConsumerGroup)
	}
	return nil
}
