package common

import (
	"context"
	"encoding/json"
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
)

const (
	IaasActivityName          = "iaasActivity"
	PaasActivityName          = "paasActivity"
	NetworkActivityName       = "networkActivity"
	LinkPaasToEcsActivityName = "linkPaasToEcsActivity"
)

type ErrorItem struct {
	ResourceID     string
	DiagnoseItemID int64
	Error          error
}

func FindParamValueByDiagnoseItemID(ctx context.Context, diagnoseItemID int64, paramGroupIDKey string) (string, error) {
	diagnoseItemInfo, err := dal.FindDiagnoseItemByID(ctx, diagnoseItemID)
	if err != nil {
		return "", err
	}
	if diagnoseItemInfo.InterParams == nil {
		return "", fmt.Errorf("InterParams is nil")
	}
	params := make(map[string]string)
	err = json.Unmarshal([]byte(*diagnoseItemInfo.InterParams), &params)
	if err != nil {
		return "", err
	}
	if _, ok := params[paramGroupIDKey]; ok {
		return params[paramGroupIDKey], nil
	}
	return "", fmt.Errorf("%v not found", paramGroupIDKey)
}

func FindHostByDiagnoseItemID(ctx context.Context, diagnoseItemID int64) (string, error) {
	diagnoseItemInfo, err := dal.FindDiagnoseItemByID(ctx, diagnoseItemID)
	if err != nil {
		return "", err
	}
	if diagnoseItemInfo.AccessPath == nil {
		return "", fmt.Errorf("AccessPath is nil")
	}
	return *diagnoseItemInfo.AccessPath, nil
}
