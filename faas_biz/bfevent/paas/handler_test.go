package paas

import (
	"context"
	"reflect"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

// Client 定义CMDB客户端接口
type Client interface {
	ListEntities(ctx context.Context, req *cmdb.ListEntitiesRequest) (*cmdb.ListEntitiesResponse, error)
	ListEntitiesWithPage(ctx context.Context, req *cmdb.ListEntitiesRequest, pageNumber, pageSize int32) ([]interface{}, error)
}

var cmdbClient Client

// TestGetHandler 测试工厂方法
func TestGetHandler(t *testing.T) {
	tests := []struct {
		name         string
		resourceType string
		wantErr      bool
	}{
		{"VkeInstance", "VkeInstance", false},
		{"VkePod", "VkePod", false},
		{"VkeNode", "VkeNode", false},
		{"VkeNodePool", "VkeNodePool", false},
		{"InvalidType", "InvalidType", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := getHandler(tt.resourceType)
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// TestVkeInstanceAdapter 测试VkeInstance适配器
func TestVkeInstanceAdapter(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockCMDB := NewMockCMDBClient(ctrl)
	mockCMDB.EXPECT().ListEntitiesWithPage(gomock.Any(), gomock.Any(), int32(1), int32(100)).
		Return([]interface{}{&cmdb.VKEClusterInstance{}}, nil)

	instance := &cmdb.VKEClusterInstance{
		Spec: &cmdb.VKEClusterInstanceSpec{
			ClusterId: "cluster1",
		},
	}
	adapter := &vkeInstanceAdapter{instance}

	// 测试BuildListEntitiesRequest
	listReq, err := adapter.BuildListEntitiesRequest(context.Background(), []string{"cluster1"})
	assert.NoError(t, err)
	assert.Equal(t, "VkeInstance", listReq.Kind)

	// 测试BuildDiagnoseRequest
	req, err := adapter.BuildDiagnoseRequest(context.Background(), "scenario", "T-2025010821400001", &cmdb.VKEClusterInstance{}, nil)
	assert.NoError(t, err)
	assert.Equal(t, "VkeInstance", req.TargetType)

	// 测试GetResourceID
	testInstance := &cmdb.VKEClusterInstance{
		Spec: &cmdb.VKEClusterInstanceSpec{
			ClusterId: "cluster1",
		},
	}
	id := adapter.GetResourceID(testInstance)
	assert.Equal(t, "cluster1", id)
}

// TestRunDiagnoseItem 测试主流程
func TestRunDiagnoseItem(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 准备mock数据
	mockCMDB := NewMockCMDBClient(ctrl)
	mockCMDB.EXPECT().ListEntitiesWithPage(gomock.Any(), gomock.Any(), int32(1), int32(100)).
		Return([]interface{}{&cmdb.VKEClusterInstance{}}, nil)

	// 替换原始client
	originalClient := cmdbClient
	cmdbClient = mockCMDB
	defer func() { cmdbClient = originalClient }()

	// 准备测试数据
	resources := []*diagnose_result.DiagnoseResource{
		{
			ProductCategoryID: 1,
			Instances:         []string{"cluster1"},
		},
	}
	diagnoseInput := &statemachines.DiagnoseRequest{
		TicketID: "123",
	}

	h := &Handler{}
	_, _, err := h.RunDiagnoseItem(context.Background(), 1, resources, diagnoseInput)
	assert.NoError(t, err)
}

// MockCMDBClient 模拟CMDB客户端
type MockCMDBClient struct {
	ctrl     *gomock.Controller
	recorder *MockCMDBClientMockRecorder
}

func NewMockCMDBClient(ctrl *gomock.Controller) *MockCMDBClient {
	mock := &MockCMDBClient{ctrl: ctrl}
	mock.recorder = &MockCMDBClientMockRecorder{mock}
	return mock
}

func (m *MockCMDBClient) EXPECT() *MockCMDBClientMockRecorder {
	return m.recorder
}

func (m *MockCMDBClient) ListEntities(ctx context.Context, req *cmdb.ListEntitiesRequest) (*cmdb.ListEntitiesResponse, error) {
	ret := m.ctrl.Call(m, "ListEntities", ctx, req)
	return ret[0].(*cmdb.ListEntitiesResponse), ret[1].(error)
}

func (m *MockCMDBClient) ListEntitiesWithPage(ctx context.Context, req *cmdb.ListEntitiesRequest, pageNumber, pageSize int32) ([]interface{}, error) {
	ret := m.ctrl.Call(m, "ListEntitiesWithPage", ctx, req, pageNumber, pageSize)
	return ret[0].([]interface{}), ret[1].(error)
}

type MockCMDBClientMockRecorder struct {
	mock *MockCMDBClient
}

func (m *MockCMDBClientMockRecorder) ListEntities(ctx, req interface{}) *gomock.Call {
	return m.mock.ctrl.RecordCallWithMethodType(m.mock, "ListEntities", reflect.TypeOf((*MockCMDBClient)(nil).ListEntities), ctx, req)
}

func (m *MockCMDBClientMockRecorder) ListEntitiesWithPage(ctx, req, pageNumber, pageSize interface{}) *gomock.Call {
	return m.mock.ctrl.RecordCallWithMethodType(m.mock, "ListEntitiesWithPage", reflect.TypeOf((*MockCMDBClient)(nil).ListEntitiesWithPage), ctx, req, pageNumber, pageSize)
}
