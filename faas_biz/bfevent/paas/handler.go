package paas

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
)

// resourceHandler 定义资源处理策略接口
type resourceHandler interface {
	BuildListEntitiesRequest(ctx context.Context, instances []string) (*cmdb.ListEntitiesRequest, error)
	BuildDiagnoseRequest(
		ctx context.Context,
		scenario string,
		ticketID string,
		entity interface{},
		instances []string,
	) (*paasdiagnose.CreateDiagnoseTaskRequest, error)
	GetResourceID(entity interface{}) string
	GetInstanceInfoFromReport(data string) (instanceID, instanceName, region string, err error)
}

// 适配器类型定义
type vkeInstanceAdapter struct{ *cmdb.VKEClusterInstance }
type vkePodAdapter struct{ *cmdb.VkePod }
type vkeNodeAdapter struct{ *cmdb.VkeNode }
type vkeNodePoolAdapter struct{ *cmdb.VkeNodePool }

// 实现resourceHandler接口方法
func (a *vkeInstanceAdapter) BuildDiagnoseRequest(ctx context.Context, scenario string, ticketID string, entity interface{}, _ []string) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	return a.VKEClusterInstance.BuildDiagnoseRequest(ctx, scenario, ticketID, entity)
}

func (a *vkePodAdapter) BuildDiagnoseRequest(ctx context.Context, scenario string, ticketID string, entity interface{}, _ []string) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	return a.VkePod.BuildDiagnoseRequest(ctx, scenario, ticketID, entity)
}

func (a *vkeNodeAdapter) BuildDiagnoseRequest(ctx context.Context, scenario string, ticketID string, entity interface{}, _ []string) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	return a.VkeNode.BuildDiagnoseRequest(ctx, scenario, ticketID, entity)
}

func (a *vkeNodePoolAdapter) BuildDiagnoseRequest(ctx context.Context, scenario string, ticketID string, entity interface{}, instances []string) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	return a.VkeNodePool.BuildDiagnoseRequest(ctx, scenario, ticketID, entity, instances)
}

// 实现GetResourceID方法
func (a *vkeInstanceAdapter) GetResourceID(entity interface{}) string {
	if instance, ok := entity.(*cmdb.VKEClusterInstance); ok {
		return instance.Spec.ClusterId
	}
	return ""
}

func (a *vkePodAdapter) GetResourceID(entity interface{}) string {
	if pod, ok := entity.(*cmdb.VkePod); ok {
		return fmt.Sprintf("%s/%s/%s", pod.Spec.ClusterId, pod.Spec.Namespace, pod.Metadata.Name)
	}
	return ""
}

func (a *vkeNodeAdapter) GetResourceID(entity interface{}) string {
	if node, ok := entity.(*cmdb.VkeNode); ok {
		return node.Spec.NodeId
	}
	return ""
}

func (a *vkeNodePoolAdapter) GetResourceID(entity interface{}) string {
	if nodePool, ok := entity.(*cmdb.VkeNodePool); ok {
		return nodePool.Spec.NodePoolId
	}
	return ""
}

// getHandler 根据资源类型返回对应的处理策略
func getHandler(resourceType string) (resourceHandler, error) {
	switch resourceType {
	case "VkeInstance":
		return &vkeInstanceAdapter{&cmdb.VKEClusterInstance{}}, nil
	case "VkePod":
		return &vkePodAdapter{&cmdb.VkePod{}}, nil
	case "VkeNode":
		return &vkeNodeAdapter{&cmdb.VkeNode{}}, nil
	case "VkeNodePool":
		return &vkeNodePoolAdapter{&cmdb.VkeNodePool{}}, nil
	default:
		return nil, fmt.Errorf("unsupported resource type: %s", resourceType)
	}
}

const (
	ScenarioKey = "Scenario"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetName() string {
	return common.PaasActivityName
}

func (h *Handler) TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error) {
	logger := utils.NewModuleLogger("PaaSActivity-TransferRespToResults")
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	switch paasResp := resp.(type) {
	case *paasdiagnose.PhaseResult:

		// instanceID := ""
		// instanceName := ""
		// region := ""
		modelObject := &paasdiagnose.ModelObject{}
		resourceHandler, err := getHandler(paasResp.Target.ModelName)
		if err != nil {
			return nil, fmt.Errorf("unsupported resource type: %s", paasResp.Target.ModelName)
		}
		instanceID, instanceName, region, err := resourceHandler.GetInstanceInfoFromReport(paasResp.Target.Data)
		if err != nil {
			logger.CtxError(ctx, "GetInstanceInfoFromReport error: %s | data: %s", err, paasResp.Target.Data)
			return nil, fmt.Errorf("GetInstanceInfoFromReport error: %w", err)
		}

		err = utils.JsonUnmarshal([]byte(paasResp.Target.Data), modelObject)
		if err != nil {
			return nil, err
		}
		logger.CtxInfo(ctx, "modelObject: %s", utils.JsonToString(paasResp.Results))
		for _, item := range paasResp.Results {
			var diagnoseResult model.DiagnoseResultV2
			diagnoseResult.InstanceID = instanceID
			diagnoseResult.InstanceName = lo.ToPtr(instanceName)
			diagnoseResult.Region = lo.ToPtr(region)
			diagnoseResult.DiagnoseMessage = lo.ToPtr(item.Rule.Description)
			diagnoseResult.DiagnoseResultLevel = transferLevel(item.Result)
			switch item.Result {
			case "Succeeded":
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr("检测通过")
				diagnoseResult.DiagnoseOperate = lo.ToPtr("")

			case "Error":
				diagnoseResult.Status = lo.ToPtr(dal.DiagnoseResultStatusFailed)
				diagnoseResult.ErrorInfo = lo.ToPtr(item.Error)
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Detail)
			case "Warning":
				diagnoseResult.Status = lo.ToPtr(string(dal.DiagnoseResultStatusSucceed))
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Detail)
				// diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Warning.Affection)
			case "Failed":
				diagnoseResult.Status = lo.ToPtr(string(dal.DiagnoseResultStatusSucceed))
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Failed.Suggestion)
				diagnoseResult.DiagnoseOperate = lo.ToPtr(item.Failed.Reason)
			default:
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.Failed.Affection)
				diagnoseResult.DiagnoseOperate = lo.ToPtr(item.Failed.Suggestion)
			}
			diagnoseResults = append(diagnoseResults, &diagnoseResult)
		}
	default:
		logger.CtxError(ctx, "resp type error: %s", utils.JsonToString(resp))
		return nil, fmt.Errorf("invalid response type: %T", resp)
	}
	return diagnoseResults, nil
}

func (h *Handler) RunDiagnoseItem(ctx context.Context, itemID int64, resources []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (interface{}, []*common.ErrorItem, error) {
	logger := utils.NewModuleLogger("PaaSActivity-RunDiagnoseItem")
	scenario, err := common.FindParamValueByDiagnoseItemID(ctx, itemID, ScenarioKey)
	if err != nil {
		return nil, nil, fmt.Errorf("FindParamValueByDiagnoseItemID error: %s", err)
	}
	errorItems := make([]*common.ErrorItem, 0)
	host, err := common.FindHostByDiagnoseItemID(ctx, itemID)
	if err != nil {
		return nil, nil, err
	}
	// 转换ticketID为int64
	// ticketID, err := strconv.ParseInt(diagnoseInput.TicketID, 10, 64)

	if err != nil {
		return nil, nil, fmt.Errorf("invalid ticketID format: %s", diagnoseInput.TicketID)
	}
	productCategoryID := int64(0)
	createTaskIDs := make(map[int64][]string)
	for _, resource := range resources {
		if productCategoryID == 0 {
			productCategoryID = resource.ProductCategoryID
		}
		productCategory, err := dal.QueryProductCategoryByID(ctx, resource.ProductCategoryID)
		if err != nil {
			logger.CtxWarn(ctx, "QueryProductCategoryByID error: %s", err)
			continue
		}

		handler, err := getHandler(productCategory.ResourceType)
		if err != nil {
			logger.CtxWarn(ctx, "unsupported resource type: %s", productCategory.ResourceType)
			continue
		}

		listReq, err := handler.BuildListEntitiesRequest(ctx, resource.Instances)
		if err != nil {
			logger.CtxWarn(ctx, "build list request error: %s", err)
			continue
		}

		pageNumber := int32(1)
		pageSize := int32(100)
		entities, err := cmdb.GetCMDBClient().ListEntitiesWithPage(ctx, listReq, pageNumber, pageSize)
		if err != nil {
			logger.CtxError(ctx, "ListEntitiesWithPage error: %s", err)
			continue
		}

		for idx, item := range entities {
			diagnoseReq, err := handler.BuildDiagnoseRequest(ctx, scenario, diagnoseInput.TicketID, item, resource.Instances)
			if err != nil {
				logger.CtxError(ctx, "[Item%d] build diagnose request error: %s | raw data: %s", idx, err, utils.JsonToString(item))
				continue
			}

			logger.CtxInfo(ctx, "diagnoseRequestBody:", *diagnoseReq)
			createTaskResult, err := CreateDiagnoseTask(ctx, host, diagnoseReq)
			if err != nil {
				resourceID := handler.GetResourceID(item)
				logger.CtxError(ctx, "CreateDiagnoseTask error: %s | resourceID: %s", err, resourceID)
				errorItems = append(errorItems, &common.ErrorItem{
					ResourceID:     resourceID,
					DiagnoseItemID: itemID,
					Error:          err,
				})
				continue
			}
			createTaskIDs[createTaskResult.Result.Id] = []string{handler.GetResourceID(item)}
			logger.CtxInfo(ctx, "CreateDiagnoseTask response: %d", createTaskResult.Result.Id)
		}
	}

	diagnoseResultArray := make([]*paasdiagnose.PhaseResult, 0)
	doneMap := make(map[int64]bool)
	done := false

	if len(createTaskIDs) > 0 {
		for i := 0; i < 18; i++ {
			if done {
				break
			}
			time.Sleep(2 * time.Second)
			taskIDs := lo.Keys(createTaskIDs)
			listDiagnoseTasksReq := &paasdiagnose.ListDiagnoseTasksRequest{
				Ids: taskIDs,
			}

			listDiagnoseTaskResponse, err := ListDiagnoseTasks(ctx, host, listDiagnoseTasksReq)
			if err != nil {
				logger.CtxError(ctx, "ListDiagnoseTasks error: %s", err)
				continue
			}
			logger.CtxInfo(ctx, "listDiagnoseTaskResponse", listDiagnoseTaskResponse)
			for _, taskResult := range listDiagnoseTaskResponse.Result.Data {
				if doneMap[taskResult.Id] {
					continue
				}
				if taskResult.TaskStatus == "Success" {
					for _, resultItem := range taskResult.Results {
						diagnoseResultArray = append(diagnoseResultArray, &resultItem)
						doneMap[taskResult.Id] = true
					}
				}
			}
			if len(doneMap) == len(createTaskIDs) {
				done = true
				break
			}
		}
	}
	//记录查询失败的诊断项
	if !done {
		for taskID, errInstanceIDs := range createTaskIDs {
			if doneMap[taskID] {
				continue
			}
			for _, clusterID := range errInstanceIDs {
				errorItems = append(errorItems, &common.ErrorItem{
					ResourceID:     clusterID,
					DiagnoseItemID: itemID,
					Error:          fmt.Errorf("ListDiagnoseTasks takeID:%d", taskID),
				})
			}
		}
	}
	return diagnoseResultArray, errorItems, nil
}
