package paas

import (
	"context"
	"errors"
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

func CreateDiagnoseTask(ctx context.Context, host string, diagnoseRequestBody interface{}) (*paasdiagnose.CreateDiagnoseTaskResponse, error) {
	paasClient := paasdiagnose.GetPaaSClient()
	// Validate input parameters
	if err := validateDiagnoseRequest(host, diagnoseRequestBody); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	paaSRequest := &paasdiagnose.PaaSRequest{
		Action:      "CreateDiagnoseTask",
		Host:        host,
		Version:     "2024-06-20",
		RequestBody: diagnoseRequestBody,
	}

	createTaskResult, err := paasClient.DoRequest(ctx, paaSRequest)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	ret := &paasdiagnose.CreateDiagnoseTaskResponse{}
	if err := utils.JsonCopy(createTaskResult, ret); err != nil {
		return nil, fmt.Errorf("response parsing failed: %w", err)
	}
	return ret, nil
}

func ListDiagnoseTasks(ctx context.Context, host string, listDiagnoseTasksReq *paasdiagnose.ListDiagnoseTasksRequest) (*paasdiagnose.ListDiagnoseTasksResponse, error) {
	paasClient := paasdiagnose.GetPaaSClient()
	// Validate input parameters

	paaSRequest := &paasdiagnose.PaaSRequest{
		Action:      "ListDiagnoseTasks",
		Host:        host,
		Version:     "2024-06-20",
		RequestBody: listDiagnoseTasksReq,
	}

	createTaskResult, err := paasClient.DoRequest(ctx, paaSRequest)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	ret := &paasdiagnose.ListDiagnoseTasksResponse{}
	if err := utils.JsonCopy(createTaskResult, ret); err != nil {
		return nil, fmt.Errorf("response parsing failed: %w", err)
	}
	return ret, nil
}
func validateDiagnoseRequest(host string, body interface{}) error {
	if host == "" {
		return errors.New("host cannot be empty")
	}
	if body == nil {
		return errors.New("request body cannot be nil")
	}

	return nil
}
func transferLevel(status string) string {
	switch status {
	case "Succeeded":
		return diagnose_task_run.DiagnoseResultLevelInfo
	case "Warning":
		return diagnose_task_run.DiagnoseResultLevelWarning
	case "Error":
		return diagnose_task_run.DiagnoseResultLevelFailed // paas平台 执行失败
	case "Failed":
		return diagnose_task_run.DiagnoseResultLevelError // paas平台 规则级别不合理，需要
	default:
		return diagnose_task_run.DiagnoseResultLevelFailed
	}
}
