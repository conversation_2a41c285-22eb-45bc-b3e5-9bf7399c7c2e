package paas

import (
	"context"
	"testing"

	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

type CreateDiagnoseTaskReq struct {
	TargetType string   `json:"TargetType"`
	Region     string   `json:"Region"`
	Identities []string `json:"Identities"`
	Scenario   string   `json:"Scenario"`
	OrderId    string   `json:"OrderId"`
}

func TestCreateDiagnoseTask(t *testing.T) {
	paasClient := paasdiagnose.GetPaaSClient()
	createDiagnoseTaskReq := &CreateDiagnoseTaskReq{
		TargetType: "VkeInstance",
		Region:     "cn-guilin-boe",
		Identities: []string{"ccla8vd9nfllvljfuaop0"},
		// Identities: []string{
		// 	"cctnc1miol6uqt9ri5no0",
		// 	"ccdge6lnqtofrdbff0ccg"},
		Scenario: "ALL",
		OrderId:  "123",
	}
	req := &paasdiagnose.PaaSRequest{
		Action:      "CreateDiagnoseTask",
		Host:        "https://infra-ops-top.byted.org",
		Version:     "2024-06-20",
		RequestBody: createDiagnoseTaskReq,
	}
	data, err := paasClient.DoRequest(context.Background(), req)
	if err != nil {
		t.Errorf("DoRequest error: %s", err)
	}
	t.Logf("DoRequest response: %s", utils.JsonToString(data))
	// ctx := context.Background()

	// // Setup test cases
	// tests := []struct {
	// 	name    string
	// 	host    string
	// 	version string
	// 	wantErr bool
	// }{
	// 	{
	// 		name:    "successful creation",
	// 		host:    "test-host",
	// 		version: "2024-06-20",
	// 		wantErr: false,
	// 	},
	// 	{
	// 		name:    "invalid version format",
	// 		host:    "test-host",
	// 		version: "invalid",
	// 		wantErr: true,
	// 	},
	// }

	// for _, tt := range tests {
	// 	t.Run(tt.name, func(t *testing.T) {
	// 		req := &PaaSRequest{
	// 			Action:      "CreateDiagnoseTask",
	// 			Host:        tt.host,
	// 			Version:     tt.version,
	// 			RequestBody: &DiagnoseRequestBody{},
	// 		}

	// 		// Mock the paasClient.DoRequest method
	// 		mockDoRequest := func(ctx context.Context, req *PaaSRequest) (string, error) {
	// 			if tt.wantErr {
	// 				return "", errors.New("test error")
	// 			}
	// 			return "test-task-id", nil
	// 		}

	// 		paasTaskID, err := mockDoRequest(ctx, req)

	// 		if tt.wantErr {
	// 			assert.Error(t, err)
	// 		} else {
	// 			assert.NoError(t, err)
	// 			assert.Equal(t, "test-task-id", paasTaskID)
	// 		}
	// 	})
	// }
}

// func TestValidateVersion(t *testing.T) {
// 	tests := []struct {
// 		name    string
// 		version string
// 		wantErr bool
// 	}{
// 		{
// 			name:    "valid version",
// 			version: "2024-06-20",
// 			wantErr: false,
// 		},
// 		{
// 			name:    "invalid version format",
// 			version: "invalid",
// 			wantErr: true,
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			err := ValidateVersion(tt.version)

// 			if tt.wantErr {
// 				assert.Error(t, err)
// 			} else {
// 				assert.NoError(t, err)
// 			}
// 		})
// 	}
// }
