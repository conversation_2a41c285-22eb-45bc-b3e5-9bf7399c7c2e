package linkPaasToEcs

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"github.com/samber/lo"
	"strings"
)

const ecsResourceType = "ecsID"

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetName() string {
	return common.LinkPaasToEcsActivityName
}

func (h *Handler) TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error) {
	logger := utils.NewModuleLogger("PaaSActivity-TransferRespToResults")
	logger.CtxInfo(ctx, "resp: %s", utils.JsonToString(resp))
	return nil, nil
}

func (h *Handler) RunDiagnoseItem(ctx context.Context, itemID int64, resources []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (interface{}, []*common.ErrorItem, error) {
	logger := utils.NewModuleLogger("PaaSActivity-RunDiagnoseItem")

	//查找ecsResourceType对应的ProductCategoryID
	ecsProductCategoryID, err := dal.QueryProductCategoryIDByResourceType(ctx, ecsResourceType)
	if err != nil {
		logger.CtxError(ctx, "QueryProductCategoryIDByResourceType error: %s", err)
		return nil, nil, err
	}

	//1.从diagnoseItemIDs过滤出是ecs的诊断项，
	ecsItems, err := dal.FilterDiagnoseItemsByProductCategoryID(ctx, ecsProductCategoryID, diagnoseInput.DiagnoseItemIDs)
	if err != nil {
		logger.CtxError(ctx, "FilterDiagnoseItemsByProductCategoryID error: %s", err)
		return nil, nil, err
	}

	productCategoryID := int64(0)
	// 从cmdb 查询资源并获取每个资源的region信息
	resourceFilters := map[string][]cmdb.FieldFilter{}
	for _, resources := range diagnoseInput.Resources {
		for _, resource := range resources {
			if productCategoryID == 0 {
				productCategoryID = resource.ProductCategoryID
			}
			productCategory, err := dal.QueryProductCategoryByID(ctx, resource.ProductCategoryID)
			if err != nil {
				logger.CtxWarn(ctx, "QueryProductCategoryByID error: %s", err)
				continue
			}

			ins := make([]cmdb.FieldFilter, 10)
			switch productCategory.ResourceType {

			case "VkePod":
				ins = lo.FilterMap(resource.Instances, func(item string, index int) (cmdb.FieldFilter, bool) {
					args := strings.Split(item, ":")
					if len(args) != 3 {
						logger.CtxWarn(ctx, "VkePod args error: %s", item)
						return nil, false
					}
					clusterID := args[0]
					namespace := args[1]
					podName := args[2]
					return []cmdb.FieldFilterItem{
						{
							Field: "spec.clusterId",
							Value: clusterID,
						},
						{
							Field: "spec.namespace",
							Value: namespace,
						},
						{
							Field: "metadata.name",
							Value: podName,
						},
					}, true
				})
			case "VkeNode":
				ins = lo.FilterMap(resource.Instances, func(machineID string, index int) (cmdb.FieldFilter, bool) {
					return []cmdb.FieldFilterItem{
						{
							Field: "spec.nodeId",
							Value: machineID,
						},
					}, true
				})
			}
			logger.CtxInfo(ctx, "ins: %s\n", utils.JsonToString(ins))

			if _, ok := resourceFilters[productCategory.ResourceType]; !ok {
				resourceFilters[productCategory.ResourceType] = ins
			} else {
				resourceFilters[productCategory.ResourceType] = append(resourceFilters[productCategory.ResourceType], ins...)
			}
		}
	}

	ecsInstances := make([]string, 0)

	// 实例列表信息 interface{} 类型, 在执行诊断的时候根据ResourceType进行转换
	for resourceType, filters := range resourceFilters {
		allItems := []interface{}{}
		pageNumber := int32(1)
		pageSize := int32(100)
		for {
			listEntitiesRequest := &cmdb.ListEntitiesRequest{
				PageNumber: pageNumber,
				PageSize:   pageSize,
				Kind:       resourceType,
				Filter: cmdb.ListEntitiesFilter{
					FieldFilters: filters,
				},
			}
			listEntitiesResponse, err := cmdb.GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
			if err != nil {
				logger.CtxWarn(ctx, "ListEntities error: %s", err)
				break
			}
			allItems = append(allItems, listEntitiesResponse.Items...)

			if int32(len(listEntitiesResponse.Items)) < pageSize {
				logger.CtxWarn(ctx, "ListEntities response is empty")
				break
			}
			pageNumber++
		}

		//创建任务,根据不同的资源类型进行request 请求体转换

		logger.CtxInfo(ctx, "CMDB查询参数: Kind=%s, Filters=%s", resourceType, utils.JsonToString(filters))

		for _, item := range allItems {

			switch resourceType {

			case "VkePod":
				vkePod := &cmdb.VkePod{}
				if err := utils.JsonCopy(item, vkePod); err != nil {
					logger.CtxError(ctx, "vkePodInfo JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(item))
					return nil, nil, err
				}
				ecsInstances = append(ecsInstances, vkePod.Spec.Node.Spec.InstanceId)
			case "VkeNode":
				vkeNode := &cmdb.VkeNode{}
				if err := utils.JsonCopy(item, vkeNode); err != nil {
					logger.CtxError(ctx, "vkeNodeInfo JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(item))
					return nil, nil, err
				}
				ecsInstances = append(ecsInstances, vkeNode.Spec.InstanceId)
			}

		}

	}
	ecsInstances = utils.RemoveDuplicates(ecsInstances)
	for _, item := range ecsItems {
		if resources, ok := diagnoseInput.Resources[item.DiagnoseItemID]; ok && len(resources) != 0 {

			resources[0].Instances = append(resources[0].Instances, ecsInstances...)

		} else {
			diagnoseInput.Resources[item.DiagnoseItemID] = []*diagnose_result.DiagnoseResource{
				{
					ProductCategoryID: item.ProductCategoryID,
					Instances:         ecsInstances,
				},
			}
		}
	}
	return diagnoseInput, nil, nil
}
