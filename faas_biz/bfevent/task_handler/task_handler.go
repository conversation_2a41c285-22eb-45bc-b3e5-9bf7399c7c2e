package task_handler

import (
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/linkPaasToEcs"
	"context"
	"fmt"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/ecs"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/network"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/paas"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	"github.com/samber/lo"

	"reflect"

	"code.byted.org/cicd/byteflow/pkg/worker/byteflow"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/bytedance/gopkg/util/logger"
)

const concurrencyLimit = 10

type Handler interface {
	GetName() string
	TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error)
	RunDiagnoseItem(ctx context.Context, itemID int64, resource []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (respArray interface{}, errorItems []*common.ErrorItem, err error)
}
type HandlerFactory struct {
	handlers sync.Map
}

var factory *HandlerFactory

var once sync.Once

func init() {
	once.Do(func() {
		factory = &HandlerFactory{
			handlers: sync.Map{},
		}
	})
	//注册Activity处理器
	factory.RegisterHandler(ecs.NewHandler())
	factory.RegisterHandler(paas.NewHandler())
	factory.RegisterHandler(network.NewHandler())
	factory.RegisterHandler(linkPaasToEcs.NewHandler())
}

// RegisterHandler 注册资源处理器
func (f *HandlerFactory) RegisterHandler(handler Handler) {
	f.handlers.Store(handler.GetName(), handler) // Handler activityName作为 key 和 value 方便查找
	logger.Infof("register  handler: %v", handler)
}

// GetHandler 获取资源处理器
func (f *HandlerFactory) GetHandler(name string) (Handler, error) {
	var foundHandler Handler
	if h, ok := f.handlers.Load(name); ok {
		foundHandler = h.(Handler)
		return foundHandler, nil
	}
	return nil, fmt.Errorf("%s handler not found", name)
}

func LoadActivity() map[string]byteflow.TaskHandler {
	activities := make(map[string]byteflow.TaskHandler)
	factory.handlers.Range(func(key, value interface{}) bool {
		activityName := key.(string)
		if activityName[0:4] == "link" {
			activities[activityName] = func(tp *byteflow.TaskParameter) *byteflow.TaskEvent {
				return linkActivityProcessFunc(tp, activityName)
			}
		} else {
			activities[activityName] = func(tp *byteflow.TaskParameter) *byteflow.TaskEvent {
				return activityProcessFunc(tp, activityName)
			}
		}
		return true
	})
	return activities
}

func activityProcessFunc(tp *byteflow.TaskParameter, activityName string) *byteflow.TaskEvent {
	logger := utils.NewModuleLogger(fmt.Sprintf("%s-activityProcessFunc", activityName))
	ctx := tp.GoCtx()
	diagnoseInput := &statemachines.DiagnoseRequest{}
	err := utils.JsonCopy(tp.Input, diagnoseInput)
	if err != nil {
		logger.CtxError(ctx, "json copy error: %s", err)
		err = FailedTaskStatus(ctx, diagnoseInput.DiagnoseTaskRunID)
		if err != nil {
			logger.CtxError(ctx, "FailedTaskStatus error: %s", err)
			return byteflow.FailureEvent("FailedTaskStatus", err.Error())
		}
		return byteflow.FailureEvent("InvalidInput", err.Error())
	}
	logger.CtxInfo(ctx, "[worker] %s ,%s diagnoseInput:%+v\n", activityName, tp.TaskContext.State.Name, diagnoseInput)
	logger.CtxInfo(ctx, "[worker] %s  taskID: %s, executionID: %s", activityName, tp.ID, tp.ExecutionID)

	//通过state语言查找执行的diagnoseItem
	diagnoseItemName := tp.TaskContext.State.Name
	diagnoseItem, err := dal.FindDiagnoseItemByName(ctx, diagnoseItemName)
	if err != nil {
		logger.CtxError(ctx, "FindDiagnoseItemByName error: %s", err)
		err = FailedTaskStatus(ctx, diagnoseInput.DiagnoseTaskRunID)
		if err != nil {
			logger.CtxError(ctx, "FailedTaskStatus error: %s", err)
			return byteflow.FailureEvent("FailedTaskStatus", err.Error())
		}
		return byteflow.FailureEvent("FindDiagnoseItemByName", fmt.Sprintf("diagnoseItem %s is not exist", diagnoseItemName))
	}

	// 更新诊断任务状态
	err = PrepareDiagnoseTask(ctx, diagnoseInput, diagnoseItem.DiagnoseItemID)
	if err != nil {
		logger.CtxError(ctx, "PrepareDiagnoseTask error: %s", err)
		return processRunError(ctx, diagnoseInput, err, diagnoseItem.DiagnoseItemID)
	}
	// 获取activity处理器
	taskHandler, err := factory.GetHandler(activityName)
	if err != nil {
		logger.CtxError(ctx, "GetHandler error: %s", err)
		return processRunError(ctx, diagnoseInput, err, diagnoseItem.DiagnoseItemID)
	}

	//先判断查找到的diagnoseItem是否在用户绑定的DiagnoseItemIDs里面存在
	isExist := false
	for _, v := range diagnoseInput.DiagnoseItemIDs {
		if diagnoseItem.DiagnoseItemID == v {
			isExist = true
			break
		}
	}
	if !isExist {
		logger.CtxError(ctx, "diagnoseInput.DiagnoseItemIDs没有找到诊断项：%d", diagnoseItem.DiagnoseItemID)
		return processRunError(ctx, diagnoseInput, fmt.Errorf("diagnoseItem %s is not exist", diagnoseItemName), diagnoseItem.DiagnoseItemID)
	}
	// 执行诊断任务
	itemID := diagnoseItem.DiagnoseItemID
	diagnoseResults, err := GetDiagnoseItemsResult(ctx, diagnoseInput, itemID, diagnoseInput.Resources[itemID], taskHandler)
	if err != nil {
		logger.CtxError(ctx, "RunDiagnoseItem error: %s", err)
		return processRunError(ctx, diagnoseInput, err, diagnoseItem.DiagnoseItemID)
	}
	//记录诊断结果
	if len(diagnoseResults) > 0 {
		err = dal.DiagnoseResultsCreate(ctx, diagnoseResults)
		if err != nil {
			logger.CtxError(ctx, "DiagnoseResultsCreate error: %s", err)
			return byteflow.FailureEvent("DiagnoseResultsCreate", err.Error())
		}
	}
	err = AfterTaskEnd(ctx, diagnoseInput, diagnoseItem.DiagnoseItemID)
	if err != nil {
		logger.CtxError(ctx, "AfterTaskEnd error: %s", err)
		return byteflow.FailureEvent("AfterTaskEnd Error", err.Error())
	}
	return byteflow.SuccessEvent(diagnoseResults, map[string]interface{}{
		"GlobalInput": tp.Input,
	})
}

func linkActivityProcessFunc(tp *byteflow.TaskParameter, activityName string) *byteflow.TaskEvent {
	logger := utils.NewModuleLogger(fmt.Sprintf("%s-", activityName))
	ctx := tp.GoCtx()
	diagnoseInput := &statemachines.DiagnoseRequest{}
	err := utils.JsonCopy(tp.TaskContext.Store["GlobalInput"], diagnoseInput)
	if err != nil {
		logger.CtxError(ctx, "json copy error: %s", err)
		return byteflow.FailureEvent("InvalidInput", err.Error())
	}
	logger.CtxInfo(ctx, "[worker] %s ,%s diagnoseInput:%+v\n", activityName, tp.TaskContext.State.Name, diagnoseInput)
	logger.CtxInfo(ctx, "[worker] %s  taskID: %s, executionID: %s", activityName, tp.ID, tp.ExecutionID)

	// 获取activity处理器
	taskHandler, err := factory.GetHandler(activityName)
	if err != nil {
		logger.CtxError(ctx, "GetHandler error: %s", err)
		return byteflow.FailureEvent("GetHandler Error", err.Error())
	}

	// 执行资源获取的函数
	diagnoseResults, _, err := taskHandler.RunDiagnoseItem(ctx, 0, nil, diagnoseInput)
	if err != nil {
		logger.CtxError(ctx, "RunDiagnoseItem error: %s", err)
		return byteflow.FailureEvent("RunDiagnoseItem Error", err.Error())
	}

	//将查询到的结果填充到diagnoseInput.Resources里面
	if diagnoseResults != nil {
		tp.Input = diagnoseResults
	}
	return byteflow.SuccessEvent(diagnoseResults, nil)
}

func GetDiagnoseItemsResult(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, itemID int64, resources []*diagnose_result.DiagnoseResource, taskHandler Handler) ([]*model.DiagnoseResultV2, error) {
	results, errorItems, err := taskHandler.RunDiagnoseItem(ctx, itemID, resources, diagnoseInput)
	if err != nil {
		return nil, err
	}
	// 填充诊断结果的其他字段
	var productCategoryId int64
	for _, r := range resources {
		if productCategoryId == 0 {
			productCategoryId = r.ProductCategoryID
		} else {
			break
		}
	}
	diagnoseResults, err := transferResp(ctx, results, itemID, productCategoryId, diagnoseInput, taskHandler)
	if len(errorItems) > 0 {
		processedResults, err := processErrorItem(errorItems, itemID, productCategoryId, diagnoseInput)
		if err != nil {
			return nil, err
		}
		diagnoseResults = append(diagnoseResults, processedResults...)
	}
	return diagnoseResults, nil
}

func GetDiagnoseItemsResultByCon(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, taskHandler Handler) ([]*model.DiagnoseResultV2, error) {
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	conChan := make(chan struct{}, concurrencyLimit)
	errChan := make(chan error)
	// 创建一个可取消的上下文
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	for _, itemID := range diagnoseInput.DiagnoseItemIDs {
		select {
		case <-ctx.Done():
			break
		default:
			wg.Add(1)
			go func(diagnoseItemId int64) {
				conChan <- struct{}{}
				defer func() {
					<-conChan
					wg.Done()
				}()
				// 检查上下文是否已经取消
				select {
				case <-ctx.Done():
					return
				default:
				}

				results, errorItems, err := taskHandler.RunDiagnoseItem(ctx, diagnoseItemId, diagnoseInput.Resources[diagnoseItemId], diagnoseInput)
				if err != nil {
					errChan <- err
					cancel() // 取消上下文，停止其他 goroutine
					return
				}
				// 填充诊断结果的其他字段
				var productCategoryId int64
				for _, resources := range diagnoseInput.Resources[diagnoseItemId] {
					if productCategoryId == 0 {
						productCategoryId = resources.ProductCategoryID
					} else {
						break
					}
				}
				dResult, err := transferResp(ctx, results, diagnoseItemId, productCategoryId, diagnoseInput, taskHandler)
				if err != nil {
					errChan <- err
					cancel() // 取消上下文，停止其他 goroutine
					return
				}
				if len(errorItems) > 0 {
					processedResults, err := processErrorItem(errorItems, diagnoseItemId, productCategoryId, diagnoseInput)
					if err != nil {
						errChan <- err
						cancel() // 取消上下文，停止其他 goroutine
						return
					}
					dResult = append(dResult, processedResults...)
				}

				mu.Lock()
				diagnoseResults = append(diagnoseResults, dResult...)
				mu.Unlock()
			}(itemID)
		}
	}

	// 等待所有 goroutine 完成
	go func() {
		wg.Wait()
		close(errChan) // 所有 goroutine 完成后，关闭错误通道
	}()

	// 检查是否有错误
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	return diagnoseResults, nil
}

func transferResp(ctx context.Context, resp interface{}, diagnoseItemId, productCategoryId int64, diagnoseInput *statemachines.DiagnoseRequest, taskHandler Handler) ([]*model.DiagnoseResultV2, error) {
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	// 使用 reflect 包检查 resp 的类型
	respValue := reflect.ValueOf(resp)
	//数组类型
	if respValue.Kind() == reflect.Slice {
		for i := 0; i < respValue.Len(); i++ {
			item := respValue.Index(i).Interface()
			result, err := taskHandler.TransferRespToResults(ctx, item)
			if err != nil {
				return nil, err
			}
			diagnoseResults = append(diagnoseResults, result...)
		}
	} else {
		diagnoseResult, err := taskHandler.TransferRespToResults(ctx, resp)
		if err != nil {
			return nil, err
		}
		diagnoseResults = append(diagnoseResults, diagnoseResult...)
	}
	//填充诊断结果的其他字段
	for i, result := range diagnoseResults {
		diagnoseResults[i].DiagnoseItemID = diagnoseItemId
		diagnoseResults[i].DiagnoseTaskRunID = diagnoseInput.DiagnoseTaskRunID
		diagnoseResults[i].DiagnoseResultLevelNum = lo.ToPtr(diagnose_task_run.GetDiagnoseResultLevelNum(result.DiagnoseResultLevel))
		diagnoseResults[i].Status = lo.ToPtr(dal.DiagnoseResultStatusSucceed)
		diagnoseResults[i].ProductCategoryID = productCategoryId
	}
	return diagnoseResults, nil
}

func processErrorItem(errorItems []*common.ErrorItem, itemID, productCategoryID int64, diagnoseInput *statemachines.DiagnoseRequest) ([]*model.DiagnoseResultV2, error) {
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	for _, errorItem := range errorItems {
		diagnoseResult := &model.DiagnoseResultV2{
			DiagnoseItemID:         itemID,
			InstanceID:             errorItem.ResourceID,
			InstanceName:           lo.ToPtr(errorItem.ResourceID),
			DiagnoseResultLevel:    diagnose_task_run.DiagnoseResultLevelFailed,
			DiagnoseResultLevelNum: lo.ToPtr(diagnose_task_run.DiagnoseResultLevelFailedNum),
			Status:                 lo.ToPtr(dal.DiagnoseResultStatusFailed),
			ErrorInfo:              lo.ToPtr(errorItem.Error.Error()),
			DiagnoseTaskRunID:      diagnoseInput.DiagnoseTaskRunID,
			ProductCategoryID:      productCategoryID,
		}
		diagnoseResults = append(diagnoseResults, diagnoseResult)
	}
	return diagnoseResults, nil
}

func addErrorItem(diagnoseInput *statemachines.DiagnoseRequest, err error) ([]*model.DiagnoseResultV2, error) {
	errorItems := make([]*common.ErrorItem, 0)
	productCategoryID := int64(0)
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	for _, itemID := range diagnoseInput.DiagnoseItemIDs {
		for _, resource := range diagnoseInput.Resources[itemID] {
			for _, instance := range resource.Instances {
				errorItems = append(errorItems, &common.ErrorItem{
					DiagnoseItemID: itemID,
					ResourceID:     instance,
					Error:          fmt.Errorf("diagnose task run failed,err: %v", err),
				})
			}
			if productCategoryID == 0 {
				productCategoryID = resource.ProductCategoryID
			}
		}
		results, err := processErrorItem(errorItems, itemID, productCategoryID, diagnoseInput)
		if err != nil {
			return nil, err
		}
		diagnoseResults = append(diagnoseResults, results...)
	}
	return diagnoseResults, nil
}

func processRunError(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, err error, itemID int64) *byteflow.TaskEvent {
	diagnoseResults, err := addErrorItem(diagnoseInput, err)
	if err != nil {
		return byteflow.FailureEvent("addErrorItem", err.Error())
	}
	err = dal.DiagnoseResultsCreate(ctx, diagnoseResults)
	if err != nil {
		return byteflow.FailureEvent("DiagnoseResultsCreate", err.Error())
	}
	err = AfterTaskEnd(ctx, diagnoseInput, itemID)
	if err != nil {
		return byteflow.FailureEvent("AfterTaskEnd Error", err.Error())
	}
	return byteflow.SuccessEvent(diagnoseResults, nil)
}
