package task_handler

import (
	"fmt"

	taskRunSrvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"github.com/samber/lo"

	"context"
	"time"

	"code.byted.org/cicd/byteflow/pkg/client"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

func handleRequestError(ctx context.Context, err error) {
	if client.IsRequestRetryable(err) {
		// 该错误是由于网络异常等原因导致的，可以重试，业务端可以根据该错误重新发起请求。本示例不做处理
		logs.CtxInfo(ctx, "can retry")
		return
	}

	if client.IsServiceError(err) {
		// 该错误是由ByteFlow服务端错误引起的，可以Oncall处理
		logs.CtxError(ctx, "is byteflow service error")
		return
	}

	if client.IsRequestInvalid(err) {
		// 不合理的请求导致的错误，需检查业务逻辑
		logs.CtxError(ctx, "request is invalid")
		return
	}
}

// updateDiagnoseItemStatusStart 更新诊断项的状态和开始时间
func updateDiagnoseItemStatusStart(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, itemID int64) error {
	startTime, err := utils.TimeToString(time.Now(), "")
	if err != nil {
		return err
	}
	diagnoseItemStatuses := make([]*diagnose_task_run.DiagnoseItemStatus, 0)
	diagnoseItemStatuses = append(diagnoseItemStatuses, &diagnose_task_run.DiagnoseItemStatus{
		DiagnoseItem: &diagnose_task_v2.DiagnoseItem{Id: itemID},
		Status:       taskRunSrvc.DiagnoseTaskStatusRunning,
		StartTime:    &startTime,
	})

	err = dal.UpdateDiagnoseTaskRunItemStatusInBatches(ctx, diagnoseInput.DiagnoseTaskRunID, diagnoseItemStatuses)
	if err != nil {
		return err
	}
	return nil
}

// updateDiagnoseItemStatusEnd 更新诊断项的状态和结束时间 todo:添加level字段
func updateDiagnoseItemStatusEnd(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, itemID int64) error {
	endTime, err := utils.TimeToString(time.Now(), "")
	if err != nil {
		return err
	}
	diagnoseItemStatuses := make([]*diagnose_task_run.DiagnoseItemStatus, 0)

	diagnoseItemStatuses = append(diagnoseItemStatuses, &diagnose_task_run.DiagnoseItemStatus{
		DiagnoseItem: &diagnose_task_v2.DiagnoseItem{Id: itemID},
		Status:       taskRunSrvc.DiagnoseItemStatusFinish,
		EndTime:      &endTime,
	})

	err = dal.UpdateDiagnoseTaskRunItemStatusInBatches(ctx, diagnoseInput.DiagnoseTaskRunID, diagnoseItemStatuses)
	if err != nil {
		return err
	}
	return nil
}

// updateDiagnoseTaskRunStatus 更新诊断任务运行状态
func updateDiagnoseTaskRunStatus(ctx context.Context, taskRunID int64, taskRunStatus *string) error {
	err := dal.UpdateDiagnoseTaskRunStatus(ctx, taskRunID, taskRunStatus)
	if err != nil {
		return err
	}
	return nil
}

// updateDiagnoseTaskEndTimeAndStatus 更新诊断任务结束时间和状态
func updateDiagnoseTaskEndTimeAndStatus(ctx context.Context, diagnoseTaskRunID int64) error {
	//查询taskRun
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, diagnoseTaskRunID)
	if err != nil {
		return err
	}
	diagnoseItemStatus, err := taskRunSrvc.TransferStringToDiagnoseItemStatus(taskRun.DiagnoseItemStatus)
	if err != nil {
		return err
	}
	//如果有未完成的诊断项，则不更新任务结束时间和状态
	for _, diagnoseItem := range diagnoseItemStatus {
		if diagnoseItem.Status == taskRunSrvc.DiagnoseItemStatusRunning || diagnoseItem.Status == taskRunSrvc.DiagnoseItemStatusWaiting {
			return nil
		}
	}
	return dal.UpdateDiagnoseTaskRunEndTimeStatus(ctx, diagnoseTaskRunID, time.Now(), lo.ToPtr(taskRunSrvc.DiagnoseTaskStatusFinish))
}

// PrepareDiagnoseTask 任务开始前状态的更新和判断
func PrepareDiagnoseTask(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, itemID int64) error {
	// 先判断当前Activity下是否有diagnoseItem
	if len(diagnoseInput.DiagnoseItemIDs) == 0 {
		return fmt.Errorf("diagnoseItemIDs is empty")
	}

	// 更新任务状态
	err := updateDiagnoseTaskRunStatus(ctx, diagnoseInput.DiagnoseTaskRunID, lo.ToPtr(taskRunSrvc.DiagnoseTaskStatusRunning))
	if err != nil {
		return fmt.Errorf("updateDiagnoseTaskRunStatus error: %s", err.Error())
	}

	// 更新诊断项状态
	err = updateDiagnoseItemStatusStart(ctx, diagnoseInput, itemID)
	if err != nil {
		return fmt.Errorf("updateDiagnoseItemStatusStart error: %s", err.Error())
	}
	return nil
}

// AfterTaskEnd 任务结束后更新诊断项状态和任务状态
func AfterTaskEnd(ctx context.Context, diagnoseInput *statemachines.DiagnoseRequest, itemID int64) error {
	// 更新诊断项状态
	err := updateDiagnoseItemStatusEnd(ctx, diagnoseInput, itemID)
	if err != nil {
		return fmt.Errorf("updateDiagnoseItemStatusEnd error: %w", err)
	}
	// 更新任务状态
	err = updateDiagnoseTaskEndTimeAndStatus(ctx, diagnoseInput.DiagnoseTaskRunID)
	if err != nil {
		return fmt.Errorf("updateDiagnoseTaskEndTimeAndStatus error: %w", err)
	}
	return nil
}

func FailedTaskStatus(ctx context.Context, taskRunID int64) error {
	// 更新任务状态
	err := updateDiagnoseTaskRunStatus(ctx, taskRunID, lo.ToPtr(taskRunSrvc.DiagnoseTaskStatusFailed))
	if err != nil {
		return fmt.Errorf("updateDiagnoseTaskRunStatus error: %s", err.Error())
	}
	return nil
}
