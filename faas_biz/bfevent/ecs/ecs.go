package ecs

import (
	"bytes"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"net/url"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"github.com/pkg/errors"
)

const (
	//BOE 域名：
	//	https://cloud-boe.bytedance.net/auth/api/v1/jwt
	//	https://paas-gw-boe.byted.org/api/v1/awacs/v1/diag/diagnosis_task/run
	//	https://paas-gw-boe.byted.org/api/v1/awacs/v1/diag/diagnosis_task/detail
	//ecsGetTokenUrl              = "https://cloud.bytedance.net/auth/api/v1/jwt"
	//ecsCreateDiagnoseMissionUrl = "https://paas-gw.byted.org/api/v1/awacs/v1/diag/diagnosis_task/run"
	//ecsQueryDiagnoseDetail      = "https://paas-gw.byted.org/api/v1/awacs/v1/diag/diagnosis_task/detail"
	ecsCreateDiagnoseMissionUrl = "api/v1/awacs/v1/diag/diagnosis_task/run"
	ecsQueryDiagnoseDetailUrl   = "api/v1/awacs/v1/diag/diagnosis_task/detail"
	paramGroupIDKey             = "diag_group_id"
	defaultEmail                = "<EMAIL>"
)

func getEcsToken() (string, error) {
	logger := utils.NewModuleLogger("getEcsToken")
	ctx := context.Background()
	cfg := tcc.GetServiceConfig()
	if cfg == nil {
		return "", errors.New("tcc config is nil")
	}
	if cfg.EcsWorkerConfig == nil {
		return "", errors.New("tcc config EcsWorkerConfig is nil")
	}
	// 创建 HTTP 请求
	req, err := http.NewRequest("GET", cfg.EcsWorkerConfig.GetTokenUrl, nil)
	if err != nil {
		logger.CtxError(ctx, "new request error: %s", err)
		return "", err
	}
	// 设置请求头
	authorization := "Bearer " + cfg.EcsWorkerConfig.TokenID
	req.Header.Set("Authorization", authorization)

	// 创建 HTTP 客户端
	client := &http.Client{}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logger.CtxError(ctx, "send request error: %s", err)
		return "", err
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		logger.CtxError(ctx, "request failed with status code: %d", resp.StatusCode)
		return "", errors.Errorf("request ecs get token failed with status code: %d", resp.StatusCode)
	}

	// 获取响应头中的 x-jwt-token
	xJwtToken := resp.Header.Get("x-jwt-token")
	if xJwtToken == "" {
		logger.CtxError(ctx, "x-jwt-token not found in response headers")
		return "", errors.New("x-jwt-token not found in response headers")
	}

	// 返回 x-jwt-token 给调用方
	return xJwtToken, nil
}

// 自定义函数用于动态生成 requestParams
func createRequestParams(extra, resourceId, resourceType string, startTime int64, endTime int64) (string, error) {

	params := RequestParams{
		Extra:        extra,
		ResourceId:   resourceId,
		ResourceType: resourceType,
		BeginTime:    utils.ConvertToMilliseconds(startTime),
		EndTime:      utils.ConvertToMilliseconds(endTime),
	}
	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", errors.Errorf("failed to marshal requestParams: %v", err)
	}
	return string(jsonData), nil
}

func findTaskRunUserID(ctx context.Context, DiagnoseTaskRunID int64) string {
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, DiagnoseTaskRunID)
	if diagnoseTaskRun == nil || diagnoseTaskRun.CreateUserID == nil || err != nil {
		return defaultEmail
	}
	return *diagnoseTaskRun.CreateUserID
}

// createEcsDiagnoseMission 创建 ECS 诊断任务
func createEcsDiagnoseMission(host, token, diagGroupID, resourceID, resourceType, operator string, startTime int64, endTime int64) (string, error) {
	// 创建multipart表单数据
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	logger := utils.NewModuleLogger("createEcsDiagnoseMission")
	ctx := context.Background()
	logger.CtxInfo(ctx, "createEcsDiagnoseMission host:%s,token:%s,diagGroupID:%s,resourceID:%s,resourceType:%s,operator:%s",
		host, token, diagGroupID, resourceID, resourceType, operator)

	// 添加diag_group_id字段
	if err := writer.WriteField("diag_group_id", diagGroupID); err != nil {
		return "", err
	}

	// 添加request_params字段
	requestParams, err := createRequestParams("", resourceID, resourceType, startTime, endTime)
	if err != nil {
		return "", err
	}
	if err = writer.WriteField("request_params", requestParams); err != nil {
		return "", err
	}

	// 添加operator字段
	if err = writer.WriteField("operator", operator); err != nil {
		return "", err
	}

	// 关闭writer以完成请求体
	if err = writer.Close(); err != nil {
		return "", err
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", fmt.Sprintf("%s/%s", host, ecsCreateDiagnoseMissionUrl), body)
	if err != nil {
		logger.CtxError(ctx, "create new request failed,err:%v", err)
		return "", err
	}

	// 添加请求头
	req.Header.Set("X-JWT-Token", token)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.CtxError(ctx, "send request failed,err:%v", err)
		return "", err
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.CtxError(ctx, "read response body failed,err:%v", err)
		return "", err
	}
	var response RunDiagResp
	err = json.Unmarshal(respBody, &response)
	if err != nil {
		logger.CtxError(ctx, "unmarshal response body failed,err:%v,resp body:%v", err, string(respBody))
		return "", err
	}
	// 打印响应
	if response.Code == 0 {
		return response.Data, nil
	}
	return "", errors.Errorf("runDiagMission failed,code:%v", response.Code)
}

func queryEcsDiagnoseMission(host, token, diagTaskID string) (*Response, error) {
	params := url.Values{}
	params.Add("diag_task_id", diagTaskID)
	queryUrl := fmt.Sprintf("%s/%s?%s", host, ecsQueryDiagnoseDetailUrl, params.Encode())
	logger := utils.NewModuleLogger("queryEcsDiagnoseMission")
	ctx := context.Background()
	logger.CtxInfo(ctx, "queryEcsDiagnoseMission host:%s,token:%s,diagTaskID:%s", host, token, diagTaskID)

	req, err := http.NewRequest("GET", queryUrl, nil)
	if err != nil {
		return nil, err
	}
	// 设置 Authorization 请求头
	req.Header.Set("X-JWT-Token", token)

	// 创建 HTTP 客户端并发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.CtxError(ctx, "send request failed,err:%v", err)
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	var response Response
	err = json.Unmarshal(body, &response)
	if err != nil {
		logger.CtxError(ctx, "unmarshal response body failed,err:%v,resp body:%v", err, string(body))
		return nil, err
	}
	logger.CtxInfo(ctx, "queryEcsDiagnoseMission resp:%v,diagTaskID:%v", utils.JsonToString(response), diagTaskID)
	return &response, nil
}

func transferLevel(status string) string {
	switch status {
	case "Normal":
		return diagnose_task_run.DiagnoseResultLevelInfo
	case "Warning":
		return diagnose_task_run.DiagnoseResultLevelWarning
	case "Abnormal":
		return diagnose_task_run.DiagnoseResultLevelError
	case "Fatal":
		return diagnose_task_run.DiagnoseResultLevelCritical
	default:
		return diagnose_task_run.DiagnoseResultLevelFailed
	}
}
