package ecs

// RequestParams 定义请求参数的结构体
type RequestParams struct {
	Extra        string `json:"Extra"`
	ResourceId   string `json:"ResourceId"`
	ResourceType string `json:"ResourceType"`
	BeginTime    int64  `json:"BeginTime"`
	EndTime      int64  `json:"EndTime"`
}

// RunDiagResp  返回值
type RunDiagResp struct {
	Code  int64  `json:"code"`
	Error string `json:"error"`
	Data  string `json:"data"`
}

// DiagnosisItem  表示诊断结果项结构体
type DiagnosisItem struct {
	DiagName       string `json:"diag_name"`
	DiagStatus     string `json:"diag_status"`
	DiagTime       int64  `json:"diag_time"`
	DiagMessage    string `json:"diag_message"`
	DiagRawMessage string `json:"diag_raw_message"`
}

// ShowItem  表示显示项结构体
type ShowItem struct {
	Name           string `json:"name"`
	Data           string `json:"data"`
	ShowItemTypeID string `json:"show_item_type_id"`
	DiagName       string `json:"diag_name"`
}

// ReportDetail 表示报告详情结构体
type ReportDetail struct {
	ReportItems []DiagnosisItem `json:"report_items"`
	ShowItems   []ShowItem      `json:"show_items"`
}

// Diagnosis  表示诊断任务结构体
type Diagnosis struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Owner        string `json:"owner"`
	ResourceType string `json:"resource_type"`
	Status       string `json:"status"`
	Params       []struct {
		Key         string `json:"key"`
		Name        string `json:"name"`
		ParamType   string `json:"param_type"`
		Description string `json:"description"`
	} `json:"params"`
	Description string `json:"description"`
	FuncPath    string `json:"func_path"`
	CreatedTime int64  `json:"created_time"`
	CreatedBy   string `json:"created_by"`
}

// DiagMissionItem 表示任务项结构体
type DiagMissionItem struct {
	ID          string          `json:"id"`
	CreatedTime int64           `json:"created_time"`
	UpdatedTime int64           `json:"updated_time"`
	DiagTaskID  string          `json:"diag_task_id"`
	Diagnosis   Diagnosis       `json:"diagnosis"`
	Status      string          `json:"status"`
	DiagResults []DiagnosisItem `json:"diag_results"`
	ResourceID  string          `json:"resource_id"`
	Input       string          `json:"input"`
}

// Response  表示主返回结构体
type Response struct {
	Code int `json:"code"`
	Data struct {
		DiagTaskID  string `json:"diag_task_id"`
		Status      string `json:"status"`
		CreatedTime int64  `json:"created_time"`
		UpdatedTime int64  `json:"updated_time"`
		DiagGroup   struct {
			ID             string `json:"id"`
			Name           string `json:"name"`
			Description    string `json:"description"`
			Owner          string `json:"owner"`
			Status         string `json:"status"`
			CreatedBy      string `json:"created_by"`
			DiagGroupItems []struct {
				DiagnosisID string `json:"diagnosis_id"`
				Priority    string `json:"priority"`
				Level       string `json:"level"`
			} `json:"diag_group_items"`
			CreatedTime  int64    `json:"created_time"`
			UpdatedTime  int64    `json:"updated_time"`
			ResourceType []string `json:"resource_type"`
			Priority     int      `json:"priority"`
		} `json:"diag_group"`
		ResourceID    string                  `json:"resource_id"`
		ResourceType  string                  `json:"resource_type"`
		Region        string                  `json:"region"`
		Input         string                  `json:"input"`
		Report        map[string]ReportDetail `json:"report_detail"`
		DiagnosisMap  map[string]Diagnosis    `json:"diagnosis_map"`
		OrderPriority []struct {
			SubTaskID string `json:"sub_task_id"`
			Priority  string `json:"priority"`
		} `json:"order_priority"`
		DiagMissionItems []DiagMissionItem `json:"diag_mission_items"`
		Operator         string            `json:"operator"`
	} `json:"data"`
}
