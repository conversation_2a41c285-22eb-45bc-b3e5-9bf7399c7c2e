package ecs

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetName() string {
	return common.IaasActivityName
}

func (h *Handler) TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error) {
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	switch ecsResp := resp.(type) {
	case *Response:
		for _, item := range ecsResp.Data.DiagMissionItems {
			for _, result := range item.DiagResults {
				var diagnoseResult model.DiagnoseResultV2
				diagnoseResult.InstanceID = ecsResp.Data.ResourceID
				diagnoseResult.InstanceName = lo.ToPtr(ecsResp.Data.ResourceID)
				diagnoseResult.Region = lo.ToPtr(ecsResp.Data.Region)
				diagnoseResult.DiagnoseMessage = lo.ToPtr(result.DiagName)
				diagnoseResult.DiagnoseResultLevel = transferLevel(result.DiagStatus)
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(result.DiagMessage)
				diagnoseResult.DiagnoseOperate = lo.ToPtr(result.DiagRawMessage)
				diagnoseResults = append(diagnoseResults, &diagnoseResult)
			}
		}
	default:
		fmt.Printf("resp type error: %s", utils.JsonToString(resp))
		return nil, fmt.Errorf("invalid response type: %T", resp)
	}
	return diagnoseResults, nil
}

func (h *Handler) RunDiagnoseItem(ctx context.Context, itemID int64, resources []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (interface{}, []*common.ErrorItem, error) {
	errorItems := make([]*common.ErrorItem, 0)
	token, err := getEcsToken()
	if err != nil {
		return nil, nil, fmt.Errorf("getEcsToken error: %s", err)
	}
	groupID, err := common.FindParamValueByDiagnoseItemID(ctx, itemID, paramGroupIDKey)
	if err != nil {
		return nil, nil, fmt.Errorf("findGroupIDByDiagnoseItemID error: %s", err)
	}
	host, err := common.FindHostByDiagnoseItemID(ctx, itemID)
	if err != nil {
		fmt.Printf("findHostByDiagnoseItemID error: %s", err)
	}
	ecsTaskIDs := make(map[string]string)
	for _, resource := range resources {
		for _, resourceID := range resource.Instances {
			//todo:ResourceType
			ecsTaskID, err := createEcsDiagnoseMission(host, token, groupID, resourceID, "instanceId",
				findTaskRunUserID(ctx, diagnoseInput.DiagnoseTaskRunID), diagnoseInput.DiagnoseStartTime, diagnoseInput.DiagnoseEndTime)
			if err != nil {
				errorItems = append(errorItems, &common.ErrorItem{
					ResourceID:     resourceID,
					DiagnoseItemID: itemID,
					Error:          err,
				})
			} else {
				ecsTaskIDs[ecsTaskID] = resourceID
			}
			//限频率
			time.Sleep(1 * time.Second)
		}
	}
	//查询任务结果
	resultArray := make([]*Response, 0)
	doneMap := make(map[string]bool)
	done := false
	//todo:超时时间设定
	if len(ecsTaskIDs) > 0 {
		for i := 0; i < 10; i++ {
			if done {
				break
			}
			time.Sleep(2 * time.Second)
			for ecsTaskID := range ecsTaskIDs {
				if doneMap[ecsTaskID] {
					continue
				}
				ecsResp, err := queryEcsDiagnoseMission(host, token, ecsTaskID)
				if err != nil {
					continue
				}
				if ecsResp.Data.Status == "Done" {
					resultArray = append(resultArray, ecsResp)
					doneMap[ecsTaskID] = true
				}
				if len(doneMap) == len(ecsTaskIDs) {
					done = true
					break
				}
			}
		}
	}
	//记录查询失败的诊断项
	if !done {
		for taskID, resourceID := range ecsTaskIDs {
			if doneMap[taskID] {
				continue
			}
			errorItems = append(errorItems, &common.ErrorItem{
				ResourceID:     resourceID,
				DiagnoseItemID: itemID,
				Error:          fmt.Errorf("queryEcsDiagnoseMission takeID:%s", taskID),
			})
		}
	}
	return resultArray, errorItems, nil
}
