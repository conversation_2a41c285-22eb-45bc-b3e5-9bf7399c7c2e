package network

// RequestParams 定义请求参数的结构体
type RequestParams struct {
	ResourceId string `json:"resourceID"`
	BeginTime  string `json:"beginTime"`
	EndTime    string `json:"endTime"`
}

// Entry 定义诊断项结构体
type EntryInstances struct {
	Name          string `json:"name"`
	Status        string `json:"status"`
	EntryID       string `json:"entryID"`
	Type          string `json:"type"`
	ResultMessage string `json:"resultMessage"`
}

// Diagnosis 定义诊断结果结构体
type Diagnosis struct {
	ResourceID     string           `json:"resourceID"`
	ResourceType   string           `json:"resourceType"`
	Status         string           `json:"status"`
	EntryInstances []EntryInstances `json:"entryInstances"`
}

// Response  定义主返回结构体
type Response struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	LogId   string `json:"logId"`
	Data    struct {
		DiagnosisID string      `json:"diagnosisID"`
		ResourceID  string      `json:"resourceID"`
		Status      string      `json:"status"`
		BeginTime   string      `json:"beginTime"`
		EndTime     string      `json:"endTime"`
		Region      string      `json:"region"`
		Link        string      `json:"link"`
		Diagnosis   []Diagnosis `json:"diagnosis"`
	} `json:"data"`
}
