package network

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/pkg/errors"
)

// SendPostRequest 发送 POST 请求并返回 诊断ID
func SendPostRequest(baseURL, ak, sk string, data map[string]interface{}) (map[string]interface{}, error) {
	// 创建 URL 查询参数
	params := url.Values{}
	params.Add("accessKeyId", ak)
	params.Add("signature", sk)

	// 构建完整的 URL
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 将请求数据转换为 JSON 格式
	jsonData, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	// 发送 POST 请求
	resp, err := http.Post(fullURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析 JSON 数据
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// SendGetRequest 发送 GET 请求并返回 诊断结果
func SendGetRequest(baseURL, ak, sk, diagnosisID string) (*Response, error) {
	logger := utils.NewModuleLogger("NetworkSendGetRequest")
	ctx := context.Background()

	// 创建 URL 查询参数
	params := url.Values{}
	params.Add("accessKeyId", ak)
	params.Add("signature", sk)
	params.Add("diagnosisID", diagnosisID)

	// 构建完整的 URL
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 发送 GET 请求
	resp, err := http.Get(fullURL) // ignore_security_alert
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析 JSON 数据
	var result Response
	err = json.Unmarshal(body, &result)
	if err != nil {
		return nil, err
	}

	logger.CtxInfo(ctx, "NetworkSendGetRequest resp:%v,diagID:%v", utils.JsonToString(result), diagnosisID)

	return &result, nil
}

// createRequestParams用于动态生成 requestParams
func createRequestParams(resourceId string, startTime string, endTime string) (string, error) {

	params := RequestParams{
		ResourceId: resourceId,
		BeginTime:  startTime,
		EndTime:    endTime,
	}
	jsonData, err := json.Marshal(params)
	if err != nil {
		return "", errors.Errorf("failed to marshal requestParams: %v", err)
	}
	return string(jsonData), nil
}

// TimeFormatConver 将int64类型时间戳转换成iso8601格式（例如：2025-03-31T10:45:04+08:00）
func TimeFormatConver(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	iso8601 := t.Format(time.RFC3339)
	// fmt.Println(iso8601)
	// fmt.Printf("类型：%T", iso8601)
	return iso8601
}

// CreateNetworkDiagnose创建诊断任务并返回诊断结果
func CreateNetworkDiagnose(resourceId string, startTime int64, endTime int64) (*Response, error) {
	logger := utils.NewModuleLogger("CreateNetworkDiagnose")
	ctx := context.Background()

	cfg := tcc.GetServiceConfig()
	if cfg == nil {
		return nil, errors.New("tcc config is nil")
	}
	fmt.Println("tcc 查询配置：", cfg)

	baseURL := cfg.NetworkConfig.Host
	ak := cfg.NetworkConfig.AK
	sk := cfg.NetworkConfig.SK

	// 时间跨度不能超过6小时，只能诊断近3天
	now := time.Now().Unix()
	if now-startTime > 3*24*60*60 {
		logger.CtxError(ctx, "create new request failed,err:%v", errors.New("time span is too long"))
		return nil, errors.New("time span is too long")
	}
	if endTime-startTime > 6*60*60 {
		endTime = startTime + 6*60*60
	}
	t1 := TimeFormatConver(startTime)
	t2 := TimeFormatConver(endTime)
	fmt.Println(t1)
	fmt.Println(t2)

	// 添加request_params字段
	requestParams, err := createRequestParams(resourceId, t1, t2)
	var data map[string]interface{}
	json.Unmarshal([]byte(requestParams), &data)

	// 调用 POST 请求，获取诊断ID
	postResponse, err := SendPostRequest(baseURL, ak, sk, data)
	if err != nil {
		fmt.Println("POST 请求失败:", err)
		logger.CtxError(ctx, "create new request failed,err:%v", err)
		return nil, err
	}
	if postResponse["success"].(bool) != true {
		fmt.Println("POST 请求失败:", err)
		logger.CtxError(ctx, "create new request failed,err:%v", err)
		return nil, errors.Errorf("failed to create new request: %v", postResponse)
	}
	diagnosisID := postResponse["data"].(map[string]interface{})["diagnosisID"]
	fmt.Println("诊断ID:", diagnosisID)

	// 睡眠1秒再查询诊断结果，避免状态结果为Running
	time.Sleep(5 * time.Second)

	// 调用 GET 请求，查询诊断结果
	getResponse, err := SendGetRequest(baseURL, ak, sk, diagnosisID.(string))
	if err != nil {
		fmt.Println("GET 请求失败:", err)
		logger.CtxError(ctx, "create new request failed,err:%v", err)
		return nil, err
	}
	fmt.Println("GET 响应:", getResponse)
	return getResponse, err
}

func transferLevel(status string) string {
	switch status {
	case "Success":
		return diagnose_task_run.DiagnoseResultLevelInfo
	case "Warning":
		return diagnose_task_run.DiagnoseResultLevelWarning
	case "Running":
		return diagnose_task_run.DiagnoseTaskStatusRunning
	case "Error":
		return diagnose_task_run.DiagnoseResultLevelCritical
	default:
		return diagnose_task_run.DiagnoseResultLevelFailed
	}
}
