package network

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) GetName() string {
	return common.NetworkActivityName
}

func (h *Handler) TransferRespToResults(ctx context.Context, resp interface{}) ([]*model.DiagnoseResultV2, error) {
	diagnoseResults := make([]*model.DiagnoseResultV2, 0)
	switch networkResp := resp.(type) {
	case *Response:
		for _, diag := range networkResp.Data.Diagnosis {
			for _, item := range diag.EntryInstances {
				var diagnoseResult model.DiagnoseResultV2
				diagnoseResult.InstanceID = networkResp.Data.ResourceID
				diagnoseResult.InstanceName = lo.ToPtr(networkResp.Data.ResourceID)
				diagnoseResult.Region = lo.ToPtr(networkResp.Data.Region)
				diagnoseResult.DiagnoseMessage = lo.ToPtr(item.Name)
				diagnoseResult.DiagnoseResultLevel = transferLevel(item.Status)
				diagnoseResult.DiagnoseSuggestion = lo.ToPtr(item.ResultMessage)
				diagnoseResult.DiagnoseOperate = nil
				diagnoseResults = append(diagnoseResults, &diagnoseResult)

			}
		}
	default:
		fmt.Printf("resp type error: %s", utils.JsonToString(resp))
		return nil, fmt.Errorf("invalid response type: %T", resp)
	}
	return diagnoseResults, nil
}

func (h *Handler) RunDiagnoseItem(ctx context.Context, itemID int64, resources []*diagnose_result.DiagnoseResource, diagnoseInput *statemachines.DiagnoseRequest) (interface{}, []*common.ErrorItem, error) {
	errorItems := make([]*common.ErrorItem, 0)
	resultArray := make([]*Response, 0)
	for _, resource := range resources {
		for _, resourceID := range resource.Instances {
			//todo:ResourceType
			result, err := CreateNetworkDiagnose(resourceID, diagnoseInput.DiagnoseStartTime, diagnoseInput.DiagnoseEndTime)

			if err != nil {
				errorItems = append(errorItems, &common.ErrorItem{
					ResourceID:     resourceID,
					DiagnoseItemID: itemID,
					Error:          fmt.Errorf("CreateNetworkDiagnose resourceID:%s", resourceID),
				})
			} else {
				resultArray = append(resultArray, result)
			}
			//限频率
			time.Sleep(1 * time.Second)
		}

	}
	return resultArray, nil, nil
}
