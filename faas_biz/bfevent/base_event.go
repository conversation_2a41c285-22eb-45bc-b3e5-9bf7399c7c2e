package bfevent

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent/task_handler"
	"context"
	"fmt"

	apiData "code.byted.org/cicd/byteflow/pkg/apiserver/models/api_data"
	"code.byted.org/cicd/byteflow/pkg/worker/byteflow"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
)

var (
	Activities = task_handler.LoadActivity()
	BFApp      *byteflow.ByteFlowApp
)

//type ActivityExecTempDate struct {
//	ResourceID        string
//	ProductCategoryID int64
//	DiagnoseItemID    int64
//	Host              string
//}

func InitBFEvent() {
	cfg := tcc.GetServiceConfig()
	app, err := byteflow.NewByteFlowApp(
		cfg.ByteFlowConfig.AppName,
		cfg.ByteFlowConfig.AppToken,
		cfg.ByteFlowConfig.ServerURL,
		cfg.ByteFlowConfig.SaSecret,
		byteflow.WithLaneEnabled(true),
	)
	BFApp = app
	ctx := context.Background()
	if err != nil {
		logs.CtxError(context.Background(), "NewByteFlowApp failed, err: %v", err)
		panic(fmt.Sprintf("NewByteFlowApp failed, err: %v", err))
	}
	// Activity在启动前需要已经在ByteFlow平台上注册好，如果已经在平台上注册过Activity，可以省略这一步。
	for name := range Activities {
		// 1. 注册接口是幂等的
		// 2. 不同Activity可以共用相同的RocketMQ Topic
		res, err := app.RegisterActivity(ctx, cfg.ByteFlowConfig.AppToken,
			name, apiData.AddActivityRequest{
				RocketCluster: cfg.ByteFlowConfig.RocketCluster,
				RocketTopic:   cfg.ByteFlowConfig.RocketTopic,
			})

		if err != nil {
			logs.CtxError(context.Background(), "RegisterActivity failed, err: %v", err)
			panic(fmt.Sprintf("RegisterActivity failed, err: %v\n", err))
		}
		logs.CtxInfo(ctx, "RegisterActivity  %s succeed, brn: %v\n", name, res.Data.BRN)
	}

	for name, handler := range Activities {
		if err = app.RegisterTaskWorker(name, handler); err != nil {
			panic(fmt.Sprintf("RegisterTaskWorker failed for %s, err: %v\n", name, err))
		}
	}
	//runCtx := ctx
	//var cancelFunc context.CancelFunc
	//runCtx, cancelFunc = context.WithTimeout(ctx, time.Minute*10)
	//defer cancelFunc()
	//if err = byteflow.CtxRunApps(runCtx, BFApp); err != nil {
	//	logs.CtxError(context.Background(), "RunApps failed, err: %v", err)
	//	panic(fmt.Sprintf("RunApps failed, err: %v\n", err))
	//}

	if err = byteflow.CtxRunApps(ctx, BFApp); err != nil {
		logs.CtxError(context.Background(), "RunApps failed, err: %v", err)
		panic(fmt.Sprintf("RunApps failed, err: %v\n", err))
	}
}
