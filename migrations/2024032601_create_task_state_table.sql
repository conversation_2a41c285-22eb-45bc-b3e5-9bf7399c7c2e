-- +migrate Up
CREATE TABLE task_state (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '关联诊断任务ID',
    current_state VARCHAR(50) NOT NULL COMMENT '当前状态',
    retries INT NOT NULL DEFAULT 0 COMMENT '重试次数',
    state_history JSON NOT NULL COMMENT '状态历史记录',
    version INT NOT NULL DEFAULT 1 COMMENT '乐观锁版本',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_task_id (task_id),
    FOREIGN KEY (task_id) REFERENCES diagnose_tasks(id) ON DELETE CASCADE
);

ALTER TABLE task_state 
ADD CONSTRAINT chk_state CHECK (current_state IN (
    'Created', 'Pending', 'Processing', 
    'Completed', 'Failed', 'Retrying'
));

-- +migrate Down
DROP TABLE task_state;
