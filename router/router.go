// Code generated by hertztool
package router

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/app_ak_sk"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/common"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/open_api"
	"time"

	"code.byted.org/middleware/hertz/pkg/app/server"
	vshertzext "code.byted.org/volcengine-support/gopkg/hertzext"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/diagnose_item"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/diagnose_template"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/example_task"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/lark_approval_option"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/oauth2"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler/ticket_diagnose_task"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"github.com/hertz-contrib/cors"
)

// customizeRegister register customize routers.
func customizeRegister(r *server.Hertz) {

	config := tcc.GetServiceConfig()
	allowOrigins := config.CORSConfig.AllowOrigins
	allowHeaders := config.CORSConfig.AllowHeaders

	// cros 跨域配置
	r.Use(cors.New(cors.Config{
		AllowOrigins:     allowOrigins, // 允许所有域名访问
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     allowHeaders,
		ExposeHeaders:    []string{"Content-Length", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           48 * time.Hour,
	}))

	r.Use(middleware.RequestLog())
	r.Use(middleware.Pre())

	defaultGroup := r.Group("/")
	// 进行 oauth2 鉴权
	defaultGroup.Use(middleware.OAuth2())
	{
		defaultGroup.Any("/permission", vshertzext.NewHandleAction(&permission.ActionHandler{}))
		defaultGroup.Any("/common", vshertzext.NewHandleAction(&common.ActionHandler{}))
		defaultGroup.Any("/feedback", vshertzext.NewHandleAction(&feedback.ActionHandler{}))
		defaultGroup.Any("/diagnose/result", vshertzext.NewHandleAction(&ticket_diagnose_task.ActionHandler{}))
		defaultGroup.Any("/diagnose_item", vshertzext.NewHandleAction(&diagnose_item.ActionHandler{}))
		defaultGroup.Any("/diagnose_template", vshertzext.NewHandleAction(&diagnose_template.ActionHandler{}))
		defaultGroup.Any("/diagnose_example", vshertzext.NewHandleAction(&example_task.ExampleTaskHandler{}))
		defaultGroup.Any("/resource", vshertzext.NewHandleAction(&describe_resource.ActionHandler{}))
		defaultGroup.Any("/diagnose/task", vshertzext.NewHandleAction(&diagnose_task_v2.ActionHandler{}))
		defaultGroup.Any("/diagnose/task/run", vshertzext.NewHandleAction(&diagnose_task_run.ActionHandler{}))
		defaultGroup.Any("/diagnose/product", vshertzext.NewHandleAction(&product_category.ActionHandler{}))
		defaultGroup.Any("/discourse", vshertzext.NewHandleAction(&discourse.ActionHandler{}))
		// oauth2 用户鉴权接口
		defaultGroup.Any("/oauth2", vshertzext.NewHandleAction(&oauth2.ActionHandler{}))

	}

	larkGroup := r.Group("/lark")
	{
		larkGroup.Any("/approval/option", lark_approval_option.LarkApprovalOptionProvide)
	}

	appGroup := r.Group("/app")
	{
		appGroup.Any("/aksk", vshertzext.NewHandleAction(&app_ak_sk.ActionHandler{}))
	}
	openApiGroup := r.Group("/openapi")
	openApiGroup.Use(middleware.AKSKAuthMiddleware())
	{
		openApiGroup.Any("/", vshertzext.NewHandleAction(&open_api.ActionHandler{}))
		openApiGroup.Any("/resource", vshertzext.NewHandleAction(&describe_resource.ActionHandler{}))
	}
}
