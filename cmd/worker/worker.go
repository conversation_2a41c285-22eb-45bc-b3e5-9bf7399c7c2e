package main

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"code.byted.org/volcengine-support/cloud-sherlock/biz"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/bfevent"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/event"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/message_consuemr"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/request"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"code.byted.org/gopkg/lang/v2/conv"

	"code.byted.org/bytefaas/faas-go/bytefaas"
	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/bytefaas/sdk-go/logger"
	"code.byted.org/gopkg/logs/v2"
)

func main() {
	// reset the global default logger, with log file size limit, and file/agent log rate limit (bytes per second).
	logger.Init()
	defer func() { _ = logger.Stop() }()

	biz.Init()
	bfevent.InitBFEvent()
	// Make the handler available for Http Call by Bytefaas Function

	bytefaas.Start(handler)
}

func handler(ctx context.Context, payload interface{}) (*events.EventResponse, error) {
	switch event := payload.(type) {
	case *events.HTTPRequest:
		return request.ScheduleRequest(ctx, event)
	case *events.CloudEvent:
		// logs/v1 compatible example
		logs.CtxInfo(ctx, "Received CloudEvent, data: %s, Extensions: %v", event.Data, event.Extensions())
		return handleCloudEvent(ctx, event)
	case []*events.CloudEvent:
		logs.CtxInfo(ctx, "Received Batch CloudEvent, %d events", len(event))
		for _, e := range event {
			logs.CtxInfo(ctx, "CloudEvent, data: %s", e.Data)
		}
		return &events.EventResponse{Body: []byte("Received CloudEvent.")}, nil
	default:
		logs.CtxWarn(ctx, "Could not detect event type.")
		return &events.EventResponse{StatusCode: http.StatusBadRequest}, nil
	}
}

func handleCloudEvent(ctx context.Context, ce *events.CloudEvent) (*events.EventResponse, error) {
	eventData, ok := ce.Data.([]byte)
	if !ok {
		logs.CtxError(ctx, "consumer message data type invalid")
		return nil, errors.New("consumer data invalid")
	}

	switch ce.Type() {
	case events.TimerEvent:
		type body struct {
			TaskName string
			Args     map[string]interface{}
		}

		b := &body{}
		err := utils.JsonUnmarshal(eventData, b)
		if err != nil {
			logs.CtxError(ctx, "unmarshal body，err=%+v, body=%s", err, ce.Data)
			return &events.EventResponse{}, err
		}

		err = event.ScheduleEvent(ctx, b.TaskName, b.Args)
		if err != nil {
			logs.CtxError(ctx, "run cronjob meet error，err=%+v, taskName=%s", err, b.TaskName)
			return nil, err
		}
		return &events.EventResponse{}, nil
	case events.RocketMQEvent, events.KafkaMQEvent:
		logs.CtxInfo(ctx, "[faas/handleCloudEvent] Received MQEvent Extensions:%s", utils.JsonToString(ce.Extensions()))
		logs.CtxInfo(ctx, "[faas/handleCloudEvent] Received MQEvent data:%s", utils.JsonToString(ce.Data))
		e := &message_consuemr.MessageEvent{
			Topic:         conv.StringDefault(ce.Extensions()["topic"], ""),
			ConsumerGroup: conv.StringDefault(ce.Extensions()["consumergroup"], ""),
			Offset:        conv.StringDefault(ce.Extensions()["offset"], ""),
			Data:          eventData,
		}
		err := message_consuemr.ScheduleMessageConsumer(ctx, e)
		if err != nil {
			logs.CtxError(ctx, "consumer message meet error，err=%+v", err)
			return nil, err
		}
	case events.EventbusEvent:
		logs.CtxInfo(ctx, "[faas/handleCloudEvent] Received MQEvent Extensions:%s", utils.JsonToString(ce.Extensions()))
		logs.CtxInfo(ctx, "[faas/handleCloudEvent] Received MQEvent data:%s", utils.JsonToString(ce.Data))
		e := &message_consuemr.MessageEvent{
			Topic:         conv.StringDefault(ce.Extensions()["topic"], ""),
			EventName:     conv.StringDefault(ce.Extensions()["event_name"], ""),
			Offset:        conv.StringDefault(ce.Extensions()["offset"], ""),
			ConsumerGroup: conv.StringDefault(ce.Extensions()["group"], ""),
			Data:          ce.Data.([]byte),
		}
		err := message_consuemr.ScheduleMessageConsumer(ctx, e)
		if err != nil {
			logs.CtxError(ctx, "consumer message meet error，err=%+v", err)
			return nil, err
		}
	default:
		logs.CtxError(ctx, "unknown event type:%s, %s", ce.Type(), utils.JsonToString(ce))
	}
	return &events.EventResponse{Body: []byte(fmt.Sprintf("Hello cloudevent, type %s.", ce.Type()))}, nil
}
