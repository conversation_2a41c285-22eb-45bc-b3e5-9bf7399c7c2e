// Code generated by hertztool.

package main

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"code.byted.org/middleware/hertz/byted"

	"code.byted.org/middleware/hertz/byted/config/local"
	"code.byted.org/middleware/hertz/byted/consts"
	"code.byted.org/middleware/hertz/byted/metainfo"
	mdw "code.byted.org/middleware/hertz/byted/middlewares/server"
	"code.byted.org/middleware/hertz/byted/middlewares/server/bytedtrace"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/app/server"
	stats2 "code.byted.org/middleware/hertz/pkg/common/tracer/stats"
	"code.byted.org/volcengine-support/cloud-sherlock/biz"
	"code.byted.org/volcengine-support/cloud-sherlock/router"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/cloudwego/hertz/pkg/common/config"
)

var (
	r *server.Hertz
)

func main() {
	fmt.Printf("start server")

	//byted.Init()

	// 如果是在 conans.wang 的 dev 开发机上，则开启 tls
	if os.Getenv("env") == "DEV-BOX" {
		// load server certificate
		cert, err := tls.LoadX509KeyPair("./tls/server.crt", "./tls/server.key")
		if err != nil {
			fmt.Println(err.Error())
		}
		// load root certificate
		certBytes, err := ioutil.ReadFile("./tls/ca.crt")
		if err != nil {
			fmt.Println(err.Error())
		}
		caCertPool := x509.NewCertPool()
		ok := caCertPool.AppendCertsFromPEM(certBytes)
		if !ok {
			panic("Failed to parse root certificate.")
		}
		// set server tls.Config
		cfg := &tls.Config{
			// add certificate
			Certificates: []tls.Certificate{cert},
			MinVersion:   tls.VersionTLS12,
			MaxVersion:   tls.VersionTLS13,
			// 不再强制客户端提供证书
			ClientAuth: tls.NoClientCert,
			ClientCAs:  caCertPool,
			// cipher suites supported
			CipherSuites: []uint16{
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
			},
			// 这里居然要注释掉，不然会报错，postman可以打开，但是浏览器不行,原因应该是使用的自签名证书
			// NextProtos: []string{"h2", "http/1.1"},
		}
		// set TLS server
		// default is standard.NewTransporter
		r = Default(server.WithTLS(cfg), server.WithHostPorts(":8443"))
		// 测试pingping接口
		r.GET("/pingping", func(ctx context.Context, c *app.RequestContext) {
			c.String(200, "TLS test\n")
		})
	} else {
		byted.Init()
		r = Default()
	}

	// r := Default()
	biz.Init()

	router.Register(r)
	r.Spin()
}

// MethodName 解决函数func1问题
func MethodName() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		methodName := c.FullPath()
		action := c.Query("Action")
		version := strings.Replace(c.Query("Version"), "-", "", -1)
		if action != "" {
			methodName += "?Action=" + action
		}
		if version != "" {
			methodName += version
		}
		c.Set(consts.METHODKEY, methodName)
		c.Next(ctx)
	}
}

func Default(opts ...config.Option) *server.Hertz {
	// prepare for listening addr
	if local.ServicePort() != 0 {
		opts = append(opts, server.WithHostPorts(fmt.Sprintf("%s:%d", local.BindingAddress(), local.ServicePort())))
	}
	if local.EnableTracing() {
		opts = append(opts, server.WithTracer(bytedtrace.NewServerTracer()))
	} else {
		opts = append(opts, server.WithTraceLevel(stats2.LevelDisabled))
	}

	h := server.New(opts...)
	mwConfig := mdw.DefaultMiddlewareConfig()
	h.Use(MiddlewareList(mwConfig)...)

	go func() {
		ctx := context.Background()
		defer utils.AsyncRecover(ctx)
		for {
			// wait server to run before meta report
			time.Sleep(1 * time.Second)
			if h.IsRunning() {
				break
			}
		}
		meta := metainfo.GenerateMetaInfo(h.GetOptions())
		pathPattern := metainfo.GeneratePathPattern(h.Routes())
		m := make(map[string]interface{})
		m["meta"] = meta
		m["mappings"] = pathPattern

		metainfo.ReportMetaInfo(m)
	}()
	return h
}

func MiddlewareList(mc mdw.MiddlewareConfig) []app.HandlerFunc {
	allMiddlewares := []app.HandlerFunc{
		mc.RecoveryMiddleware,
		mc.ContextMiddleware,
		MethodName(),
		mc.BytedTraceMiddleware,
		mc.AccessLogMiddleware,
		mc.MetricsMiddleware,
		mc.StressSwitcherMiddleware,
		mc.DynamicLogLevelMiddleware,
	}
	nonNilMiddlewares := make([]app.HandlerFunc, 0, len(allMiddlewares))
	for _, mw := range allMiddlewares {
		nonNilMiddlewares = appendIfNonNil(nonNilMiddlewares, mw)
	}
	return nonNilMiddlewares
}

func appendIfNonNil(mwList []app.HandlerFunc, mw app.HandlerFunc) []app.HandlerFunc {
	if mw == nil {
		return mwList
	}
	return append(mwList, mw)
}
