package fh_openapi

import (
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/pkg/app/client"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"crypto/tls"
	"encoding/json"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"net/http"
	"os"
)

var (
	FH_BOE_URL    = "https://fh-boe.bytedance.net/openapi/customer"
	FH_ONLINE_URL = "https://fh.bytedance.net/openapi/customer"
)

func ListCustomerVolcAccount(reqBody *fh_openapi.ListCustomerVolcAccountRequest) (*fh_openapi.ListCustomerVolcAccountResponse, error) {

	logger := utils.NewModuleLogger("fh_openapi.ListCustomerVolcAccount")
	conf := tcc.GetServiceConfig()
	env := conf.SSOEnv.Env
	url := ""
	if env == "boe" {
		url = FH_BOE_URL
	} else {
		url = FH_ONLINE_URL
	}

	clientCfg := &tls.Config{
		InsecureSkipVerify: true,
	}
	//创建客户端
	//跳过tls验证
	c, err := byted.NewClient(
		byted.WithAppClientOptions(
			client.WithTLSConfig(clientCfg),
			client.WithDialer(standard.NewDialer()),
		))
	if err != nil {
		return nil, err
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		logger.CtxError(context.Background(), "ListCustomerVolcAccount err:%v", err)
		return nil, err
	}

	req := &protocol.Request{}
	res := &protocol.Response{}
	defer func() {
		protocol.ReleaseRequest(req)
		protocol.ReleaseResponse(res)
	}()

	req.SetRequestURI(url)
	req.SetMethod(http.MethodPost)
	req.SetQueryString("Action=ListCustomerVolcAccount")
	req.SetBody(jsonBody)
	req.Header.SetContentTypeBytes([]byte("application/json")) //设置content-type

	if env == "boe" {
		req.Header.Add("acl-token", os.Getenv("SEC_TOKEN_STRING"))
	}

	// 发送请求
	logger.CtxInfo(context.Background(), "ListCustomerVolcAccount req: %v", req)
	err = c.Do(context.Background(), req, res)
	if err != nil {
		logger.Error("ListCustomerVolcAccount err:%v", err)
		return nil, err
	}

	// 处理响应...
	Response := &fh_openapi.ListCustomerVolcAccountResponse{}
	err = json.Unmarshal(res.Body(), Response)
	if err != nil {
		logger.CtxError(context.Background(), "ListCustomerVolcAccount json.Unmarshal err:%v", err)
		return nil, err
	}

	logger.CtxInfo(context.Background(), "ListCustomerVolcAccount Response: %v", Response)
	return Response, nil
}
