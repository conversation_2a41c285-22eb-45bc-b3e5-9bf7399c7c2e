package byteflowclient

import (
	"time"

	"code.byted.org/cicd/byteflow/pkg/client"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
)

var BFClient *client.Client

func Init() {
	cfg := tcc.GetServiceConfig()
	// ctx := context.Background()
	cli, err := client.NewClient(cfg.ByteFlowConfig.ServerURL, cfg.ByteFlowConfig.SaSecret, client.WithTimeout(time.Second*10), client.WithLaneEnabled(true))
	if err != nil {
		panic(err)
	}
	BFClient = cli
}
