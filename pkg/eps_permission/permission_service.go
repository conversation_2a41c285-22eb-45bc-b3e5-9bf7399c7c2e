package eps_permission

import (
	"context"
	"github.com/google/uuid"
	"strings"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"

	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/lang/v2/stringx"
	"code.byted.org/overpass/eps_platform_permission_core/kitex_gen/stargate_core"
	"code.byted.org/overpass/eps_platform_permission_core/rpc/eps_platform_permission_core"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/thoas/go-funk"
)

type EpsPermissionService struct {
	ApplicationID int64
	logger        *utils.ModuleLogger
}

func NewEpsPermissionService(applicationID int64) *EpsPermissionService {
	return &EpsPermissionService{
		ApplicationID: applicationID,
		logger:        utils.NewModuleLogger("EpsPermissionService"),
	}
}

// 聚合resource关联action列表
func (s *EpsPermissionService) GetUserPermission(ctx context.Context, userEmail string) ([]*permission.UserPermission, error) {
	logger := s.logger.WithFunc("GetUserPermission")
	rawReq := &stargate_core.GetUserPermissionsRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		UserEmail: userEmail,
	}
	rawResp, err := eps_platform_permission_core.RawCall.GetUserPermissions(ctx, rawReq)
	if err != nil {
		logger.CtxError(ctx, "rpc GetUserPermissions err:%v, req: %v", err, utils.JsonToString(rawReq))
		return nil, errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
	}
	logger.CtxInfo(ctx, "rpc GetUserPermissions success, req: %v, resp: %v", utils.JsonToString(rawReq), utils.JsonToString(rawResp))

	resourceActionsMap := make(map[string][]string)
	funk.ForEach(rawResp.Permissions, func(perm *stargate_core.SubjectPermission) {
		if perm != nil {
			/*
			   {
			       "SubjectType": "Role",
			       "SubjectKey": "project_member",
			       "ObjectKey": "common_resource",
			       "ActionKey": "project_weekly/get",
			       "ExpireAt": 4102416000000,
			       "CreateTime": 1717494404000
			   }
			*/
			//subjectType := perm.SubjectType //SubjectType: stargate_core.EntityTypeRole,
			//subjectKey := perm.SubjectKey
			actionKey := perm.ActionKey
			objectKey := perm.ObjectKey
			camelResourceObjectKey := stringx.ToCamelCase(objectKey, true, '_')
			if !slicex.Contains(resourceActionsMap[camelResourceObjectKey], actionKey) {
				resourceActionsMap[camelResourceObjectKey] = append(resourceActionsMap[camelResourceObjectKey], perm.ActionKey)
			}
		}
	})

	permissionList := make([]*permission.UserPermission, 0)
	funk.ForEach(resourceActionsMap, func(resourceKey string, actionKeys []string) {
		permissionList = append(permissionList, &permission.UserPermission{
			Resource:   resourceKey,
			ActionList: actionKeys,
		})
	})

	return permissionList, nil
}

func (s *EpsPermissionService) GetUserRoles(ctx context.Context, userEmail string) ([]string, error) {
	logger := s.logger.WithFunc("GetUserRoles")
	// 获取用户的所有角色
	rawReq := &stargate_core.GetUserRolesRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		UserEmail: userEmail,
	}
	resp, err := eps_platform_permission_core.RawCall.GetUserRoles(ctx, rawReq)
	if err != nil {
		logger.CtxError(ctx, "Rpc GetUserRoles err:%s, req:%s", err, utils.JsonToString(rawReq))
		return nil, errorcode.ErrUpStreamSystemError.WithArgs("Eps Permission")
	}
	logger.CtxInfo(ctx, "Rpc GetUserRoles success, req:%s, resp:%s", utils.JsonToString(rawReq), utils.JsonToString(resp))

	if resp != nil && len(resp.GetRoleKeys()) != 0 {
		return resp.GetRoleKeys(), nil
	}

	return []string{}, nil
}

func (s *EpsPermissionService) GetRoleMembers(ctx context.Context, roleKey string) ([]string, error) {
	logger := s.logger.WithFunc("GetRoleMembers")
	// 获取用户的所有角色
	rawReq := &stargate_core.GetRoleMembersRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		RoleKey: roleKey,
		Offset:  0,
		Limit:   1000,
	}
	resp, err := eps_platform_permission_core.RawCall.GetRoleMembers(ctx, rawReq)
	if err != nil {
		logger.CtxError(ctx, "Rpc GetRoleMembers err:%s, req:%s", err, utils.JsonToString(rawReq))
		return nil, err
	}
	if resp.GetBaseResp().GetStatusCode() != 0 {
		logger.CtxError(ctx, "Rpc GetRoleMembers err:%s, req:%s, resp:%s", err, utils.JsonToString(rawReq), utils.JsonToString(resp))
		return nil, errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
	}
	logger.CtxInfo(ctx, "Rpc GetRoleMembers success, req:%s, resp:%s", utils.JsonToString(rawReq), utils.JsonToString(resp))
	return resp.UserEmails, nil
}

func (s *EpsPermissionService) AddUserRoles(ctx context.Context, userEmail string, roleKey string, expireAt int64) error {
	logger := s.logger.WithFunc("AddUserRoles")
	// 获取用户的所有角色
	rawReq := &stargate_core.UpdateRoleMembersRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		RoleKey:       roleKey,
		AddUserEmails: []string{userEmail},
		ExpireAt:      expireAt,
	}
	_, err := eps_platform_permission_core.RawCall.UpdateRoleMembers(ctx, rawReq)
	if err != nil {
		logger.CtxError(ctx, "Rpc AddUserRoles err:%s, req:%s", err, utils.JsonToString(rawReq))
		return errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
	}
	return nil
}

func (s *EpsPermissionService) CheckPermissions(c context.Context, userEmail string, rules []*permission.Rule) (map[string]bool, error) {
	logger := s.logger.WithFunc("CheckPermissions")
	newRuleList := slicex.Map(rules, func(rule *permission.Rule) *stargate_core.Rule {
		return &stargate_core.Rule{
			ResourceKey: rule.ResourceKey,
			ActionKey:   rule.ActionKey,
			//CheckTag:    pointer.GetBool(rule.CheckTag),
		}
	})

	if len(newRuleList) == 0 {
		return nil, nil
	}

	// 获取用户的所有角色
	rawReq := &stargate_core.CheckPermissionsRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		UserEmail: userEmail,
		Rules:     newRuleList,
	}
	resp, err := eps_platform_permission_core.RawCall.CheckPermissions(c, rawReq)
	if err != nil {
		logger.CtxError(c, "Rpc CheckPermissions err:%s, req:%s", err, utils.JsonToString(rawReq))
		return nil, errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
	}
	logger.CtxInfo(c, "Rpc GetUserRoles success, req:%s, resp:%s", utils.JsonToString(rawReq), utils.JsonToString(resp))

	if resp != nil && len(resp.GetAccess()) != 0 {
		return resp.GetAccess(), nil
	}

	return nil, nil
}

// GetResources 获取一批资源的权限
func (s *EpsPermissionService) GetResources(c context.Context,
	resourceKey []string) ([]*permission.GetResourcesResp, error) {

	logger := s.logger.WithFunc("GetResources")
	// todo
	s.ApplicationID = 1829434486851642

	rawReq := &stargate_core.GetResourcesRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		ResourceKeys: resourceKey,
	}

	resp, err := eps_platform_permission_core.RawCall.GetResources(c, rawReq)
	if err != nil {
		logger.CtxError(c, "Rpc GetResources err:%s, req:%s", err, utils.JsonToString(rawReq))
		return nil, errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
	}
	logger.CtxInfo(c, "Rpc GetUserRoles success, req:%s, resp:%s", utils.JsonToString(rawReq), utils.JsonToString(resp))

	if resp != nil && len(resp.GetResources()) != 0 {
		return slicex.Map(slicex.Filter(resp.Resources, func(detail *stargate_core.ResourceDetail) bool {
				return detail.Enable
			}),
				func(detail *stargate_core.ResourceDetail) *permission.GetResourcesResp {
					return &permission.GetResourcesResp{
						Key:         detail.Key,
						Name:        detail.Name,
						Description: detail.Description,
						ActionKeys:  detail.ActionKeys,
					}
				}),
			nil
	}

	return nil, nil
}

// CreateRole 创建角色
func (s *EpsPermissionService) CreateRole(c context.Context,
	userEmail,
	name,
	roleKey,
	description string,
	owner []string) (string, error) {

	logger := s.logger.WithFunc("CreateRole")

	// 根据uuid生成分布式唯一roleKey
	memberRoleK := "R" + strings.Replace(uuid.New().String(), "-", "", -1)

	rawReq := &stargate_core.UpsertRoleRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
			OperatorKey:   userEmail,
		},
		Role: &stargate_core.Role{
			Key:             roleKey,
			Name:            name,
			Description:     description,
			AdminUserEmails: owner,
			Enable:          true,
		},
	}
	if resp, err := eps_platform_permission_core.RawCall.UpsertRole(c, rawReq); err != nil {
		logger.CtxError(c, "[eps_permission/CreateRole] UpsertRole err:%s, req: %s", err, utils.JsonToString(rawReq))
		return "", err
	} else {
		logger.CtxInfo(c, "[eps_permission/CreateRole] UpsertRole success, req: %s, resp: %s", utils.JsonToString(rawReq), utils.JsonToString(resp))
		return memberRoleK, nil
	}
}

// GetRoleAction 批量获取角色对应权限
func (s *EpsPermissionService) GetRoleAction(c context.Context,
	roleKeys []string) ([]string, error) {

	logger := s.logger.WithFunc("GetRoleAction")

	if roleKeys == nil || len(roleKeys) == 0 {
		return []string{}, nil
	}

	newStrRoleKey := slicex.Distinct(roleKeys)

	if len(newStrRoleKey) == 0 {
		logger.CtxWarn(c, "[eps_permission/GetRoleAction] newStrRoleKey size is 0")
		return []string{}, nil
	}

	rawReq := &stargate_core.GetRolesPermissionsRequest{
		BaseReq: &stargate_core.BaseReq{
			ApplicationID: s.ApplicationID,
		},
		RoleKeys: newStrRoleKey,
	}

	rawResp, err := eps_platform_permission_core.RawCall.GetRolesPermissions(c, rawReq)
	if err != nil {
		logger.CtxError(c, "[eps_permission/GetRoleAction] GetRolesPermissions err:%v, req:%s", err, utils.JsonToString(rawReq))
		return nil, err
	}
	logger.CtxInfo(c, "[eps_permission/GetRoleAction] GetRolesPermissions success, req:%s, resp:%s", utils.JsonToString(rawReq), utils.JsonToString(rawResp))

	return slicex.Map(rawResp.Permissions, func(perm *stargate_core.SubjectPermission) string {
		return perm.ActionKey
	}), nil
}
