package eps_permission

import "code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"

var permissionService *EpsPermissionService

func Init() {
	permissionConfig := tcc.GetServiceConfig().Permission
	if permissionConfig == nil {
		panic("Get permission config failed")
	}
	permissionService = NewEpsPermissionService(permissionConfig.AppID)
}

func GetEpsPermissionService() *EpsPermissionService {
	return permissionService
}
