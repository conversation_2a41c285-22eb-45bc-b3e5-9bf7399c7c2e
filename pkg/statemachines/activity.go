package statemachines

import (
	"bytes"
	"text/template"
)

type DynamicPara struct {
	AppName      string
	MapFaaSID    string
	ReduceFaaSID string
}

var para DynamicPara

func init() {
	para = DynamicPara{
		AppName: "cloud-sherlock",
		// MapFaaSID:    config.MapFaaSID,
		// ReduceFaaSID: config.ReduceFaaSID,
	}
}

var withAsyncActivityTask = `
{
  "StartAt": "IaaSDiagnose",
  "States": {
    "IaaSDiagnose": {
      "Type": "Task",
      "End": true,
      "Resource": "brn:byteflow:::{{.AppName}}/activity/iaasActivity:invoke"
    }
  }
}
`

func Decorator(s string) string {
	t := template.Must(template.New("decorator").Parse(s))
	bufferString := new(bytes.Buffer)
	var res string
	if err := t.Execute(bufferString, para); err != nil {
		panic(err)
	}
	res = bufferString.String()
	return res
}

func WithAsyncActivityTask() string {
	return Decorator(withAsyncActivityTask)
}
