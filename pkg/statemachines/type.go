package statemachines

import "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"

type DiagnoseRequest struct {
	DiagnoseTaskRunID  int64   `json:"DiagnoseTaskRunID"`
	TicketID           string  `json:"TicketID"`
	AccountID          *string `json:"AccountID"`
	DiagnoseTemplateID int64   `json:"DiagnoseTemplateID"`
	DiagnoseItemIDs    []int64 `json:"DiagnoseItemIDs"`
	// V2版本的Resources以map的形式传入Activity，key是诊断项ID
	Resources         map[int64][]*diagnose_result.DiagnoseResource `json:"Resources"`
	DiagnoseStartTime int64                                         `json:"DiagnoseStartTime"`
	DiagnoseEndTime   int64                                         `json:"DiagnoseEndTime"`
}
