package sso

import (
	"encoding/json"
	"math"
	"time"

	"code.byted.org/kv/goredis"

	OAuth2 "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/oauth2"
	redisStorage "code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

// 用户信息
type UserInfo struct {
	Name           string             `json:"name,omitempty"`
	Email          string             `json:"email,omitempty"`
	Username       string             `json:"username,omitempty"`
	EmployeeID     string             `json:"employee_id,omitempty"`
	NickName       string             `json:"nickname,omitempty"`
	Picture        string             `json:"picture,omitempty"`
	EmployeeNumber int64              `json:"employee_number,omitempty"`
	TenantAlias    string             `json:"tenant_alias,omitempty"`
	Department     *OAuth2.Department `json:"department,omitempty"`

	// redis 客户端连接对象
	RedisClient *goredis.Client
}

// 返回给前端的用户信息
type UserInfoLite struct {
	Name       string             `json:"name,omitempty"`
	Email      string             `json:"email,omitempty"`
	Department *OAuth2.Department `json:"department,omitempty"`
}

// 创建一个新的UserInfo对象
func NewUserInfo() *UserInfo {
	var userInfo = &UserInfo{}
	userInfo.RedisClient = redisStorage.RedisClient
	userInfo.Department = &OAuth2.Department{}
	return userInfo
}

// 将用户信息存储到redis中
// key: redis 的key, key的格式为 "token_"+Sha256(token)
// ttl: 过期时间,单位为纳秒
func (u *UserInfo) StoreToRedis(key string, ttlNanoSecond int64) error {
	// 将用户信息序列化为json字符串
	userInfoJson, err := json.Marshal(u)
	if err != nil {
		return err
	}
	// aes加密
	userInfoAes, err := utils.AesCrypto(string(userInfoJson))
	if err != nil {
		return err
	}
	// base64编码
	userInfoBase64 := utils.Base64Encode(userInfoAes)

	// 取 ttlNanoSecond的绝对值
	ttlNanoSecond = int64(math.Abs(float64(ttlNanoSecond)))

	// 将用户信息存储到redis中,过期时间为ttl秒
	// err = redisStorage.RedisClient.Set(key, userInfoBase64, time.Duration(ttlInt64)).Err()
	err = u.RedisClient.Set(key, userInfoBase64, time.Duration(ttlNanoSecond)).Err()
	if err != nil {
		return err
	}
	return nil

}
