package sso

import "golang.org/x/oauth2"

// oauth2 配置信息
type OAuth2Config struct {
	oauth2.Config

	UserInfoURL string
}

var (
	// boe oauth2 sso 配置
	BoeOAuth2Config = NewOAuth2Config(
		"md1kr5j59re7hu5ivedp",
		"kc0jfcfb4apdezgjn5vtkqsbm9sxl2m4jw62b3az",
		"https://test-sso.bytedance.net/oauth2/authorize",
		"https://test-sso.bytedance.net/oauth2/access_token",
		"https://test-sso.bytedance.net/oauth2/userinfo",
	)
	// prod oauth2 sso 配置
	ProdOAuth2Config = NewOAuth2Config(
		"qd1krromkk4w747tj7s4",
		"uj241vlrmbbqm6mwa6l3ujnkm7nuojvplm0oxqiy",
		"https://sso.bytedance.com/oauth2/authorize",
		"https://sso.bytedance.com/oauth2/access_token",
		"https://sso.bytedance.com/oauth2/userinfo",
	)
)

func NewOAuth2Config(clientId, clientSecret, authUrl, tokenUrl, userInfoUrl string) *OAuth2Config {
	var oauth2Cfg = OAuth2Config{
		Config: oauth2.Config{
			ClientID:     clientId,
			ClientSecret: clientSecret,
			Endpoint: oauth2.Endpoint{
				AuthURL:  authUrl,
				TokenURL: tokenUrl,
			},
		},
		UserInfoURL: userInfoUrl,
	}
	return &oauth2Cfg
}
