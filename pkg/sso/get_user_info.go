package sso

import (
	"encoding/json"
	"io/ioutil"
	"net/http"

	redisStorage "code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

// 使用token查询sso userinfo 接口，获取用户信息
func GetUserInfoByToken(token string) (*UserInfo, error) {
	u := NewUserInfo()
	u.RedisClient = redisStorage.RedisClient
	// 生成http请求,并携带token,访问sso userinfo 接口
	client := &http.Client{}
	req, err := http.NewRequest("GET", Conf.UserInfoURL, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Add("Authorization", "Bearer "+token)
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}

	defer resp.Body.Close()

	bs, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	// 解析用户信息，如果失败，则返回错误信息
	err = json.Unmarshal(bs, &u)
	if err != nil {
		return nil, err
	}
	return u, nil
}

// 使用token查询redis，获取用户信息
func GetUserInfoByTokenFromRedis(token string) (*UserInfo, error) {
	u := NewUserInfo()
	val, err := redisStorage.RedisClient.Get("token_" + utils.Sha256(token)).Result()
	if err != nil {
		return nil, err
	}
	// base64解码
	valBase64, err := utils.Base64Decode(val)
	if err != nil {
		return nil, err
	}
	// aes解密
	valAes, err := utils.AesDecrypto(valBase64)
	if err != nil {
		return nil, err
	}
	// 解析用户信息，如果失败，则返回错误信息
	err = json.Unmarshal([]byte(valAes), &u)
	if err != nil {
		return nil, err
	}
	return u, nil
}
