package rds

import (
	"context"
	"fmt"
	"reflect"
	"runtime/debug"
	"strings"

	"code.byted.org/gopkg/lang/v2/stringx"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"gorm.io/gen/field"
)

func CalculateOffset(pageNumber, pageSize int) int {
	return (pageNumber - 1) * pageSize
}

// GenerateGenOrder 根据gen生成表对象tableObj和排序规则生成期望的排序表达式
// 使用命名返回值，确保defer panic时能注入err
func GenerateGenOrder(c context.Context, tableObj any, rules []*common.Order) (ret []field.Expr, err error) {
	defer func() { // reflect.Value.MethodByName 有Panic风险
		if r := recover(); r != nil {
			logs.CtxError(c, "[utils/GenerateGenOrder] GenerateGenOrder failed, recover: %v, stack: %s", r, debug.Stack())
			err = fmt.Errorf("GenerateGenOrder Panic")
		}
	}()
	// 获取对象的值
	v := reflect.ValueOf(tableObj)
	if v.Kind() != reflect.Ptr {
		return nil, fmt.Errorf("tableObj must be a pointer")
	}

	// 遍历规则, 生成gen条件
	ret = make([]field.Expr, 0, len(rules))
	for _, rule := range rules {
		fieldOrderExpr, ok := callGetFieldByName(v, rule.FieldName)
		if !ok {
			return nil, fmt.Errorf("call GetFieldByName failed, field name: %s", rule.FieldName)
		}
		if *rule.Order == "Desc" {
			ret = append(ret, fieldOrderExpr.Desc())
		} else {
			ret = append(ret, fieldOrderExpr)
		}
	}
	return
}

// callGetFieldByName获取到的field.OrderExpr不止可以用来执行.Desc()作为排序的表达式
// ，也可以执行.Equal等其他GEN方法，作为其他链式过程中的表达式。

// 通过反射调用gen生成表对象的 GetFieldByName 方法
func callGetFieldByName(v reflect.Value, fieldName string) (field.OrderExpr, bool) {
	// 获取并调用方法
	method := v.MethodByName("GetFieldByName")
	if !method.IsValid() {
		return nil, false
	}
	results := method.Call([]reflect.Value{reflect.ValueOf(fieldName)})
	if len(results) != 2 {
		return nil, false
	}

	// 提取返回值
	orderExpr, ok := results[0].Interface().(field.OrderExpr)
	success := results[1].Bool()

	return orderExpr, success && ok
}

func GenerateToSnakeOrderStr(c context.Context, rules []*common.Order) string {
	orderStrList := make([]string, 0, len(rules))
	for _, rule := range rules {
		if rule == nil || stringx.IsBlank(rule.FieldName) {
			continue
		}
		var order string
		if stringx.IsBlank(*rule.Order) {
			order = "DESC"
		}
		orderStr := fmt.Sprintf("%s %s", stringx.ToLowerSnakeCase(rule.FieldName), order)
		orderStrList = append(orderStrList, orderStr)
	}
	return strings.Join(orderStrList, ",")
}

func GenerateOrderStr(c context.Context, rules []*common.Order) string {
	orderStrList := make([]string, 0, len(rules))
	for _, rule := range rules {
		if rule == nil || stringx.IsBlank(rule.FieldName) {
			continue
		}
		var order string
		if stringx.IsBlank(*rule.Order) {
			order = "DESC"
		}
		orderStr := fmt.Sprintf("%s %s", rule.FieldName, order)
		orderStrList = append(orderStrList, orderStr)
	}
	return strings.Join(orderStrList, ",")
}

func MakeFuzz(s string) string {
	return "%" + s + "%"
}
