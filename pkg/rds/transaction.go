package rds

import (
	"context"
	"errors"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/query"
	"github.com/bytedance/mockey"
)

// 解决循环引用：偏业务事务逻辑由utils目录移动至特定的rds三方目录
const transactionKey = "__gorm__transaction__"

func StartTransaction(ctx context.Context) context.Context {
	tx := QueryCloudSherlock.Begin()
	return context.WithValue(ctx, transactionKey, tx)
}

func SetTransaction(ctx context.Context, tx *query.Query) context.Context {
	return context.WithValue(ctx, transactionKey, tx)
}

func ClearTransaction(ctx context.Context) {
	tx := GetTransaction(ctx)
	if tx == nil {
		return
	}

	qtx := tx
	if qtx.Error != nil {
		err := qtx.Rollback()
		logs.CtxError(ctx, "rollback transaction error:%+v", err)
	}
	err := qtx.Commit()
	logs.CtxError(ctx, "commit transaction error:%+v", err)
	return
}

func GetTransaction(ctx context.Context) *query.QueryTx {
	tx := ctx.Value(transactionKey)
	if tx == nil {
		return nil
	}
	return tx.(*query.QueryTx)
}

func Transaction(ctx context.Context, fc func(ctx context.Context) error) (err error) {
	ctx = StartTransaction(ctx)

	defer func() {
		tx := ctx.Value(transactionKey)
		if tx == nil {
			return
		}
		qtx, ok := tx.(*query.QueryTx)
		if !ok {
			logs.CtxError(ctx, "[rds/transaction] transaction not queryTx type err:%+v", tx)
		}
		if err != nil {
			rollbackErr := qtx.Rollback()
			if rollbackErr != nil {
				logs.CtxError(ctx, "[rds/transaction] rollback transaction error:%+v", rollbackErr)
			}
		} else {
			err = qtx.Commit()
			if err != nil {
				logs.CtxError(ctx, "[rds/transaction] commit transaction error:%+v", err)
			}
		}
	}()

	tx := GetTransaction(ctx)
	if tx == nil {
		logs.CtxError(ctx, "[rds/transaction] transaction not found")
		return errors.New("transaction not found")
	}

	err = fc(ctx)
	if err != nil {
		logs.CtxError(ctx, "[rds/transaction] transaction error: %+v", err)

		return err
	}

	return nil
}

func MockTransaction() {
	mockey.Mock(GetTransaction).Return(&query.QueryTx{}).Build()
	mockey.Mock(StartTransaction).Return(context.Background()).Build()
}
