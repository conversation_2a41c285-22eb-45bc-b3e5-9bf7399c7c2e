package rds

// func pluginCustomerSyncBeforeQuery(db *gorm.DB) {
// 	// 生成定义logID
// 	ctx := ctxvalues.SetLogID(context.Background(), logid.GetNginxID())

// 	// TCC 可动态配置：针对插件Key
// 	pluginHookConfig, err := tcc.GetGormPluginHookConfig(ctx)
// 	if err != nil {
// 		logs.CtxError(ctx, "[rds/pluginCustomerSyncBeforeQuery] GetGormPluginHookConfig err:%v", err)
// 		return
// 	}
// 	if pluginHookConfig == nil || !pluginHookConfig.PluginSwitch[QueryPluginKey_CustomerSync] {
// 		logs.CtxWarn(ctx, "[rds/pluginCustomerSyncBeforeQuery] pluginHookConfig is nil")
// 		return
// 	}
// 	if !pluginHookConfig.PluginSwitch[QueryPluginKey_CustomerSync] {
// 		logs.CtxInfo(ctx, "[rds/pluginCustomerSyncBeforeQuery] PluginSwitch %s is closed", QueryPluginKey_CustomerSync)
// 		return
// 	}

// 	// customer表筛选
// 	tableName := db.Statement.Table
// 	if tableName != QuerySignalFire.Customer.TableName() {
// 		return
// 	}

// 	// 获取where中查询的customer_number参数
// 	clauses := db.Statement.Clauses
// 	clause, ok := clauses["WHERE"]
// 	if !ok {
// 		return
// 	}
// 	var customerNumberList []string
// 	var customerSfIDList []string
// 	switch clause.Expression.(type) {
// 	case gormClause.Where:
// 		exprs := clause.Expression.(gormClause.Where).Exprs
// 		for _, v := range exprs {
// 			switch v.(type) {
// 			case gormClause.Eq: // 1、等于
// 				eq, ok := v.(gormClause.Eq)
// 				if !ok || eq.Column == nil {
// 					continue
// 				}
// 				if column, ok := eq.Column.(gormClause.Column); ok && column.Name == "customer_number" {
// 					if columnValue, ok := eq.Value.(string); ok {
// 						customerNumberList = append(customerNumberList, columnValue)
// 					}
// 				}
// 				if column, ok := eq.Column.(gormClause.Column); ok && column.Name == "sf_id" {
// 					if columnValue, ok := eq.Value.(string); ok {
// 						customerSfIDList = append(customerSfIDList, columnValue)
// 					}
// 				}
// 			case gormClause.IN: // 2、In
// 				in, ok := v.(gormClause.IN)
// 				if !ok || in.Column == nil {
// 					continue
// 				}
// 				if column, ok := in.Column.(gormClause.Column); ok && column.Name == "customer_number" {
// 					funk.ForEach(in.Values, func(value *interface{}) {
// 						if value != nil && *value != nil {
// 							switch v := (*value).(type) {
// 							case string:
// 								customerNumberList = append(customerNumberList, v)
// 							}
// 						}
// 					})
// 				}
// 				if column, ok := in.Column.(gormClause.Column); ok && column.Name == "sf_id" {
// 					funk.ForEach(in.Values, func(value *interface{}) {
// 						if value != nil && *value != nil {
// 							switch v := (*value).(type) {
// 							case string:
// 								customerSfIDList = append(customerSfIDList, v)
// 							}
// 						}
// 					})
// 				}
// 				//case gormClause.Expr: // 3、表达式
// 				//	expr, ok := v.(gormClause.Expr)
// 				//	if !ok {
// 				//		continue
// 				//	}

// 				//	if expr.SQL == "customer_number" {
// 				//		for _, value := range expr.Vars {
// 				//			switch value.(type) {
// 				//			case string:
// 				//				num := value.(string)
// 				//				customerNumberList = append(customerNumberList, num)
// 				//			case []string:
// 				//				nums := value.([]string)
// 				//				customerNumberList = append(customerNumberList, nums...)
// 				//			}
// 				//		}
// 				//	}
// 			}
// 		}
// 	default:
// 		return
// 	}

// 	// 二次筛选过滤
// 	customerNumberList = slicex.Filter(customerNumberList, func(customerNumber string) bool {
// 		return len(customerNumber) != 0 && strings.HasPrefix(customerNumber, "ACC")
// 	})
// 	customerSfIDList = slicex.Filter(customerSfIDList, func(sfID string) bool {
// 		return len(sfID) != 0
// 	})

// 	if len(customerNumberList) == 0 && len(customerSfIDList) == 0 {
// 		logs.CtxInfo(ctx, "[rds/pluginCustomerSyncBeforeQuery] customerNumberList and customerSfIDList is blank")
// 		return
// 	}
// 	logs.CtxInfo(ctx, "[rds/pluginCustomerSyncBeforeQuery] orginal customerNumberList:%v, orginal customerSfIDList:%v", customerNumberList, customerSfIDList)

// 	// 校验底表是否存在
// 	// 源客户表数据
// 	subQuery := QuerySignalFire.SfCustomer.WithContext(ctx)
// 	if len(customerNumberList) != 0 || len(customerSfIDList) != 0 {
// 		if len(customerNumberList) != 0 {
// 			subQuery = subQuery.Or(QuerySignalFire.SfCustomer.AccountNumberC.In(customerNumberList...))
// 		}
// 		if len(customerSfIDList) != 0 {
// 			subQuery = subQuery.Or(QuerySignalFire.SfCustomer.SfID.In(customerSfIDList...))
// 		}
// 	}

// 	missingSfCustomerList, err := QuerySignalFire.SfCustomer.WithContext(ctx).
// 		LeftJoin(QuerySignalFire.Customer, QuerySignalFire.Customer.CustomerNumber.EqCol(QuerySignalFire.SfCustomer.AccountNumberC)).
// 		Where(subQuery).
// 		Where(QuerySignalFire.SfCustomer.IsDeleted.Is(false)).
// 		Where(QuerySignalFire.Customer.SfID.IsNull()).
// 		Find()
// 	if err != nil {
// 		logs.CtxError(ctx, "[rds/pluginBeforeQuery] FindSfCustomerByConditions err:%v", err)
// 		return
// 	}
// 	if len(missingSfCustomerList) == 0 {
// 		logs.CtxInfo(ctx, "[rds/pluginBeforeQuery] missingSfCustomerList is blank")
// 		return
// 	}

// 	// 获取用户+主客户数据
// 	var mainSfCustomerSfIDCustomerNumberMap, missingSfUserEmailMap map[string]string
// 	eg, _ := errgroup.WithContext(ctx)
// 	eg.Go(func() error {
// 		mainSfCustomerSfIDInDBList := make([]string, 0)
// 		funk.ForEach(missingSfCustomerList, func(sfCustomer *model.SfCustomer) {
// 			if sfCustomer != nil && len(sfCustomer.ParentID) != 0 {
// 				mainSfCustomerSfIDInDBList = append(mainSfCustomerSfIDInDBList, sfCustomer.ParentID)
// 			}
// 		})

// 		if len(mainSfCustomerSfIDInDBList) == 0 {
// 			return nil
// 		}

// 		// 补充缺失客户数据
// 		mainSfCustomerList, err := QuerySignalFire.SfCustomer.WithContext(ctx).
// 			Where(QuerySignalFire.SfCustomer.SfID.In(mainSfCustomerSfIDInDBList...)).Find()
// 		if err != nil {
// 			logs.CtxError(ctx, "[rds/pluginBeforeQuery] FindSfCustomer err:%v", err)
// 			return err
// 		}

// 		sfIDCustomerNumberMap, ok := funk.Map(mainSfCustomerList, func(sfCustomer *model.SfCustomer) (string, string) {
// 			return sfCustomer.SfID, pointer.GetString(sfCustomer.AccountNumberC)
// 		}).(map[string]string)
// 		if !ok {
// 			logs.CtxError(ctx, "[rds/pluginBeforeQuery] FindSfUserByConditions err:%v", err)
// 			return nil
// 		} else {
// 			mainSfCustomerSfIDCustomerNumberMap = sfIDCustomerNumberMap
// 		}

// 		return nil
// 	})
// 	eg.Go(func() error {
// 		missingOwnerSfIdList := make([]string, 0)
// 		funk.ForEach(missingSfCustomerList, func(sfCustomer *model.SfCustomer) {
// 			if sfCustomer != nil && len(sfCustomer.OwnerID) != 0 {
// 				missingOwnerSfIdList = append(missingOwnerSfIdList, sfCustomer.OwnerID)
// 			}
// 		})

// 		sfUserList, err := QuerySignalFire.SfUser.WithContext(ctx).Where(QuerySignalFire.SfUser.SfID.In(missingOwnerSfIdList...)).Find()
// 		if err != nil {
// 			logs.CtxError(ctx, "[rds/pluginBeforeQuery] FindSfUserByConditions err:%v", err)
// 			return err
// 		}
// 		sfUserEmailMap, ok := funk.Map(sfUserList, func(sfUser *model.SfUser) (string, string) {
// 			return sfUser.SfID, sfUser.Email
// 		}).(map[string]string)
// 		if !ok {
// 			logs.CtxError(ctx, "[rds/pluginBeforeQuery] FindSfUserByConditions err:%v", err)
// 			return nil
// 		} else {
// 			missingSfUserEmailMap = sfUserEmailMap
// 		}
// 		return nil
// 	})
// 	if err := eg.Wait(); err != nil {
// 		logs.CtxError(ctx, "[rds/pluginBeforeQuery] eg.Wait() err:%v", err)
// 		return
// 	}

// 	// 组装新客户表数据：存入
// 	newCustomerList := make([]*model.Customer, 0, len(missingSfCustomerList))
// 	newCustomerNumberList := make([]string, 0, len(missingSfCustomerList))
// 	funk.ForEach(missingSfCustomerList, func(sfCustomer *model.SfCustomer) {
// 		ownerEmail := missingSfUserEmailMap[sfCustomer.OwnerID]
// 		mainCustomerNumber := mainSfCustomerSfIDCustomerNumberMap[sfCustomer.ParentID]
// 		newCustomerList = append(newCustomerList, &model.Customer{
// 			SfID:               sfCustomer.SfID,
// 			ParentID:           sfCustomer.ParentID,
// 			CustomerNumber:     pointer.GetString(sfCustomer.AccountNumberC),
// 			MainCustomerNumber: mainCustomerNumber,
// 			CustomerName:       sfCustomer.Name,
// 			CustomerShortName:  sfCustomer.ShortNameC,
// 			Industry:           sfCustomer.IndustryC,
// 			SubIndustry:        sfCustomer.SubIndustryC,
// 			OwnerID:            sfCustomer.OwnerID,
// 			OwnerEmail:         ownerEmail,
// 			Country:            sfCustomer.CountryC,
// 			Region:             sfCustomer.RegionC,
// 			Province:           sfCustomer.ProvinceC,
// 			City:               sfCustomer.CityC,
// 			CustomerTier:       sfCustomer.AccountTierC,
// 			CustomerPLevel:     pointer.GetString(sfCustomer.AccountPLevelC),
// 			Status:             pointer.GetString(sfCustomer.StatusC),
// 		})

// 		newCustomerNumberList = append(newCustomerNumberList, pointer.GetString(sfCustomer.AccountNumberC))
// 	})

// 	if len(newCustomerList) > 0 {
// 		err = QuerySignalFire.Customer.WithContext(ctx).Create(newCustomerList...)
// 		if err != nil {
// 			logs.CtxError(ctx, "[rds/pluginBeforeQuery] CreateCustomer err:%v", err)
// 			return
// 		}
// 	}
// 	logs.CtxInfo(ctx, "[rds/pluginBeforeQuery] run plugin success, newCustomerNumberList:%v", newCustomerNumberList)
// 	return
// }
