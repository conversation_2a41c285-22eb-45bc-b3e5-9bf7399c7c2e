package rds

// func TestCalculateOffset(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange

// 			// Act
// 			got := CalculateOffset(1, 10)

// 			// Assert
// 			convey.So(got, convey.ShouldEqual, 0)
// 		})
// 	})
// }

// func TestGenerateGenOrder(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			QueryCloudSherlock = &query.Query{}
// 			gotRet, err := GenerateGenOrder(context.Background(), &QueryCloudSherlock.Customer, []*common.Order{&common.Order{
// 				FieldName: "created_time",
// 				Order:     pointer.ToString("Desc"),
// 			}})
// 			fmt.Println(gotRet)
// 			// Assert
// 			convey.So(err, convey.ShouldNotBeNil)
// 		})
// 	})
// }

// func TestGenerateSnakeOrderStr(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange

// 			// Act
// 			var v string = ""
// 			got := GenerateToSnakeOrderStr(context.Background(), []*common.Order{&common.Order{
// 				FieldName: "CreatedTime",
// 				Order:     &v,
// 			}})

// 			fmt.Println(got)
// 			// Assert
// 			convey.So(got, convey.ShouldEqual, "created_time DESC")
// 		})
// 	})

// }

// func TestGenerateOrderStr(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			// mockey.Mock((*common.Order).Order).Return("").Build()

// 			// Act
// 			var v string = ""
// 			got := GenerateOrderStr(context.Background(), []*common.Order{&common.Order{
// 				FieldName: "CreatedTime",
// 				Order:     &v,
// 			}})
// 			fmt.Println(got)
// 			// Assert
// 			convey.So(got, convey.ShouldEqual, "CreatedTime DESC")
// 		})
// 	})
// }
