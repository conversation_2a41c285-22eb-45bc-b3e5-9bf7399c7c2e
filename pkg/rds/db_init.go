package rds

import (
	"context"
	"fmt"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gorm/bytedgorm"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/query"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	QueryCloudSherlock *query.Query
	// QueryPdp        *pdp_query.Query
	// QueryLdi *ldi_query.Query

	DBSignalFire *gorm.DB
)

func Init() {
	InitCloudSherlockDB()
}

func InitCloudSherlockDB() {
	dbConfig := tcc.GetServiceConfig().CloudSherlockDB
	if dbConfig == nil {
		panic("Get db config failed")
	}
	psm := dbConfig.PSM
	dbName := dbConfig.DBName
	rwDb, err := gorm.Open(
		bytedgorm.MySQL(psm, dbName),
		bytedgorm.Logger{
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
		},
		&gorm.Config{
			PrepareStmt:            true,
			SkipDefaultTransaction: true,
		})
	fmt.Printf("[rds/InitCloudSherlockDB] dbConfig:%+v\n", dbConfig)
	if err != nil {
		logs.CtxError(context.Background(), "[rds/InitSignalFireDB] gorm.Open err:%v", err)
		panic(err)
	}
	DBSignalFire = rwDb
	QueryCloudSherlock = query.Use(rwDb)

	// 自定义插件注册
	// RegisteSignalFireDBPlugin()

	return
}

// func InitPdpDB() {
// 	dbConfig := tcc.GetServiceConfig().PdpDBReadOnly
// 	if dbConfig == nil {
// 		panic("Get db config failed")
// 	}
// 	psm := dbConfig.PSM
// 	dbName := dbConfig.DBName
// 	host := dbConfig.Host
// 	readDb, err := gorm.Open(
// 		bytedgorm.MySQL(psm, dbName).With(func(conf *bytedgorm.DBConfig) {
// 			conf.DBHostname = host
// 		}),
// 		bytedgorm.Logger{
// 			LogLevel:                  logger.Info,
// 			IgnoreRecordNotFoundError: true,
// 		})
// 	if err != nil {
// 		logs.CtxError(context.Background(), "[rds/InitPdpDB] gorm.Open err:%v", err)
// 		panic(err)
// 	}
// 	QueryPdp = pdp_query.Use(readDb)
// 	return
// }

// func InitLdiDB() {
// 	dbConfig := tcc.GetServiceConfig().LdiDBReadOnly
// 	if dbConfig == nil {
// 		panic("Get db config failed")
// 	}
// 	psm := dbConfig.PSM
// 	dbName := dbConfig.DBName
// 	host := dbConfig.Host
// 	readDb, err := gorm.Open(
// 		bytedgorm.MySQL(psm, dbName).With(func(conf *bytedgorm.DBConfig) {
// 			conf.DBHostname = host
// 		}),
// 		bytedgorm.Logger{
// 			LogLevel:                  logger.Info,
// 			IgnoreRecordNotFoundError: true,
// 		})
// 	if err != nil {
// 		logs.CtxError(context.Background(), "[rds/InitLdiDB] gorm.Open err:%v", err)
// 		panic(err)
// 	}
// 	QueryLdi = ldi_query.Use(readDb)
// 	return
// }
