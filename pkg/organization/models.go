package organization

type BatchGetOrganizationReq struct {
	OrganizationNumber []string // 组织根节点，决定获取不同组织类型树
	WithEmployee       bool     // 获取人员信息
	WithBps            bool     // 获取人员信息
	IncludeTransferOut bool     // 获取转出人员信息
	IncludeExpired     bool     // 获取离职人员信息
}

// ErrorObj 错误结构
type ErrorObj struct {
	Code    string
	Message string
}

// ResponseMetadata metadata结构
type ResponseMetadata struct {
	RequestID string
	Service   string
	Error     *ErrorObj
}

type LeaderInfo struct {
	Name           string // 姓名
	Email          string // 邮箱
	EmployeeNumber string // 工号
}

type Employee struct {
	ID             int64       // ID
	Name           string      // 姓名
	Email          string      // 邮箱
	EmployeeNumber string      // 工号
	LarkUserID     string      // 飞书用户ID
	DepartmentID   string      // 飞书部门ID
	DepartmentName string      // 飞书部门名称
	JobFamily      string      // 序列
	JoinTime       int64       // 入职时间
	ExpirationTime *int64      // 离职时间
	IsExpired      bool        // 是否离职
	Leader         *LeaderInfo // Leader
}

// OrganizationEmployee 人员组织关系信息
type OrganizationEmployee struct {
	RelID                int64     // 关系ID
	Employee             *Employee // 人员
	Position             string    // 职位
	IsLeader             bool      // 是否管理者
	IsTransferOut        bool      // 是否转出
	TransferOutTime      *int64    // 转出时间
	OrganizationNumber   string    // 架构编号
	OrganizationNamePath string    // 组织架构
}

// Organization 组织信息
type Organization struct {
	CreatedTime              int64                   // 创建时间
	UpdatedTime              int64                   // 更新时间
	Creator                  string                  // 创建人
	Number                   string                  // 架构编号
	Name                     string                  // 架构名
	ParentOrganizationNumber string                  // 父级架构编号
	NamePath                 string                  // 架构路径名称
	Level                    int                     // 层级 1:团队 2:小组 3:二级小组
	OrganizationType         string                  // 架构类型
	TreeOrder                float64                 // 树形排序
	OrgLeader                string                  // 组织管理者邮箱
	OrgLeaderNumber          string                  // 组织管理者工号
	OrgLeaderName            string                  // 组织管理者姓名
	Managers                 []string                // 管理员
	SaleBps                  []string                // 销管bp
	ProductBps               []string                // 产管bp
	HrBps                    []string                // HRBP
	Employees                []*OrganizationEmployee // 组织架构下人员
	ChildrenOrganization     []*Organization         // 子架构
	IsLeaf                   bool                    // 是否叶子结点
	ParentOrganizationName   string                  // 父级架构名
}

type BatchGetOrganizationResp struct {
	ResponseMetadata ResponseMetadata
	Result           []*Organization
}

type CommonEnum struct {
	Label    string
	Value    string
	Children []*CommonEnum     `json:"Children,omitempty"`
	Extra    map[string]string `json:"Extra,omitempty"`
}

type GetCommonEnumResp struct {
	ResponseMetadata ResponseMetadata
	Result           map[string]map[string][]*CommonEnum
}

type ListEmployeeChangeRecordReq struct {
	ChangeTimeBegin int64
	ChangeTimeEnd   int64
	Email           []string
	EmployeeNumber  []string
	Type            []int32
	Limit           int
}

type EmployeeChangeRecord struct {
	ID                      int64  // 自增ID
	CreatedTime             int64  // 创建时间
	UpdatedTime             int64  // 更新时间
	Type                    int32  // 变动类型
	Name                    string // 姓名
	Email                   string // 邮箱
	EmployeeNumber          string // 工号
	LarkUserID              string // 飞书用户id
	ChangeTime              int64  // 变更时间
	OldOrganizationNumber   string // 原组织架构编号
	OldOrganizationNamePath string // 原组织架构名路径
	OldPosition             string // 原人员岗位
	NewOrganizationNumber   string // 新组织架构编号
	NewOrganizationNamePath string // 新组织架构名路径
	NewPosition             string // 新人员岗位
	Source                  int32  //来源
}

type ListEmployeeChangeRecordResp struct {
	ResponseMetadata ResponseMetadata
	Result           []*EmployeeChangeRecord
}
