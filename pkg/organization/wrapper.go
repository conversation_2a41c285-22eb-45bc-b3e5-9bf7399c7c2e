package organization

import (
	"context"
	"net/http"
	"time"

	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/http_client"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	anycache "code.byted.org/webcast/libs_anycache"
)

func (o *OrgsManagementClient) BatchGetOrganization(c context.Context, param *BatchGetOrganizationReq) ([]*Organization, error) {
	logger := o.logger.WithFunc("OpenBatchGetOrganization")
	bodyBytes, err := utils.JsonMarshalToBytes(param)
	if err != nil {
		logger.CtxError(c, "json.Marshal err:%v", err)
		return nil, err
	}

	reqParams := http_client.RequestParams{
		Host:    o.host,
		Path:    "/openapi/organization",
		Method:  http.MethodPost,
		Query:   "Action=OpenBatchGetOrganization",
		Headers: o.commonHeaders,
		Body:    bodyBytes,
		WithSD:  true,
	}

	resp, err := http_client.DoRequest(c, reqParams)
	if err != nil {
		logger.CtxError(c, "client.HttpClient.Do err:%v", err)
		return nil, err
	}
	defer func() {
		resp.SetConnectionClose()
		_ = resp.CloseBodyStream()
	}()
	rp := &BatchGetOrganizationResp{}
	err = utils.JsonUnmarshal(resp.Body(), rp)
	if err != nil {
		logger.CtxError(c, " unmarshal resp.Body() failed, err:%v", err)
		return nil, err
	}

	if rp.ResponseMetadata.Error != nil {
		logger.CtxError(c, "BatchGetOrganization failed.err=%s", utils.JsonToString(rp.ResponseMetadata.Error))
		return nil, err
	}
	return rp.Result, nil
}

func (o *OrgsManagementClient) ListEmployeeChangeRecord(c context.Context, param ListEmployeeChangeRecordReq) ([]*EmployeeChangeRecord, error) {
	logger := o.logger.WithFunc("OpenListEmployeeChangeRecord")
	bodyBytes, err := utils.JsonMarshalToBytes(param)
	if err != nil {
		logger.CtxError(c, "json.Marshal err:%v", err)
		return nil, err
	}
	reqParams := http_client.RequestParams{
		Host:    o.host,
		Path:    "/openapi/employee",
		Method:  http.MethodPost,
		Query:   "Action=OpenListEmployeeChangeRecord",
		Headers: o.commonHeaders,
		Body:    bodyBytes,
		WithSD:  true,
	}

	resp, err := http_client.DoRequest(c, reqParams)
	if err != nil {
		logger.CtxError(c, "client.HttpClient.Do err:%v", err)
		return nil, err
	}
	defer func() {
		resp.SetConnectionClose()
		_ = resp.CloseBodyStream()
	}()
	rp := &ListEmployeeChangeRecordResp{}
	err = utils.JsonUnmarshal(resp.Body(), rp)
	if err != nil {
		logger.CtxError(c, " unmarshal resp.Body() failed, err:%v", err)
		return nil, err
	}

	if rp.ResponseMetadata.Error != nil {
		logger.CtxError(c, "ListEmployeeChangeRecord failed.err=%s", utils.JsonToString(rp.ResponseMetadata.Error))
		return nil, err
	}
	return rp.Result, nil
}

var cacheGetCommonEnum = anycache.NewDefaultObjectCache().
	WithNameSpace("commonEnum").
	WithTTL(30*time.Minute, 30*time.Minute).
	WithCacheNil(true).
	BuildFetcherByLoader(
		func(ctx context.Context, item interface{}, extraParam interface{}) string {
			return item.(string)
		},
		func(ctx context.Context, missedItem interface{}, extraParam interface{}) (interface{}, error) {
			commonEnums, err := DefaultInstance.GetCommonEnum(ctx)
			if err != nil {
				return nil, err
			}
			return commonEnums, nil
		})

func (o *OrgsManagementClient) GetCacheCommonEnum(c context.Context) (map[string]map[string][]*CommonEnum, error) {
	var (
		ret    map[string]map[string][]*CommonEnum
		logger = o.logger.WithFunc("GetCacheCommonEnum")
	)
	_, err := cacheGetCommonEnum.Get(c, "commonEnum", &ret)
	if err != nil {
		logger.CtxError(c, "GetCommonEnum meet error:%+v,", err)
		return nil, err
	}
	return ret, nil
}

func (o *OrgsManagementClient) GetCommonEnum(c context.Context) (map[string]map[string][]*CommonEnum, error) {
	logger := o.logger.WithFunc("OpenGetCommonEnum")

	reqParams := http_client.RequestParams{
		Host:    o.host,
		Path:    "/openapi/common",
		Method:  http.MethodPost,
		Query:   "Action=OpenGetCommonEnum",
		Headers: o.commonHeaders,
		WithSD:  true,
	}

	resp, err := http_client.DoRequest(c, reqParams)
	if err != nil {
		logger.CtxError(c, "client.HttpClient.Do err:%v", err)
		return nil, err
	}
	defer func() {
		resp.SetConnectionClose()
		_ = resp.CloseBodyStream()
	}()
	rp := &GetCommonEnumResp{}
	err = utils.JsonUnmarshal(resp.Body(), rp)
	if err != nil {
		logger.CtxError(c, " unmarshal resp.Body() failed, err:%v", err)
		return nil, err
	}

	if rp.ResponseMetadata.Error != nil {
		logger.CtxError(c, "GetCommonEnum failed.err=%s", utils.JsonToString(rp.ResponseMetadata.Error))
		return nil, err
	}
	return rp.Result, nil
}

func LoopGetOrganizationEmailList(ctx context.Context, organization *Organization) []string {
	allEmailList := make([]string, 0)
	if organization == nil {
		return nil
	}
	if len(organization.Employees) == 0 && len(organization.ChildrenOrganization) == 0 {
		return nil
	}

	for _, employee := range organization.Employees {
		if employee == nil {
			continue
		}
		if employee.Employee != nil && !slicex.Contains(allEmailList, employee.Employee.Email) {
			allEmailList = append(allEmailList, employee.Employee.Email)
		}
	}
	for _, childOrganization := range organization.ChildrenOrganization {
		if childOrganization == nil {
			continue
		}
		emailList := LoopGetOrganizationEmailList(ctx, childOrganization)
		if len(emailList) != 0 {
			allEmailList = append(allEmailList, emailList...)
		}
	}

	return allEmailList
}

func LoopGetLeaderOrganizationNode(ctx context.Context, org *Organization) *Organization {
	userEmail := utils.CtxGetEmployeeEmail(ctx)
	var leaderOrg *Organization
	for _, employee := range org.Employees {
		if employee == nil {
			continue
		}
		if employee.IsLeader && employee.Employee != nil && employee.Employee.Email == userEmail {
			leaderOrg = org
		}
	}
	for _, childOrganization := range org.ChildrenOrganization {
		if childOrganization == nil {
			continue
		}
		childOrg := LoopGetLeaderOrganizationNode(ctx, childOrganization)
		if childOrg != nil {
			return childOrg
		}
	}
	return leaderOrg
}
