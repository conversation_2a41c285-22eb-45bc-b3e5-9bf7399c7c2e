package organization

import "code.byted.org/volcengine-support/cloud-sherlock/utils"

var DefaultInstance *OrgsManagementClient

type OrgsManagementClient struct {
	logger        *utils.ModuleLogger
	host          string
	commonHeaders map[string]string
}

func init() {
	DefaultInstance = &OrgsManagementClient{
		logger: utils.NewModuleLogger("OrgsManagementClient"),
		host:   "http://eps.platform.eps_orgs_management",
		commonHeaders: map[string]string{
			"Content-Type": "application/json;charset=UTF-8",
		},
	}
}
