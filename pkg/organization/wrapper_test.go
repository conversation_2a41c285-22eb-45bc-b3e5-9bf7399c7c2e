package organization

// func TestPermissionService_loopGetCSMList(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.<PERSON>Convey("", t, func() {
// 			// Arrange
// 			organizationList := make([]*Organization, 0)
// 			oStr := "[{\"CreatedTime\":1716459743000,\"UpdatedTime\":1717494818000,\"Creator\":\"8763618\",\"Number\":\"ORG00004\",\"Name\":\"技术服务\",\"ParentOrganizationNumber\":\"\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":4,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[],\"ChildrenOrganization\":[{\"CreatedTime\":1717727377000,\"UpdatedTime\":1724382305000,\"Creator\":\"8231871\",\"Number\":\"ORG10462\",\"Name\":\"测试专用\",\"ParentOrganizationNumber\":\"ORG00004\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":1,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[{\"RelID\":2045,\"Employee\":{\"ID\":667,\"Name\":\"杜燕凤\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"9203001\",\"LarkUserID\":\"efc495c4\",\"DepartmentID\":\"od-92b39c95c2ad01c7a08fd0b22720a308\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-客户管理\",\"JobFamily\":\"质量保障\",\"JoinTime\":1632700800000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":true,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"},{\"RelID\":7985,\"Employee\":{\"ID\":680,\"Name\":\"汪兆光\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"8231871\",\"LarkUserID\":\"e7dc435g\",\"DepartmentID\":\"od-92b39c95c2ad01c7a08fd0b22720a308\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-客户管理\",\"JobFamily\":\"技术\",\"JoinTime\":1652832000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"group_leader\",\"IsLeader\":false,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"},{\"RelID\":18129,\"Employee\":{\"ID\":1535,\"Name\":\"李本领\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"7003573\",\"LarkUserID\":\"91a11cd1\",\"DepartmentID\":\"od-affb5626cc086dbe2a163df5f2dc07c7\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-业务管理\",\"JobFamily\":\"质量保障\",\"JoinTime\":1653408000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":false,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"}],\"ChildrenOrganization\":[{\"CreatedTime\":1724382389000,\"UpdatedTime\":1724382389000,\"Creator\":\"8607283\",\"Number\":\"ORG10490\",\"Name\":\"111\",\"ParentOrganizationNumber\":\"ORG10462\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":1,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[{\"RelID\":7982,\"Employee\":{\"ID\":679,\"Name\":\"熊刚\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"9677920\",\"LarkUserID\":\"53564dd4\",\"DepartmentID\":\"od-affb5626cc086dbe2a163df5f2dc07c7\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-业务管理\",\"JobFamily\":\"技术\",\"JoinTime\":1646352000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":false,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10490\",\"OrganizationNamePath\":\"技术服务/测试专用/111\"}],\"ChildrenOrganization\":[],\"IsLeaf\":true,\"ParentOrganizationName\":\"\"}],\"IsLeaf\":false,\"ParentOrganizationName\":\"\"}],\"IsLeaf\":false,\"ParentOrganizationName\":\"\"}]"
// 			utils.JsonUnmarshalString(oStr, &organizationList)

// 			// Act
// 			resultList := LoopGetLeaderOrganizationNode(context.Background(), organizationList[0])
// 			fmt.Println(resultList)
// 			// Assert
// 			convey.So("your code", convey.ShouldEqual, "your code")
// 		})
// 	})
// }

// func TestPermissionService_LoopGetLeaderOrganizationNode(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			// mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()
// 			organizationList := make([]*Organization, 0)
// 			oStr := "[{\"CreatedTime\":1716459743000,\"UpdatedTime\":1717494818000,\"Creator\":\"8763618\",\"Number\":\"ORG00004\",\"Name\":\"技术服务\",\"ParentOrganizationNumber\":\"\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":4,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[],\"ChildrenOrganization\":[{\"CreatedTime\":1717727377000,\"UpdatedTime\":1724382305000,\"Creator\":\"8231871\",\"Number\":\"ORG10462\",\"Name\":\"测试专用\",\"ParentOrganizationNumber\":\"ORG00004\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":1,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[{\"RelID\":2045,\"Employee\":{\"ID\":667,\"Name\":\"杜燕凤\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"9203001\",\"LarkUserID\":\"efc495c4\",\"DepartmentID\":\"od-92b39c95c2ad01c7a08fd0b22720a308\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-客户管理\",\"JobFamily\":\"质量保障\",\"JoinTime\":1632700800000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":false,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"},{\"RelID\":7985,\"Employee\":{\"ID\":680,\"Name\":\"汪兆光\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"8231871\",\"LarkUserID\":\"e7dc435g\",\"DepartmentID\":\"od-92b39c95c2ad01c7a08fd0b22720a308\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-客户管理\",\"JobFamily\":\"技术\",\"JoinTime\":1652832000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"group_leader\",\"IsLeader\":true,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"},{\"RelID\":18129,\"Employee\":{\"ID\":1535,\"Name\":\"李本领\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"7003573\",\"LarkUserID\":\"91a11cd1\",\"DepartmentID\":\"od-affb5626cc086dbe2a163df5f2dc07c7\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-业务管理\",\"JobFamily\":\"质量保障\",\"JoinTime\":1653408000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":false,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10462\",\"OrganizationNamePath\":\"技术服务/测试专用\"}],\"ChildrenOrganization\":[{\"CreatedTime\":1724382389000,\"UpdatedTime\":1724382389000,\"Creator\":\"8607283\",\"Number\":\"ORG10490\",\"Name\":\"111\",\"ParentOrganizationNumber\":\"ORG10462\",\"NamePath\":\"\",\"Level\":0,\"OrganizationType\":\"sale\",\"TreeOrder\":1,\"OrgLeader\":\"\",\"OrgLeaderNumber\":\"\",\"OrgLeaderName\":\"\",\"Managers\":[\"<EMAIL>\"],\"SaleBps\":null,\"ProductBps\":null,\"HrBps\":null,\"Employees\":[{\"RelID\":7982,\"Employee\":{\"ID\":679,\"Name\":\"熊刚\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"9677920\",\"LarkUserID\":\"53564dd4\",\"DepartmentID\":\"od-affb5626cc086dbe2a163df5f2dc07c7\",\"DepartmentName\":\"Data-基础架构-火山引擎平台架构-经营平台-业务管理\",\"JobFamily\":\"技术\",\"JoinTime\":1646352000000,\"ExpirationTime\":null,\"IsExpired\":false,\"Leader\":{\"Name\":\"徐渭\",\"Email\":\"<EMAIL>\",\"EmployeeNumber\":\"2926551\"}},\"Position\":\"sale\",\"IsLeader\":true,\"IsTransferOut\":false,\"TransferOutTime\":null,\"OrganizationNumber\":\"ORG10490\",\"OrganizationNamePath\":\"技术服务/测试专用/111\"}],\"ChildrenOrganization\":[],\"IsLeaf\":true,\"ParentOrganizationName\":\"\"}],\"IsLeaf\":false,\"ParentOrganizationName\":\"\"}],\"IsLeaf\":false,\"ParentOrganizationName\":\"\"}]"
// 			utils.JsonUnmarshalString(oStr, &organizationList)
// 			// Act
// 			got := LoopGetLeaderOrganizationNode(context.Background(), organizationList[0])
// 			fmt.Println(got)
// 			// Assert
// 			convey.So("your code", convey.ShouldEqual, "your code")
// 		})
// 	})
// }
