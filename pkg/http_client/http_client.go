package http_client

import (
	"context"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/retry"
	"code.byted.org/middleware/hertz/byted"
	"code.byted.org/middleware/hertz/byted/discovery"
	hertzClient "code.byted.org/middleware/hertz/pkg/app/client"
	httpRetry "code.byted.org/middleware/hertz/pkg/app/client/retry"
	"code.byted.org/middleware/hertz/pkg/protocol"
	"github.com/cloudwego/hertz/pkg/network/standard"
	"github.com/pkg/errors"
)

const (
	KiteLogID = "K_LOGID"
	HttpLogID = "X-TT-LOGID"
)

type HttpClientPool struct {
	httpClientPool sync.Pool
}

var defaultHttpClientPool HttpClientPool

func GetHttpClientPool() *HttpClientPool {
	return &defaultHttpClientPool
}

func init() {
	defaultHttpClientPool = HttpClientPool{
		httpClientPool: sync.Pool{New: func() interface{} {
			var httpClient *byted.Client
			err := retry.Do("byted.NewClient", 3, time.Second, func() error {
				var err error
				httpClient, err = byted.NewClient(byted.WithAppClientOptions(
					hertzClient.WithDialer(standard.NewDialer()),
					hertzClient.WithRetryConfig(httpRetry.WithMaxAttemptTimes(3)),
					hertzClient.WithMaxConnDuration(3*time.Minute),
					hertzClient.WithDialTimeout(time.Minute)),
				)
				if err != nil {
					logs.CtxError(context.Background(), "[client_pool/InitHttpClientPool] byted.NewClient err:%v", err)
					return err
				}

				return nil
			})

			if err != nil {
				logs.CtxError(context.Background(), "[client_pool/InitHttpClientPool] byted.NewClient err:%v", err)
				return err
			}
			return httpClient
		}},
	}
}

func (h *HttpClientPool) HttpPoolGet() (*byted.Client, error) {
	var cli *byted.Client
	err := retry.Do("get client", 3, time.Second, func() error {
		gotItem := h.httpClientPool.Get()
		if c, ok := gotItem.(*byted.Client); ok {
			cli = c
			return nil
		} else {
			return errors.Errorf("get client err: %+v", c)
		}
	})
	return cli, err
}

func (h *HttpClientPool) HttpPoolPut(client *byted.Client) {
	h.httpClientPool.Put(client)
}

type RequestParams struct {
	Host                string
	Path                string
	Method              string
	Headers             map[string]string `json:"Headers,omitempty"`
	Query               string
	Body                []byte            `json:"Body,omitempty"`
	FormatData          map[string]string `json:"FormatData,omitempty"`
	MultipartFormatData map[string]string `json:"MultipartFormatData,omitempty"`
	WithSD              bool
}

func DoRequest(c context.Context, params RequestParams) (*protocol.Response, error) {
	logs.CtxInfo(c, "[client_pool/DoRequest] req:%s", utils.JsonToString(params))

	httpClient, err := defaultHttpClientPool.HttpPoolGet()
	if err != nil {
		logs.CtxError(c, "[client_pool/DoRequest] HttpClientPool.HttpPoolGet() err:%v", err)
		return nil, err
	}
	defer func() {
		httpClient.CloseIdleConnections()
		defaultHttpClientPool.HttpPoolPut(httpClient)
	}()

	req := &protocol.Request{}
	resp := &protocol.Response{}

	req.SetMethod(params.Method)
	req.SetRequestURI(params.Host + params.Path)
	if params.WithSD {
		req.SetOptions(discovery.WithSD(true))
	}

	if len(params.Body) != 0 {
		//req.SetBodyRaw(params.Body)
		req.SetBody(params.Body)
	}

	if len(params.Query) != 0 {
		req.SetQueryString(params.Query)
	}

	if len(params.FormatData) != 0 {
		req.SetFormData(params.FormatData)
	}

	if len(params.Headers) != 0 {
		for k, v := range params.Headers {
			req.Header.Add(k, v)
		}
	}

	if logid, ok := c.Value(KiteLogID).(string); ok {
		req.Header.Set(HttpLogID, logid)
	}

	if len(params.MultipartFormatData) != 0 {
		req.SetMultipartFormData(params.MultipartFormatData)
	}

	err = httpClient.DoTimeout(c, req, resp, 10*time.Second)
	if err != nil {
		logs.CtxError(c, "[client_pool/DoRequest] client.HttpClient.Do err:%v", err)
		return nil, err
	}
	return resp, err
}
