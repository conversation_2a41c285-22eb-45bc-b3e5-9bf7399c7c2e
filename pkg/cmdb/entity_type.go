package cmdb

type Entity struct {
	Kind     string                 `json:"kind"`
	Metadata Metadata               `json:"metadata"`
	Spec     map[string]interface{} `json:"spec"`
}

type Metadata struct {
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Generation  string            `json:"generation"`
	Users       []string          `json:"users"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
}
