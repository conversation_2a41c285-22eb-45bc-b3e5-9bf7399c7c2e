package cmdb

import (
	"fmt"
	"strconv"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"github.com/samber/lo"
)

var VkeOpsEnvMap = map[string]int{
	"cn-beijing":           1,
	"cn-beijing2":          42,
	"cn-shanghai":          9,
	"cn-guangzhou":         8,
	"ap-southeast-1":       26,
	"cn-datong":            20,
	"cn-beijing-selfdrive": 78,
	"cn-hongkong":          84,
}

type DescribeResourceResponse struct {
	ResourceType        string                 `form:"ResourceType,required" json:"ResourceType" query:"ResourceType,required"`
	ResourceMetadata    []*ResourceEntry       `form:"ResourceMetadata,required" json:"ResourceMetadata" query:"ResourceMetadata,required"`
	ResourceNetworkInfo []*ResourceEntry       `form:"ResourceNetworkInfo" json:"ResourceNetworkInfo,omitempty" query:"ResourceNetworkInfo"`
	CustomerInfo        *CustomerInfo          `form:"CustomerInfo" json:"CustomerInfo" query:"CustomerInfo"`
	OriginMsg           interface{}            `form:"OriginMsg" json:"OriginMsg,omitempty" query:"OriginMsg"`
	OriginPlatformLink  string                 `form:"OriginPlatformLink" json:"OriginPlatformLink,omitempty" query:"OriginPlatformLink"`
	GrafanaLink         *string                `form:"GrafanaLink" json:"GrafanaLink,omitempty" query:"GrafanaLink"`
	ResponseMetadata    *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DescribeResourceDependencyResponse struct {
	InstanceID string     `form:"InstanceID" json:"InstanceID,omitempty" query:"InstanceID"`
	Products   []*Product `form:"Product" json:"Product,omitempty" query:"Product"`
}

type Product struct {
	ProductName string                   `form:"ProductName" json:"ProductName,omitempty" query:"ProductName"`
	Header      []*Field                 `form:"Header" json:"Header,omitempty" query:"Header"`
	Items       []map[string]interface{} `form:"Items" json:"Items,omitempty" query:"Items"`
}

type Field struct {
	Key  string `form:"Key" json:"Key,omitempty" query:"Key"`
	Name string `form:"Name" json:"Name,omitempty" query:"Name"`
}

type ResourceEntry struct {
	Key   string `form:"Key,required" json:"Key" query:"Key,required"`
	Name  string `form:"Name,required" json:"Name" query:"Name,required"`
	Value string `form:"Value" json:"Value,omitempty" query:"Value"`
}

type CustomerInfo struct {
	AccountId    *string `form:"AccountId" json:"AccountId,omitempty" query:"AccountId"`
	CustomerName *string `form:"CustomerName" json:"CustomerName,omitempty" query:"CustomerName"`
	CustomerLink *string `form:"CustomerLink" json:"CustomerLink,omitempty" query:"CustomerLink"`
}

func CreateEntry(key, name string, value interface{}) *ResourceEntry {
	// 处理指针类型值
	if v, ok := value.(*string); ok {
		value = lo.FromPtr(v)
	}

	// 转换为字符串
	var strValue string
	switch v := value.(type) {
	case string:
		strValue = v
	case int, int32, int64:
		strValue = fmt.Sprintf("%d", v)
	case *int: // 处理int指针类型
		if v != nil {
			strValue = fmt.Sprintf("%d", *v)
		}
	case *int64: // 处理int64指针类型
		if v != nil {
			strValue = fmt.Sprintf("%d", *v)
		}
	case bool:
		strValue = strconv.FormatBool(v)
	default:
		strValue = fmt.Sprintf("%v", value)
	}

	// 处理空值
	if strValue == "" || strValue == "<nil>" {
		strValue = "N/A"
	}

	return &ResourceEntry{
		Key:   key,
		Name:  name,
		Value: strValue,
	}
}

func AddField(key, name string) *Field {
	return &Field{
		Key:  key,
		Name: name,
	}
}

func AddValue[T any](value *T) string {
	if value != nil {
		return fmt.Sprintf("%v", *value)
	}
	return ""
}

func ConvertBillingType(billingType *int32) string {
	if billingType == nil {
		return "未知类型"
	}
	switch *billingType {
	case 1:
		return "包年包月"
	case 2:
		return "按量付费"
	default:
		return "未知类型"
	}
}
