package cmdb

import (
	"context"
	"net/http"
	"os"
	"testing"
)

func TestGetEntity(t *testing.T) {
	client := &CMDBClient{
		baseURL:     "https://reef.stable.innerapi.commonpcs-test.com",
		client:      &http.Client{},
		AccessKey:   os.Getenv("reef_ak"),
		SecretKey:   os.Getenv("reef_sk"),
		ServiceCode: "reef",
	}

	req := &GetEntityRequest{
		Kind: "VkeInstance",
		// Namespace:      "commonpcs-test",
		Name: "ccstj1dujv8peoh4etdtg",
	}

	resp, err := client.GetEntity(context.Background(), req)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)

}
