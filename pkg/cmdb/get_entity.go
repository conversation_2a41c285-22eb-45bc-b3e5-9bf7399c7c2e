package cmdb

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/url"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

type GetEntityRequest struct {
	Kind           string `json:"Kind"`
	Namespace      string `json:"Namespace"`
	Name           string `json:"Name"`
	EntityGlobalID string `json:"EntityGlobalID"`
}

type GetEntityResponse struct {
	Result           GetEntityResult        `json:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type GetEntityResult struct {
	Entity Entity `json:"Entity"`
}

func (c *CMDBClient) GetEntity(ctx context.Context, req *GetEntityRequest) (*GetEntityResponse, error) {
	getEntityRequest, err := c.createGetEntityRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	var resp GetEntityResponse
	output, err := c.apiRequest(ctx, getEntityRequest)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(output, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil

}

func (c *CMDBClient) createGetEntityRequest(ctx context.Context, request *GetEntityRequest) (*http.Request, error) {
	cmdbUrl, err := url.Parse(c.baseURL)
	if err != nil {
		return nil, err
	}
	params := url.Values{}
	params.Add("Action", "GetEntity")
	params.Add("Version", "2025-06-07")
	cmdbUrl.RawQuery = params.Encode()
	payload, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	return http.NewRequestWithContext(ctx, http.MethodPost, cmdbUrl.String(), bytes.NewBuffer(payload))
}
