package cmdb

import (
	"context"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
)

func TestVkeNodeMatch(t *testing.T) {
	tests := []struct {
		name     string
		input    *describe_resource.DescribeResourceRequest
		expected bool
	}{
		{
			name: "valid node format",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					ResourceType: "",                                // 显式设置资源类型为空
					Instances:    []string{"nd00h3r8nde11hh7afq2g"}, // 有效nd开头+18位字符（总长度20）
				},
			},
			expected: true,
		},
		{
			name: "invalid length",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"n123456789"},
				},
			},
			expected: false,
		},
		{
			name: "invalid prefix",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"d1234567890123456789"},
				},
			},
			expected: false,
		},
		{
			name: "exceeds max length",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"n123456789012345678901"}, // 21位超长无效
				},
			},
			expected: false,
		},
		{
			name: "multiple instances with partial valid",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{
						"invalid_node_id",       // 无效ID
						"n0000000000000000000",  // 纯数字
						"nd00h3r8nde11hh7afq2g", // 有效nd开头ID
					},
				},
			},
			expected: true,
		},
	}

	vn := &VkeNode{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := vn.Match(context.Background(), tt.input); got != tt.expected {
				t.Errorf("VkeNode.Match() = %v, want %v", got, tt.expected)
			}
		})
	}
}
