package cmdb

import (
	"testing"
)

func TestCreateEntry(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		nameVal  string
		value    interface{}
		expected *ResourceEntry
	}{
		{
			name:    "normal string value",
			key:     "instance_id",
			nameVal: "实例ID",
			value:   "i-1234567890abcdef0",
			expected: &ResourceEntry{
				Key:   "instance_id",
				Name:  "实例ID",
				Value: "i-1234567890abcdef0",
			},
		},
		{
			name:    "integer conversion",
			key:     "cpu_count",
			nameVal: "CPU核心数",
			value:   4,
			expected: &ResourceEntry{
				Key:   "cpu_count",
				Name:  "CPU核心数",
				Value: "4",
			},
		},
		{
			name:    "boolean conversion",
			key:     "is_active",
			nameVal: "激活状态",
			value:   true,
			expected: &ResourceEntry{
				Key:   "is_active",
				Name:  "激活状态",
				Value: "true",
			},
		},
		{
			name:    "nil pointer handling",
			key:     "owner",
			nameVal: "负责人",
			value:   (*string)(nil),
			expected: &ResourceEntry{
				Key:   "owner",
				Name:  "负责人",
				Value: "N/A",
			},
		},
		{
			name:    "empty string handling",
			key:     "description",
			nameVal: "描述",
			value:   "",
			expected: &ResourceEntry{
				Key:   "description",
				Name:  "描述",
				Value: "N/A",
			},
		},
		{
			name:    "struct conversion",
			key:     "metadata",
			nameVal: "元数据",
			value: struct {
				ID   int
				Name string
			}{ID: 1, Name: "test"},
			expected: &ResourceEntry{
				Key:   "metadata",
				Name:  "元数据",
				Value: "{1 test}",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CreateEntry(tt.key, tt.nameVal, tt.value)
			if got.Key != tt.expected.Key {
				t.Errorf("Key mismatch: expected %s, got %s", tt.expected.Key, got.Key)
			}
			if got.Name != tt.expected.Name {
				t.Errorf("Name mismatch: expected %s, got %s", tt.expected.Name, got.Name)
			}
			if got.Value != tt.expected.Value {
				t.Errorf("Value mismatch: expected %s, got %s", tt.expected.Value, got.Value)
			}
		})
	}
}
