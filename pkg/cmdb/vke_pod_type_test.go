package cmdb

import (
	"context"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
)

func TestVkePodMatch(t *testing.T) {
	tests := []struct {
		name     string
		input    *describe_resource.DescribeResourceRequest
		expected bool
	}{
		{
			name: "valid format",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnc1miol6uqt9ri5no0:default:debug-nas-69657684-rp9lw"},
				},
			},
			expected: true,
		},
		{
			name: "invalid segment count",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cc123456789012345678:default"},
				},
			},
			expected: false,
		},
		{
			name: "empty second part",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cc123456789012345678::pod-abc"},
				},
			},
			expected: false,
		},
		{
			name: "invalid prefix",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"abctnc1miol6uqt9ri5no0:default:invalid-pod-123"},
				},
			},
			expected: false,
		},
		{
			name: "multiple instances with one valid",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{
						"invalid-format",
						"cctnc1miol6uqt9ri5no0:namespace:valid-pod",
					},
				},
			},
			expected: true,
		},
	}

	vp := &VkePod{} // 结构体已在vke_pod_type.go中正确定义
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := vp.Match(context.Background(), tt.input); got != tt.expected {
				t.Errorf("VkePod.Match() = %v, want %v", got, tt.expected)
			}
		})
	}
}
