package cmdb

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type NatInstance struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"`
	Metadata   struct {
		Name      string `json:"name"`
		NameSpace string `json:"namespace"`
	} `json:"metadata"`
	Spec *NatInstanceSpec `json:"spec"`
}

type NatInstanceSpec struct {
	AccountID            string `json:"accountId"`
	Arch                 string `json:"arch"`
	BandwidthLimit       int64  `json:"bandwidthLimit"`
	BillingType          int64  `json:"billingType"`
	BusinessStatus       string `json:"businessStatus"`
	ClusterID            string `json:"clusterId"`
	CreatedAt            string `json:"createdAt"`
	Description          string `json:"description"`
	DirectMode           int64  `json:"directMode"`
	EniID                string `json:"eniId"`
	ExpiredTime          string `json:"expiredTime"`
	IsFirstNatGateway    int64  `json:"isFirstNatGateway"`
	LockMark             string `json:"lockMark"`
	LockReason           string `json:"lockReason"`
	NatGatewayID         string `json:"natGatewayId"`
	NatGatewayName       string `json:"natGatewayName"`
	NatGatewayType       string `json:"natGatewayType"`
	NetworkType          string `json:"networkType"`
	OverdueTime          string `json:"overdueTime"`
	ProjectName          string `json:"projectName"`
	ProjectTime          int64  `json:"projectTime"`
	ReaderCount          int64  `json:"readerCount"`
	Region               string `json:"region"`
	RouteEntryID         string `json:"routeEntryId"`
	SecurityGroupID      string `json:"securityGroupId"`
	SmartScheduleEnabled int64  `json:"smartScheduleEnabled"`
	SmartScheduleRule    string `json:"smartScheduleRule"`
	Spec                 string `json:"spec"`
	SpecFactor           int64  `json:"specFactor"`
	Status               string `json:"status"`
	SubnetID             string `json:"subnetId"`
	UpdatedAt            string `json:"updatedAt"`
	VpcID                string `json:"vpcId"`
	ZoneID               string `json:"zoneId"`
}

func (natIns *NatInstance) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(instanceID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "metadata.name",
				Value: instanceID,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "NatGateway",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (natIns *NatInstance) GetResources(ctx context.Context, c *app.RequestContext) error {
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		// ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := natIns.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrRequestParamInvalid.WithArgs("ListEntities response is empty")
	}
	natInstanceInfo := &NatInstance{}
	err = utils.JsonCopy(allItems[0], natInstanceInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}

	if natInstanceInfo.Spec != nil {
		BillingTypeMap := map[int64]string{
			1: "包年包月",
			2: "按量计费-按规格计费",
			3: "按量计费-按使用量计费",
		}

		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号名称", natInstanceInfo.Spec.AccountID),
			CreateEntry("NatGatewayName", "实例名称", natInstanceInfo.Spec.NatGatewayName),
			CreateEntry("Status", "状态", natInstanceInfo.Spec.Status),
			CreateEntry("NatGatewayID", "NatGatewayID", natInstanceInfo.Spec.NatGatewayID),
			CreateEntry("CreatedAt", "创建时间", natInstanceInfo.Spec.CreatedAt),
			CreateEntry("ExpiredTime", "过期时间", natInstanceInfo.Spec.ExpiredTime),
			CreateEntry("BandwidthLimit", "带宽", strconv.FormatInt(natInstanceInfo.Spec.BandwidthLimit, 10)+" Kbps"),
			CreateEntry("Spec", "规格", natInstanceInfo.Spec.Spec),
			CreateEntry("NetworkType", "网络类型", natInstanceInfo.Spec.NetworkType),
			CreateEntry("BillingType", "计费类型", BillingTypeMap[natInstanceInfo.Spec.BillingType]),
			CreateEntry("Region", "地域", natInstanceInfo.Spec.Region),
		}

		resourceNetworkInfo := []*ResourceEntry{
			CreateEntry("EniId", "EniID", natInstanceInfo.Spec.EniID),
			CreateEntry("SecurityGroupID", "安全组", natInstanceInfo.Spec.SecurityGroupID),
			CreateEntry("SubnetID", "子网", natInstanceInfo.Spec.SubnetID),
			CreateEntry("VpcId", "VpcID", natInstanceInfo.Spec.VpcID),
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		customerInfo := &CustomerInfo{
			AccountId: &natInstanceInfo.Spec.AccountID,
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{natInstanceInfo.Spec.AccountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", natInstanceInfo.Spec.AccountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", natInstanceInfo.Spec.AccountID))
			}
		}

		networkInstanceInfoResp := &DescribeResourceResponse{
			ResourceType:        "natID",
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: resourceNetworkInfo,
			CustomerInfo:        customerInfo,
			OriginMsg:           natInstanceInfo,
			OriginPlatformLink:  fmt.Sprintf("https://vnet.byted.org/nat/%s?region=%s&tab=detail", natInstanceInfo.Spec.NatGatewayID, natInstanceInfo.Spec.Region),
		}

		// 获取Grafana链接
		if url := natIns.getGrafanaURL(ctx, natInstanceInfo); url != nil {
			networkInstanceInfoResp.GrafanaLink = url
			networkInstanceInfoResp.ResourceMetadata = append(networkInstanceInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}

		ehttp.DoResponse(ctx, c, networkInstanceInfoResp)
	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrRequestParamInvalid.WithArgs("natInstanceInfo is empty")
	}

	return nil
}

// getNatInstanceDetail 获取NAT实例详情
func (natIns *NatInstance) getNatInstanceDetail(ctx context.Context, instanceID string) (*NatInstance, error) {
	listEntitiesRequest, err := natIns.BuildListEntitiesRequest(ctx, []string{instanceID})
	if err != nil {
		return nil, fmt.Errorf("build list entities request failed: %v", err)
	}
	listEntitiesRequest.MaxResults = 100

	listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
	if err != nil {
		return nil, fmt.Errorf("list entities failed: %v", err)
	}

	if len(listEntitiesResponse.Items) == 0 {
		return nil, fmt.Errorf("nat instance not found")
	}

	instance := &NatInstance{}
	if err := utils.JsonCopy(listEntitiesResponse.Items[0], instance); err != nil {
		return nil, fmt.Errorf("parse nat instance failed: %v", err)
	}

	return instance, nil
}

func (natIns *NatInstance) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {

	// logger := utils.NewModuleLogger("GetNatInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	natInstanceInfo, err := GetNATResource(ctx, ins)
	if err != nil {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if natInstanceInfo.Spec != nil {
		result := DescribeResourceDependencyResponse{}
		if natInstanceInfo.Spec.NatGatewayID != "" {
			result.InstanceID = natInstanceInfo.Spec.NatGatewayID
		}

		//获取NAT的信息返回数据，生成返回信息
		NAT := &Product{}
		NAT.ProductName = "网卡"
		// NAT.Header = []*Field{
		// 	AddField("ResourceID", "资源ID"),
		// 	AddField("ResourceName", "资源名称"),
		// 	AddField("Region", "地域"),
		// 	AddField("AccountID", "账号ID"),
		// 	AddField("EniId", "EniID"),
		// 	AddField("SecurityGroupID", "安全组"),
		// 	AddField("SubnetID", "子网"),
		// 	AddField("VpcId", "VpcId"),
		// }
		// NAT.Items = []map[string]interface{}{
		// 	{
		// 		"ResourceID":      natInstanceInfo.Spec.NatGatewayID,
		// 		"ResourceName":    natInstanceInfo.Spec.NatGatewayName,
		// 		"Region":          natInstanceInfo.Spec.Region,
		// 		"AccountID":       natInstanceInfo.Spec.AccountID,
		// 		"EniId":           natInstanceInfo.Spec.EniID,
		// 		"SecurityGroupID": natInstanceInfo.Spec.SecurityGroupID,
		// 		"SubnetID":        natInstanceInfo.Spec.SubnetID,
		// 		"VpcId":           natInstanceInfo.Spec.VpcID,
		// 	},
		// }
		NAT.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("SecurityGroupID", "安全组"),
			AddField("SubnetID", "子网"),
			AddField("VpcId", "VpcId"),
		}
		NAT.Items = []map[string]interface{}{
			{
				"ResourceID":      natInstanceInfo.Spec.EniID,
				"SecurityGroupID": natInstanceInfo.Spec.SecurityGroupID,
				"SubnetID":        natInstanceInfo.Spec.SubnetID,
				"VpcId":           natInstanceInfo.Spec.VpcID,
			},
		}
		result.Products = append(result.Products, NAT)

		eipInstanceInfo, err := GetEIPResourceBind(ctx, ins)
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return errorcode.ErrCommonInternalError.WithArgs(err)
		}
		if eipInstanceInfo != nil {
			if eipInstanceInfo.Spec != nil {
				//获取EIP的信息返回数据，生成返回信息
				EIP := &Product{}
				EIP.ProductName = "公网IP"
				EIP.Header = []*Field{
					AddField("ResourceID", "资源ID"),
					AddField("EipAddress", "公网IP"),
					AddField("Bandwidth", "带宽"),
					AddField("BandwidthPackageId", "绑定带宽包ID"),
				}
				EIP.Items = []map[string]interface{}{
					{
						"ResourceID":         eipInstanceInfo.Spec.EipId,
						"EipAddress":         eipInstanceInfo.Spec.EipAddress,
						"Bandwidth":          strconv.FormatInt(eipInstanceInfo.Spec.Bandwidth, 10) + " Mbps",
						"BandwidthPackageId": eipInstanceInfo.Spec.BandwidthPackageId,
					},
				}
				result.Products = append(result.Products, EIP)
			}
		}
		ehttp.DoResponse(ctx, c, result)
	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrDataNotFound
	}

	return nil
}

func (natIns *NatInstance) getGrafanaURL(ctx context.Context, instance *NatInstance) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, natIns.GetResourceType())
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region": instance.Spec.Region,
		"NatID":  instance.Spec.NatGatewayID,
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (natIns *NatInstance) GetResourceType() string {
	return "natID"
}

func (natIns *NatInstance) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == natIns.GetResourceType()
	}

	regex := regexp.MustCompile(`^ngw-[a-z0-9]{15,29}$`)
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		}
	}

	return false
}
