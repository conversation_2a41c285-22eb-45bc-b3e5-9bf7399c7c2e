package cmdb

import (
	"context"
	"errors"

	"code.byted.org/iaasng/volcengine-go-ops-sdk/ops"
	"code.byted.org/iaasng/volcengine-go-ops-sdk/service/ecsops"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

func GetEcsResource(ctx context.Context, ins string) (*ecsops.OpsFusionSearchOutput, error) {

	// 从tcc获取ak sk
	config := tcc.GetServiceConfig()
	sess, err := session.NewSession(ops.DefaultConfig(config.EcsAccount.AK, config.EcsAccount.SK))
	if err != nil {
		return nil, err
	}

	ecsClient := ecsops.New(sess)

	respData, err := ecsClient.OpsFusionSearch(&ecsops.OpsFusionSearchInput{KeyWord: &ins})
	if err != nil {
		return nil, err
	}

	if respData.Metadata.HTTPCode == 404 {
		return nil, errors.New(respData.Metadata.Error.Message)
	}

	// 判断 HTTPCode 是否为 200
	if respData.Metadata.HTTPCode != 200 {
		return nil, errors.New(respData.Metadata.Error.Message)
	}
	return respData, nil
}

func GetVkeCLusterResource(ctx context.Context, ins []string) (*VKEClusterInstance, error) {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vc := &VKEClusterInstance{}
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vc)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vc, nil
}

func GetVkeNodeResource(ctx context.Context, ins []string) (*VkeNode, error) {
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vn := &VkeNode{}
	for {
		listEntitiesRequest, err := vn.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vn)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vn, nil
}

func GetVkeNodePoolResource(ctx context.Context, ins []string) (*VkeNodePool, error) {
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vnp := &VkeNodePool{}
	for {
		listEntitiesRequest, err := vnp.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vnp)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		return nil, err
	}
	return vnp, nil
}

func GetVkePodResource(ctx context.Context, ins []string) (*VkePod, error) {
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vp := &VkePod{}
	for {
		listEntitiesRequest, err := vp.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vp)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vp, nil
}

func GetCLBResource(ctx context.Context, ins []string) (*CLBInstance, error) {
	// logger := utils.NewModuleLogger("GetCLBResources")
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vc := &CLBInstance{}
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vc)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vc, nil
}

func GetEIPResource(ctx context.Context, ins []string) (*EipInstance, error) {
	// logger := utils.NewModuleLogger("GetEIPResources")
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vc := &EipInstance{}
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vc)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vc, nil
}

func GetEIPResourceBind(ctx context.Context, ins []string) (*EipInstance, error) {
	// logger := utils.NewModuleLogger("GetEIPResourceBind")
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vc := &EipInstance{}
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequestBind(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, nil
	}
	err := utils.JsonCopy(allItems[0], vc)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vc, nil
}

func GetNATResource(ctx context.Context, ins []string) (*NatInstance, error) {
	// logger := utils.NewModuleLogger("GetNATResources")
	allItems := []interface{}{}
	pageNumber := int32(1)
	pageSize := int32(100)
	vc := &NatInstance{}
	for {
		listEntitiesRequest, err := vc.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return nil, err
		}
		listEntitiesRequest.PageNumber = pageNumber
		listEntitiesRequest.PageSize = pageSize

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			// logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		pageNumber++
	}
	if len(allItems) == 0 {
		return nil, errors.New("data is not found")
	}

	err := utils.JsonCopy(allItems[0], vc)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)

		return nil, err
	}
	return vc, nil
}
