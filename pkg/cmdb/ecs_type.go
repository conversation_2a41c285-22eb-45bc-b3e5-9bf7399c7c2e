package cmdb

import "time"

type EcsInstance struct {
	Instance     InstanceSpec `json:"Instance"`
	KeyWord      string       `json:"KeyWord"`
	Region       string       `json:"Region"`
	ResourceType string       `json:"ResourceType"`
	// ...其他字段...
}

type InstanceSpec struct {
	AccountId       string              `json:"AccountId"`
	Cluster         map[string]string   `json:"Cluster"`
	CreatedAt       time.Time           `json:"CreatedAt"`
	DeploymentSetId string              `json:"DeploymentSetId"`
	Description     string              `json:"Description"`
	EipAddress      EcsEipAddressSpec   `json:"EipAddress"`
	GPUAmount       int                 `json:"GPUAmount"`
	GPUModel        string              `json:"GPUModel"`
	GPUSize         int                 `json:"GPUSize"`
	Host            EcsHostSpec         `json:"Host"`
	HostName        string              `json:"HostName"`
	Id              string              `json:"Id"`
	ImageId         string              `json:"ImageId"`
	InstanceConfig  string              `json:"InstanceConfig"`
	InstanceType    EcsInstanceTypeSpec `json:"InstanceType"`
	KeyPairId       string              `json:"KeyPairId"`
	KeyPairName     string              `json:"KeyPairName"`
}

type EcsHostSpec struct {
	Id         string `json:"Id"`
	BizeStatus string `json:"BizeStatus"`
	Evacuable  bool   `json:"Evacuable"`
	Ipv4       string `json:"Ipv4"`
	Ipv6       string `json:"Ipv6"`
	Name       string `json:"Name"`
	Status     string `json:"Status"`
	Type       string `json:"Type"`
}

type EcsEipAddressSpec struct {
	AllocationId string `json:"AllocationId"`
	Bandwidth    string `json:"Bandwidth"`
	IpAddress    string `json:"IpAddress"`
}

type EcsInstanceTypeSpec struct {
	Architecture                string                `json:"Architecture"`
	Cpu                         int                   `json:"Cpu"`
	Id                          string                `json:"Id"`
	InstanceTypeFamily          string                `json:"InstanceTypeFamily"`
	LocalVolumes                interface{}           `json:"LocalVolumes"`
	Mem                         int64                 `json:"Mem"`
	NetKppsQuota                int64                 `json:"NetKppsQuota"`
	NetMbpsQuota                int64                 `json:"NetMbpsQuota"`
	NetSessionQuota             int64                 `json:"NetSessionQuota"`
	NetworkInterfaceNumQuota    int64                 `json:"NetworkInterfaceNumQuota"`
	NetworkType                 string                `json:"NetworkType"`
	PrivateIpQuota              int64                 `json:"PrivateIpQuota"`
	Type                        string                `json:"Type"`
	VolumeTypes                 []string              `json:"VolumeTypes"`
	NetworkInterfaces           []EcsNetworkInterface `json:"NetworkInterfaces"`
	OsName                      string                `json:"OsName"`
	OsType                      string                `json:"OsType"`
	ProjectName                 string                `json:"ProjectName"`
	RdmaIpAddresses             interface{}           `json:"RdmaIpAddresses"`
	ScheduledInstanceId         string                `json:"ScheduledInstanceId"`
	SecurityEnhancementStrategy string                `json:"SecurityEnhancementStrategy"`
	SpotPriceLimit              int                   `json:"SpotPriceLimit"`
	SpotStrategy                string                `json:"SpotStrategy"`
	Status                      string                `json:"Status"`
	UpdateAt                    time.Time             `json:"UpdateAt"`
	UserData                    string                `json:"UserData"`
	Uuid                        string                `json:"Uuid"`
	Volumes                     []EcsVolume           `json:"Volumes"`
	VpcId                       string                `json:"VpcId"`
	ZoneId                      string                `json:"ZoneId"`
	Tags                        []struct {
		Key   string `json:"Key"`
		Value string `json:"Value"`
	} `json:"Tags"`
}

type EcsNetworkInterface struct {
	AllocationId         string   `json:"AllocationId"`
	EipAddress           string   `json:"EipAddress"`
	MacAddress           string   `json:"MacAddress"`
	NetworkInterfaceId   string   `json:"NetworkInterfaceId"`
	NetworkInterfaceName string   `json:"NetworkInterfaceName"`
	PrimaryIpAddress     string   `json:"PrimaryIpAddress"`
	SecurityGroupIds     []string `json:"SecurityGroupIds"`
	Status               string   `json:"Status"`
	SubnetId             string   `json:"SubnetId"`
	Type                 string   `json:"Type"`
	VpcId                string   `json:"VpcId"`
	VpcName              string   `json:"VpcName"`
}

type EcsVolume struct {
	BillingType int    `json:"BillingType"`
	DeletedTime string `json:"DeletedTime"`
	ExpiredTime string `json:"ExpiredTime"`
	ImageId     string `json:"ImageId"`
	Kind        string `json:"Kind"`
	OverdueTime string `json:"OverdueTime"`
	PayType     string `json:"PayType"`
	Size        int    `json:"Size"`
	Status      string `json:"Status"`
	TradeStatus string `json:"TradeStatus"`
	VolumeId    string `json:"VolumeId"`
	VolumeName  string `json:"VolumeName"`
	VolumeType  string `json:"VolumeType"`
}
