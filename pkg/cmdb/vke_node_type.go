package cmdb

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	paasdiagnose "code.byted.org/volcengine-support/cloud-sherlock/pkg/paas"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type VkeNode struct {
	ApiVersion string `json:"apiVersion"`
	Kind       string `json:"kind"` // VkeNode
	Metadata   struct {
		Name        string            `json:"name"`
		Namespace   string            `json:"namespace"`
		Annotations map[string]string `json:"annotations"`
	}

	Spec *VkeNodeSpec `json:"spec"`
}
type VkeNodeSpec struct {
	AccountId    int64    `json:"accountId"`
	ClusterId    string   `json:"clusterId"`
	Cordon       bool     `json:"cordon"`
	CreateTime   string   `json:"createTime"`
	DeleteAt     string   `json:"deleteAt"`
	Id           int64    `json:"id"`
	ImageId      string   `json:"imageId"`
	ImageType    string   `json:"imageType"`
	InstanceId   string   `json:"instanceId"`
	IsVirtual    bool     `json:"isVirtual"`
	Labels       string   `json:"labels"` // strings format []map[string]string
	Name         string   `json:"name"`
	NodeId       string   `json:"nodeId"`
	NodePoolId   string   `json:"nodePoolId"`
	Region       string   `json:"region"`
	Role         []string `json:"role"` // Worker,Master
	SpotStrategy string   `json:"spotStrategy"`
	StatusPhase  string   `json:"statusPhase"`
	Taints       string   `json:"taints"` // strings format is []map[string]string
	UpdateTime   string   `json:"updateTime"`
	ZoneId       string   `json:"zoneId"`
}

func (vn *VkeNode) BuildDiagnoseRequest(ctx context.Context,
	scenario, ticketID string, instanceInfo interface{}) (*paasdiagnose.CreateDiagnoseTaskRequest, error) {
	logger := utils.NewModuleLogger("BuildVkeNodeDiagnoseRequest")
	if err := utils.JsonCopy(instanceInfo, vn); err != nil {
		logger.CtxError(ctx, "VkeNode JSON解析失败: %s | 原始数据: %s", err, utils.JsonToString(instanceInfo))
		return nil, err
	}

	// 增加字段校验
	if strings.TrimSpace(vn.Spec.Region) == "" {
		logger.CtxWarn(ctx, "VkeNode 缺失必要字段 Region=%s ",
			vn.Spec.Region)
		return nil, errors.New("VkeNode 缺失必要字段: Region")
	}

	region := strings.TrimSpace(vn.Spec.Region)
	logger.CtxInfo(ctx, "vkeNodeInfo 解析成功 Region=%s ",
		region)

	diagnoseRequestBody := &paasdiagnose.CreateDiagnoseTaskRequest{
		TargetType: "VkeNode",
		Region:     region,
		Identities: []string{vn.Spec.NodeId},
		Scenario:   scenario,
		OrderId:    ticketID,
	}
	return diagnoseRequestBody, nil

}

func (vn *VkeNode) BuildListEntitiesRequest(ctx context.Context, instances []string) (*ListEntitiesRequest, error) {
	ins := lo.FilterMap(instances, func(machineID string, index int) (FieldFilter, bool) {
		return []FieldFilterItem{
			{
				Field: "spec.nodeId",
				Value: machineID,
			},
		}, true
	})
	listResourcesRequest := &ListEntitiesRequest{
		Kind: "VkeNode",
		Filter: ListEntitiesFilter{
			FieldFilters: ins,
		},
	}
	return listResourcesRequest, nil
}

func (vn *VkeNode) GetResourceType() string {
	return "VkeNode"
}

func (vn *VkeNode) GetResources(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	allItems := []interface{}{}
	var nextToken string
	for {
		listEntitiesRequest, err := vn.BuildListEntitiesRequest(ctx, ins)
		if err != nil {
			// logger.CtxWarn(ctx, "BuildListEntitiesRequest error: %s", err)
			return err
		}
		listEntitiesRequest.MaxResults = 100
		listEntitiesRequest.NextToken = nextToken

		listEntitiesResponse, err := GetCMDBClient().ListEntities(ctx, listEntitiesRequest)
		if err != nil {
			// logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if listEntitiesResponse.NextToken == "EOF" {
			break
		}
		nextToken = listEntitiesResponse.NextToken
	}
	if len(allItems) == 0 {
		// logger.CtxWarn(ctx, "ListEntities response is empty")
		// ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		return errorcode.ErrRequestParamInvalid.WithArgs("ListEntities response is empty")
	}
	vkeNodeInfo := &VkeNode{}

	err = utils.JsonCopy(allItems[0], vkeNodeInfo)
	if err != nil {
		// logger.CtxWarn(ctx, "JsonCopy error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkeNodeInfo.Spec != nil {

		resourceMetadata := []*ResourceEntry{
			CreateEntry("AccountId", "账号ID", vkeNodeInfo.Spec.AccountId),
			CreateEntry("clusterId", "VKE集群", vkeNodeInfo.Spec.ClusterId),
			CreateEntry("Cordon", "封锁状态", vkeNodeInfo.Spec.Cordon),
			CreateEntry("CreateTIme", "创建时间", vkeNodeInfo.Spec.CreateTime),
			CreateEntry("UpdateTime", "更新时间", vkeNodeInfo.Spec.UpdateTime),
			CreateEntry("ImageId", "OS镜像ID", vkeNodeInfo.Spec.ImageId),
			CreateEntry("ImageType", "镜像类型", vkeNodeInfo.Spec.ImageType),
			CreateEntry("ECSId", "ECS-ID", vkeNodeInfo.Spec.InstanceId),
			CreateEntry("IsVirtual", "是否是虚拟节点", vkeNodeInfo.Spec.IsVirtual),
			CreateEntry("NodeName", "节点名称", vkeNodeInfo.Spec.Name),
			CreateEntry("NodeId", "节点ID", vkeNodeInfo.Spec.NodeId),
			CreateEntry("NodePoolId", "节点池ID", vkeNodeInfo.Spec.NodePoolId),
			CreateEntry("Region", "地域", vkeNodeInfo.Spec.Region),
			CreateEntry("Status", "状态", vkeNodeInfo.Spec.StatusPhase),
			CreateEntry("ZoneId", "可用区", vkeNodeInfo.Spec.ZoneId),
			CreateEntry("Taint", "污点", vkeNodeInfo.Spec.Taints),
			CreateEntry("Labels", "标签", vkeNodeInfo.Spec.Labels),
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		accountID := fmt.Sprintf("%d", vkeNodeInfo.Spec.AccountId)
		customerInfo := &CustomerInfo{
			AccountId: lo.ToPtr(accountID),
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{accountID},
		})
		if err != nil {
			// ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrUpStreamSystemError.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", accountID))
			}
		}

		vkeNodeInfoResp := &DescribeResourceResponse{
			ResourceType:        vkeNodeInfo.GetResourceType(),
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: []*ResourceEntry{},
			CustomerInfo:        customerInfo,
			OriginMsg:           vkeNodeInfo,
		}
		// 获取Grafana链接
		if url := vn.getGrafanaURL(ctx, vkeNodeInfo); url != nil {
			vkeNodeInfoResp.GrafanaLink = url
			vkeNodeInfoResp.ResourceMetadata = append(vkeNodeInfoResp.ResourceMetadata,
				CreateEntry("GrafanaLink", "监控链接", *url))
		}
		ehttp.DoResponse(ctx, c, vkeNodeInfoResp)
	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodeInfo.Spec is nil"))
		return errorcode.ErrCommonInternalError.WithArgs("vkeNodeInfo.Spec is nil")
	}

	return nil

}

func (vn *VkeNode) Match(ctx context.Context, inputInfo *describe_resource.DescribeResourceRequest) bool {
	// 优先检查明确指定的resourceType
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return strings.TrimSpace(inputInfo.Resources.ResourceType) == vn.GetResourceType()
	}

	// 当resourceType为空时，进行实例ID正则匹配
	nodeIDRegex := regexp.MustCompile(`(?i)^n[a-z0-9]{20}$`) // 匹配n开头且总长度21的字母数字组合
	for _, resource := range inputInfo.Resources.Instances {
		matched := nodeIDRegex.MatchString(resource)
		println("Checking:", resource, "Length:", len(resource), "Matched:", matched) // 添加调试输出
		if matched {
			return true
		}
	}

	return false
}

func (vn *VkeNode) getGrafanaURL(ctx context.Context, instance *VkeNode) *string {
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, "vkeNode")
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		return nil
	}

	data := map[string]string{
		"Region":       instance.Spec.Region,
		"VkeClusterID": instance.Spec.ClusterId,
		"AccountID":    fmt.Sprintf("%d", instance.Spec.AccountId),
	}
	url, err := utils.RenderGrafanaTemplate(*productCategory.GrafanaTemplate, data)
	if err != nil {
		logger.CtxWarnf(ctx, "render grafana template failed: %v", err)
		return nil
	}
	return &url
}

func (vn *VkeNode) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// logger := utils.NewModuleLogger("GetVkeInstanceResources")
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		ehttp.DoResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}
	ins := params.Resources.Instances
	vkeNodeInfo, err := GetVkeNodeResource(ctx, ins)
	if err != nil {
		// logger.CtxWarn(ctx, "GetVkeNodeResource error: %s", err)
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
		return errorcode.ErrCommonInternalError.WithArgs(err)
	}
	if vkeNodeInfo.Spec != nil {
		//
		result := DescribeResourceDependencyResponse{}

		//获取vkeInstance的信息，生成返回数据
		vkeCluster := &Product{}
		vkeCluster.ProductName = "VKE集群"
		vkeCluster.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源名称"),
			AddField("Region", "地域"),
			AddField("AccountID", "账号ID"),
			AddField("KubernetesVersion", "Kubernetes版本"),
			AddField("SecurityGroupIds", "安全组"),
			AddField("PodNetworkMode", "网络模型"),
		}
		vkeClusterInfo, err := GetVkeCLusterResource(ctx, []string{vkeNodeInfo.Spec.ClusterId})
		if err != nil {
			// logger.CtxWarn(ctx, "GetVkeCLusterResource error: %s", err)
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return errorcode.ErrCommonInternalError.WithArgs(err)
		}
		vkeCluster.Items = []map[string]interface{}{
			{
				"ResourceID":        vkeClusterInfo.Spec.ClusterId,
				"ResourceName":      vkeClusterInfo.Spec.Name,
				"Region":            vkeClusterInfo.Spec.Region,
				"AccountID":         vkeClusterInfo.Spec.AccountId,
				"KubernetesVersion": vkeClusterInfo.Spec.KubernetesVersion,
				"SecurityGroupIds":  vkeClusterInfo.Spec.SecurityGroupIds,
				"PodNetworkMode":    vkeClusterInfo.Spec.PodNetworkMode,
			},
		}
		result.Products = append(result.Products, vkeCluster)

		//获取vkeNodePool的信息，生成返回数据
		vkeNodePool := &Product{}
		vkeNodePool.ProductName = "VKE节点池"
		vkeNodePool.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("ResourceName", "资源名称"),
			AddField("AccountID", "账号ID"),
			AddField("ClusterID", "集群ID"),
			AddField("InstanceTypeIds", "节点规格"),
		}
		vkeNodePoolInfo, err := GetVkeNodePoolResource(ctx, []string{vkeNodeInfo.Spec.NodePoolId})
		if err != nil {
			// logger.CtxWarn(ctx, "GetVkeNodePoolResource error: %s", err)
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return errorcode.ErrCommonInternalError.WithArgs(err)
		}
		vkeNodePool.Items = []map[string]interface{}{
			{
				"ResourceID":      vkeNodePoolInfo.Spec.NodePoolId,
				"ResourceName":    vkeNodePoolInfo.Spec.Name,
				"AccountID":       vkeNodePoolInfo.Spec.AccountId,
				"ClusterID":       vkeNodePoolInfo.Spec.ClusterId,
				"InstanceTypeIds": strings.Join(vkeNodePoolInfo.Spec.InstanceTypeIds, ","),
			},
		}
		result.Products = append(result.Products, vkeNodePool)

		//获取Ecs的信息，生成返回数据
		ecs := &Product{}
		ecs.ProductName = "云服务器"
		ecs.Header = []*Field{
			AddField("ResourceID", "资源ID"),
			AddField("AccountID", "账号ID"),
			AddField("Region", "地域"),
			AddField("Description", "描述"),
			AddField("VpcID", "VpcID"),
			AddField("EIPAddress", "公网IP"),
		}
		ecsInfo, err := GetEcsResource(ctx, vkeNodeInfo.Spec.InstanceId)
		if err != nil {
			// logger.CtxWarn(ctx, "GetEcsResource error: %s", err)
			// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(err))
			return errorcode.ErrCommonInternalError.WithArgs(err)
		}
		ecs.Items = []map[string]interface{}{
			{
				"ResourceID":  AddValue(ecsInfo.Instance.Id),
				"AccountID":   AddValue(ecsInfo.Instance.AccountId),
				"Region":      AddValue(ecsInfo.Region),
				"Description": AddValue(ecsInfo.Instance.Description),
				"VpcID":       AddValue(ecsInfo.Instance.VpcId),
				"EIPAddress":  AddValue(ecsInfo.Instance.EipAddress),
			},
		}
		result.Products = append(result.Products, ecs)

		ehttp.DoResponse(ctx, c, result)
	} else {
		// ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs("vkeNodeInfo.Spec  is nil"))
		return errorcode.ErrCommonInternalError.WithArgs("vkeNodeInfo.Spec  is nil")
	}
	return nil
}

// 从诊断结果中获取实例的基本信息instanceID、instanceName、region
func (vn *VkeNode) GetInstanceInfoFromReport(data string) (instanceID, instanceName, region string, err error) {
	targetData := &VkeNode{}
	err = utils.JsonUnmarshalString(data, targetData)
	if err != nil {
		return "", "", "", fmt.Errorf("unmarshal VkeNode data error: %w", err)
	}
	instanceID = targetData.Spec.NodeId
	instanceName = targetData.Spec.Name
	region = targetData.Spec.Region
	return
}
