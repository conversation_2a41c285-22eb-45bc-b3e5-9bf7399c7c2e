package cmdb_test

import (
	"context"
	"testing"

	describe_resource "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
)

func TestVKEClusterInstanceMatch(t *testing.T) {
	tests := []struct {
		name     string
		input    *describe_resource.DescribeResourceRequest
		expected bool
	}{
		{
			name: "明确指定resourceType匹配",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					ResourceType: "VkeInstance",
				},
			},
			expected: true,
		},
		{
			name: "空resourceType_有效实例ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnc1miol6uqt9ri5no0"}, // 有效ID：21位小写字母数字，cc开头
				},
			},
			expected: true,
		},
		{
			name: "混合有效无效实例ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"invalid_id", "cctnc1miol6uqt9ri5no0"}, // 有效ID + 无效ID组合
				},
			},
			expected: true,
		},
		{
			name: "resourceType不匹配_无效实例ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					ResourceType: "WrongType",
					Instances:    []string{"invalid_id"},
				},
			},
			expected: false,
		},
		{
			name: "空resourceType_无效实例ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"invalid_id"},
				},
			},
			expected: false,
		},
		// 新增严格格式测试用例
		{
			name: "有效21位小写字母数字ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnc1miol6uqt9ri5no0"},
				},
			},
			expected: true,
		},
		{
			name: "无效前缀ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cxtnc1miol6uqt9ri5no0"},
				},
			},
			expected: false,
		},
		{
			name: "长度不足ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnc1miol6uqt9ri5n"},
				},
			},
			expected: false,
		},
		{
			name: "包含大写字母ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnC1miol6uqt9ri5no0"},
				},
			},
			expected: false,
		},
		{
			name: "包含特殊字符ID",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"cctnc1miol6uqt9ri5n_0"},
				},
			},
			expected: false,
		},
	}

	vkeHandler := &cmdb.VKEClusterInstance{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := vkeHandler.Match(context.Background(), tt.input)
			if result != tt.expected {
				t.Errorf("预期 %v 但得到 %v，输入参数：%+v", tt.expected, result, tt.input)
			}
		})
	}
}
