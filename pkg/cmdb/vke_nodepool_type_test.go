package cmdb

import (
	"context"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
)

func TestVKENodePoolTypeMatch(t *testing.T) {
	tests := []struct {
		name  string
		input *describe_resource.DescribeResourceRequest
		want  bool
	}{
		{
			name: "valid node pool id",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"p12345678901234567890"}, // 符合 ^p[a-z0-9]{20}$ 格式
				},
			},
			want: true,
		},
		{
			name: "invalid character",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"p@invalidcharacters"}, // 包含非法字符@
				},
			},
			want: false,
		},
		{
			name: "too short",
			input: &describe_resource.DescribeResourceRequest{
				Resources: &describe_resource.Resources{
					Instances: []string{"p1234"}, // 长度不足21字符
				},
			},
			want: false,
		},
	}

	vnp := &VkeNodePool{}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := vnp.Match(context.TODO(), tt.input)
			if got != tt.want {
				t.Errorf("Match() = %v, want %v", got, tt.want)
			}
		})
	}
}
