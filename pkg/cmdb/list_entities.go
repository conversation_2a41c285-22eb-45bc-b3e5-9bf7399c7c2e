package cmdb

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/url"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

const (
	OperatorEq      OperatorType = "="
	OperatorNotEq   OperatorType = "!="
	OperatorIn      OperatorType = "in"
	OperatorNotIn   OperatorType = "not in"
	OperatorLike    OperatorType = "like"
	OperatorNotLike OperatorType = "not like"
)

type OperatorType string

type ListEntitiesRequest struct {
	PageNumber int32
	PageSize   int32
	NextToken  string
	MaxResults int
	Kind       string
	Filter     ListEntitiesFilter
	OrderBy    []OrderByItem
}

type ListEntitiesResponse struct {
	PageNumber int32
	PageSize   int32
	TotalCount int64
	Items      []interface{}
	NextToken  string
}

type FieldFilterItem struct {
	Field    string
	Value    string
	Operator OperatorType
	RefKind  string
}

type FieldFilter []FieldFilterItem

type ListEntitiesFilter struct {
	FieldFilters []FieldFilter
}

type OrderByItem struct {
	Field string
	Desc  bool
}

func (c *CMDBClient) ListEntities(ctx context.Context, req *ListEntitiesRequest) (*ListEntitiesResponse, error) {
	ListEntitiesRequest, err := c.createListEntitiesRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	var resp ListEntitiesResponse
	output, err := c.apiRequest(ctx, ListEntitiesRequest)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(output, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil

}

func (c *CMDBClient) ListEntitiesWithPage(ctx context.Context, req *ListEntitiesRequest, pageNum, pageSize int32) ([]interface{}, error) {
	logger := utils.NewModuleLogger("ListEntitiesWithPage")
	cmdbPageNum := pageNum
	cmdbPageSize := pageSize
	allItems := []interface{}{}

	for {
		req.PageNumber = cmdbPageNum
		req.PageSize = cmdbPageSize
		// listEntitiesResponse, err := c.ListEntities(ctx, req)
		// listEntitiesRequest := &cmdb.ListEntitiesRequest{
		// 	PageNumber: pageNumber,
		// 	PageSize:   pageSize,
		// 	Kind:       resourceType,
		// 	Filter: cmdb.ListEntitiesFilter{
		// 		FieldFilters: filters,
		// 	},
		// }
		listEntitiesResponse, err := c.ListEntities(ctx, req)
		if err != nil {
			logger.CtxWarn(ctx, "ListEntities error: %s", err)
			return nil, err
		}
		allItems = append(allItems, listEntitiesResponse.Items...)

		if int32(len(listEntitiesResponse.Items)) < pageSize {
			logger.CtxWarn(ctx, "ListEntities response is empty")
			break
		}
		cmdbPageNum++
	}
	return allItems, nil

}

func (c *CMDBClient) createListEntitiesRequest(ctx context.Context, request *ListEntitiesRequest) (*http.Request, error) {
	cmdbUrl, err := url.Parse(c.baseURL)
	if err != nil {
		return nil, err
	}
	params := url.Values{}
	params.Add("Action", "ListEntities")
	params.Add("Version", "2025-06-07")
	cmdbUrl.RawQuery = params.Encode()
	payload, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}

	return http.NewRequestWithContext(ctx, http.MethodPost, cmdbUrl.String(), bytes.NewBuffer(payload))
}
