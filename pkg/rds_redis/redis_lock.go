package rds_redis

import (
	"code.byted.org/gopkg/logs/v2/log"
	"fmt"
	"time"
)

const lockRedisPrefix = "lock"

func GetLock(key string, expiration time.Duration) (bool, error) {
	RedisClient.Client.Get(fmt.Sprintf("%s:%s", lockRedisPrefix, key))
	lockValue := time.Now().String()

	// 尝试获取锁
	result, err := RedisClient.Client.SetNX(fmt.Sprintf("%s:%s", lockRedisPrefix, key), lockValue, expiration).Result()
	if err != nil {
		return false, err
	}
	if result {
		return true, nil
	} else {
		log.V2.Warn().Str("get lock failed").
			Str("key:").Str(key).
			Emit()
		return false, nil
	}
}

func DeleteLock(key string) {
	RedisClient.Client.Del(fmt.Sprintf("%s:%s", lockRedisPrefix, key))
}
