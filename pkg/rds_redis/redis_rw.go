package rds_redis

import (
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"errors"
	"github.com/samber/lo"
	"time"
)

func GetObj[T any](key string, obj T) error {
	if result, err := RedisClient.Client.Get(key).Result(); err != nil {
		return err
	} else {
		return utils.JsonUnmarshalString(result, obj)
	}
}

func GetMapObj[T *any](key, mapKey string, obj T) error {
	if result, err := RedisClient.Client.MGet(key, mapKey).Result(); err != nil || len(result) == 0 {
		return lo.Ternary(err == nil, errors.New("data not found"), err)
	} else {
		obj = &result[0]
	}
	return errors.New("handler error")
}

func StoreObj[T any](key string, obj T, ttlNanoSecond time.Duration) error {
	// 将用户信息序列化为json字符串
	jsonStr, err := utils.JsonMarshal(obj)
	if err != nil {
		return err
	}
	// 取 ttlNanoSecond的绝对值
	err = RedisClient.Set(key, jsonStr, ttlNanoSecond).Err()
	if err != nil {
		return err
	}
	return nil
}

func DelObj(key string) {
	RedisClient.Del(key)
}

func StoreMapObj(key string, objs []map[string]any) error {
	mKey := "product_" + key
	for _, obj := range objs {
		err = RedisClient.HMSet(mKey, obj).Err()
	}
	if err != nil {
		return err
	}
	return nil
}
