package rds_redis

import (
	"os"

	"code.byted.org/kv/goredis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
)

var err error

// 创建 redis 连接
func InitRedis() {
	// 从环境变量中获取redis 连接信息
	cluster := os.Getenv("REDIS_CLUSTER")

	// 如果从环境变量获取失败，则从配置文件中获取
	if cluster == "" {
		cluster = tcc.GetServiceConfig().CloudSherlockRedis.PSM
	}

	// 创建 redis 连接
	options := goredis.NewOption()
	RedisClient, err = goredis.NewClientWithOption(cluster, options)
	if err != nil {
		panic(err)
	}
	// 测试连接
	_, err = RedisClient.Ping().Result()
	if err != nil {
		panic(err)
	}
}
