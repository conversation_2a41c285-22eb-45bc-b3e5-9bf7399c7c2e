package rds_redis

import "code.byted.org/kv/goredis"

// 创建 redis 配置文件对象
// var RedisCfg *RedisConfig
var RedisClient *goredis.Client

// // redis 配置信息
// type RedisConfig struct {
// 	// 主机地址和端口
// 	Addr string `json:"addr"`
// 	// 用户名
// 	Username string `json:"username"`
// 	// 密码
// 	Passwd string `json:"passwd"`
// 	// 数据库编号
// 	DB int `json:"db"`
// }

// // 创建一个新的redisConfig对象
// func NewRedisConfig(psm string) *RedisConfig {
// 	var redisCfg = RedisConfig{
// 		PSM: psm,
// 	}
// 	return &redisCfg
// }
