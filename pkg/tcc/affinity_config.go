package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
)

type AffinityConfig struct {
	AffinitySummaryNotifyRoles []string `json:"AffinitySummaryNotifyRoles"`
}

var affinityConfigGetter = NewGetter("affinity_config", &AffinityConfig{})

func GetAffinityConfig(ctx context.Context) (*AffinityConfig, error) {
	res, err := affinityConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetAffinityConfig] err:%v", err)
		return nil, err
	}
	return res.(*AffinityConfig), nil
}
