package tcc

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var (
	customerService = "customer_service"
)

type CustomerServiceConfig struct {
	CustomerServiceTeamRoleList      []*common.EnumItem `json:"CustomerServiceTeamRoleList"`
	CustomerGuaranteeStatusSortRule  map[string]int     `json:"CustomerGuaranteeStatusSortRule"`
	SubMainGuaranteeStatusRelatedMap map[string]string  `json:"SubMainGuaranteeStatusRelatedMap"`
	CustomerAffinityStatusSortRule   map[string]int     `json:"CustomerAffinityStatusSortRule"`
	SosStatusSortRule                map[string]int     `json:"SosStatusSortRule"`
	EpsLabelKeyMap                   map[string]string  `json:"EpsLabelKeyMap"`
	CustomerAlarmStatusOrderList     []string           `json:"CustomerAlarmStatusOrderList"`
	AlarmLevelTransMap               map[string]string  `json:"AlarmLevelTransMap"`
	CustomerGuaranteeStatusOrderList []string           `json:"CustomerGuaranteeStatusOrderList"`
	CustomerAffinityStatusOrderList  []string           `json:"CustomerAffinityStatusOrderList"`
	SosStatusOrderList               []string           `json:"SosStatusOrderList"`
}

var customerServiceConfigGetter = NewGetter(customerService, &CustomerServiceConfig{})

func GetCustomerServiceConfig(ctx context.Context) (*CustomerServiceConfig, error) {
	res, err := customerServiceConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetCustomerServiceConfig] err:%v", err)
		return nil, err
	}
	return res.(*CustomerServiceConfig), nil
}
