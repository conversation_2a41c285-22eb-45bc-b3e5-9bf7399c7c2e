package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"

	"code.byted.org/gopkg/tccclient"

	"sync"
	"time"

	"github.com/bytedance/sonic"
)

var (
	client *tccclient.ClientV2
	once   sync.Once
)

type Getter tccclient.Getter

func getClient() *tccclient.ClientV2 {
	logs.Infof("start get tcc client")
	once.Do(func() {
		var err error
		config := tccclient.NewConfigV2()
		logs.Infof("init tcc client, config: %v", config)
		config.FirstGetTimeout = time.Second * 10
		client, err = tccclient.NewClientV2("volcengine.support.cloud_sherlock", config)
		if err != nil {
			logs.Errorf("init tcc client error: %v", err)
			panic(err)
		}
		logs.Infof("init tcc client success, client: %v", client)
	})
	logs.Infof("get tcc client: %v", client)
	return client
}

// Get 获取tcc value
func Get(ctx context.Context, key string) (string, error) {
	value, err := getClient().Get(ctx, key)
	return value, err
}

// GetWithParser 获取tcc value
func NewGetter(key string, ins interface{}, opts ...tccclient.GetterOption) Getter {
	return Getter(getClient().NewGetter(key, sonic.Unmarshal, ins, opts...))
}

// AddListener 注册监听器
func AddListener(key string, callback tccclient.Callback, opts ...tccclient.ListenOption) error {
	return getClient().AddListener(key, callback, opts...)
}
