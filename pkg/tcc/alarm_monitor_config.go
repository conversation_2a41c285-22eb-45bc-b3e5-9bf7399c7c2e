package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"context"
)

var (
	alarmMonitorConfig = "alarm_monitor_config"
)

type AlarmMonitorConfig struct {
	AlarmRuleLink                map[string]string `json:"AlarmRuleLink"`
	AlarmTicketLink              map[string]string `json:"AlarmTicketLink"`
	AlarmMonitoringRefreshPeriod int32             `json:"AlarmMonitoringRefreshPeriod"`
}

var alarmMonitorConfigGetter = NewGetter(alarmMonitorConfig, &AlarmMonitorConfig{})

func GetAlarmMonitorConfig(ctx context.Context) (*AlarmMonitorConfig, error) {
	res, err := alarmMonitorConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetAlarmMonitorConfig] err:%v", err)
		return nil, err
	}
	return res.(*AlarmMonitorConfig), nil
}

func GetAlarmRuleUrl(ctx context.Context, source, sourceID string) string {
	monitorConfig, err := GetAlarmMonitorConfig(ctx)
	if err != nil {
		return ""
	}

	if urlTemplate, ok := monitorConfig.AlarmRuleLink[source]; ok {
		tmpl, err := utils.TemplateRender(ctx, urlTemplate, map[string]string{"SourceID": sourceID})
		if err != nil {
			logs.CtxError(ctx, "[tcc/GetAlarmRuleUrl] err:%v", err)
			return ""
		}
		return tmpl
	}
	return ""
}
