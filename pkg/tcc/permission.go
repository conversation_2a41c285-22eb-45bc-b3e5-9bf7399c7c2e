package tcc

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	hertzPermission "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
)

var (
	permission = "permission"
)

type PermissionConfig struct {
	CustomerAllViewRangeRoleList  []string                    `json:"CustomerAllViewRangeRoleList"`
	CustomerPartViewRangeRoleList []string                    `json:"CustomerPartViewRangeRoleList"`
	CustomerNoViewRangeRoleList   []string                    `json:"CustomerNoViewRangeRoleList"`
	MenuPermissionList            []*hertzPermission.MenuItem `json:"MenuPermissionList"`
	OrganizationNumberList        []string                    `json:"OrganizationNumberList"`
}

var permissionConfigGetter = NewGetter(permission, &PermissionConfig{})

func GetPermissionConfig(ctx context.Context) (*PermissionConfig, error) {
	res, err := permissionConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetPermissionConfig] err:%v", err)
		return nil, err
	}
	return res.(*PermissionConfig), nil
}
