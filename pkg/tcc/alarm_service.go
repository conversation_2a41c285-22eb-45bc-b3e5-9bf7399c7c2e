package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
)

var (
	alarmService = "alarm_service"
)

type ProductParam struct {
	ProductCode, ProductBizLineCode, ProductSecondaryBizLineCode string
}

type AlarmServiceConfig struct {
	AlarmDutyTypeTimeSlotMap                  map[int32][]string         `json:"AlarmDutyTypeTimeSlotMap"`
	AlarmOperateNoticeTemplateMap             map[string]string          `json:"AlarmOperateNoticeTemplateMap"`
	AlarmOperateLogTemplateMap                map[string]string          `json:"AlarmOperateLogTemplateMap"`
	ExternalAlarmRuleLinkMap                  map[string]string          `json:"ExternalAlarmRuleLinkMap"`
	ExternalAlarmRuleLinkNameMap              map[string]string          `json:"ExternalAlarmRuleLinkNameMap"`
	ExternalAlarmRuleRelatedProductMap        map[string][]*ProductParam `json:"ExternalAlarmRuleRelatedProductMap"`
	AlarmRuleNoSyncFieldFromTemplateFieldList []string                   `json:"AlarmRuleNoSyncFieldFromTemplateFieldList"`
	AlarmRuleNoShowChildProductCodeList       []string                   `json:"AlarmRuleNoShowChildProductCodeList"`
	ExternalAlarmRuleLinkSortList             []string                   `json:"ExternalAlarmRuleLinkSortList"`
}

var alarmServiceConfigGetter = NewGetter(alarmService, &AlarmServiceConfig{})

func GetAlarmServiceConfig(ctx context.Context) (*AlarmServiceConfig, error) {
	res, err := alarmServiceConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetAlarmServiceConfig] err:%v", err)
		return nil, err
	}
	return res.(*AlarmServiceConfig), nil
}
