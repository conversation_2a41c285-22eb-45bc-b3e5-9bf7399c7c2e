package tcc

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var (
	commonEnumList = "common_enum_list"
)

const (
	TccKeyCommonEnumCustomer                             = "Customer"
	TccKeyCommonEnumCustomer_MainCustomerGuaranteeStatus = "MainCustomerGuaranteeStatus"
	TccKeyCommonEnumCustomer_SosStatus                   = "SosStatus"
	TccKeyCommonEnumCustomer_CustomerLevel               = "CustomerLevel"

	TccKeyCommonEnumCustomerAffinity                = "CustomerAffinity"
	TccKeyCommonEnumCustomerAffinity_AffinityLevel  = "AffinityLevel"
	TccKeyCommonEnumCustomerAffinity_AffinityStatus = "AffinityStatus"

	TccKeyCommonEnumGuarantee                 = "Guarantee"
	TccKeyCommonEnumGuarantee_GuaranteeStatus = "GuaranteeStatus"
)

type CommonEnumConfig struct {
	CommonEnumList map[string]map[string][]*common.EnumItem `json:"CommonEnumList"`
}

var commonEnumConfigGetter = NewGetter(commonEnumList, &CommonEnumConfig{})

func GetCommonEnumConfig(ctx context.Context) (*CommonEnumConfig, error) {
	res, err := commonEnumConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetCommonEnumConfig] err:%v", err)
		return nil, err
	}
	return res.(*CommonEnumConfig), nil
}

// func GetCommonEnumValueLabelMap(ctx context.Context, firstNodeKey, secondLevelNodeKey string) (map[string]string, error) {
// 	commonEnumConfig, err := GetCommonEnumConfig(ctx)
// 	if err != nil {
// 		logs.CtxError(ctx, "[tcc/GetGuaranteeStatusEnumMap] err:%v", err)
// 		return nil, err
// 	}
// 	valueLabelMap := make(map[string]string)
// 	commonEnumMap := commonEnumConfig.CommonEnumList
// 	if configMap, ok := commonEnumMap[firstNodeKey]; ok {
// 		// map[string][]*common.EnumItem
// 		if labelEnumItemList, ok := configMap[secondLevelNodeKey]; ok {
// 			for _, item := range labelEnumItemList {
// 				if stringx.IsPtrBlank(item.Value) || stringx.IsPtrBlank(item.Label) || pointer.GetBool(item.IsHidden) {
// 					continue
// 				}
// 				valueLabelMap[item.GetValue()] = item.GetLabel()
// 			}
// 		}
// 	}
// 	return valueLabelMap, nil
// }
