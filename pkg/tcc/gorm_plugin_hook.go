package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
)

var (
	gormPluginHook = "gorm_plugin_hook_config"
)

type GormPluginHookConfig struct {
	PluginSwitch map[string]bool `json:"PluginSwitch"`
}

var gormPluginHookConfigGetter = NewGetter(gormPluginHook, &GormPluginHookConfig{})

func GetGormPluginHookConfig(ctx context.Context) (*GormPluginHookConfig, error) {
	res, err := gormPluginHookConfigGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetGormPluginHookConfig] err:%v", err)
		return nil, err
	}
	return res.(*GormPluginHookConfig), nil
}
