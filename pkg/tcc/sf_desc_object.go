package tcc

import (
	"code.byted.org/gopkg/logs/v2"
	"context"
)

var (
	sfDescribeFieldResult = "sf_describe_object_results"
)

var describesObjectResults []DescribesObjectResult

const SfAccount = "Account"
const (
	SfAccountField_Tier        = "account_tier_c"
	SfAccountField_PLevel      = "account_p_level_c"
	SfAccountField_Level       = "account_level_c"
	SfAccountField_Province    = "province_c"
	SfAccountField_Industry    = "industry_c"
	SfAccountField_SubIndustry = "sub_industry_c"
	SfAccountField_Region      = "region_c"
	SfAccountField_City        = "city_c"
)

type DescribesObjectResult struct {
	Name   string                `json:"name"`
	SfName string                `json:"sfName"`
	Label  string                `json:"label"`
	Fields []DescribeFieldResult `json:"fields"`
}
type PicklistEntry struct {
	Value string `json:"value"`
	Label string `json:"label"`
}
type DescribeFieldResult struct {
	PicklistValues []PicklistEntry `json:"picklistValues"`
	Name           string          `json:"name"`
	Label          string          `json:"label"`
	DefaultValue   string          `json:"defaultValue,omitempty"`
}

var sfDescribeFieldResultGetter = NewGetter(sfDescribeFieldResult, describesObjectResults)

func GetDescribeFieldResultConfig(ctx context.Context) ([]DescribesObjectResult, error) {
	res, err := sfDescribeFieldResultGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetDescribeFieldResultConfig] err:%v", err)
		return nil, err
	}
	return res.([]DescribesObjectResult), nil
}

func GetSfAccountLabel(ctx context.Context, entityName, fieldName, fieldValue string) *string {
	describeFieldResultConfig, err := GetDescribeFieldResultConfig(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetSfAccountLabel] err:%v", err)
		return nil
	}
	if len(entityName) == 0 || len(fieldName) == 0 || len(fieldValue) == 0 {
		return nil
	}
	for _, describesObject := range describeFieldResultConfig {
		if describesObject.SfName == entityName {
			for _, ob := range describesObject.Fields {
				if ob.Name == fieldName {
					for _, item := range ob.PicklistValues {
						if item.Value == fieldValue {
							return &item.Label
						}
					}
				}
			}
		}
	}
	return nil
}
