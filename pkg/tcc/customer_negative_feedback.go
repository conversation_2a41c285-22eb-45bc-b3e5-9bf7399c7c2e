package tcc

import (
	"context"

	"code.byted.org/gopkg/logs/v2"
)

var (
	customerNegativeFeedback = "customer_negative_feedback"
)

type CustomerNegativeFeedbackConfig struct {
	LarkInfo      LarkInfo      `json:"LarkInfo"`
	FeishuBiTable FeishuBiTable `json:"FeishuBiTable"`
}

type LarkInfo struct {
	AppID     string `json:"AppID"`
	AppSecret string `json:"AppSecret"`
}

type FeishuBiTable struct {
	AppToken          string `json:"AppToken"`
	AccountTableID    string `json:"AccountTableID"`
	SuperHeadTableID  string `json:"SuperHeadTableID"`
	UltimateTableID   string `json:"UltimateTableID"`
	CommitmentTableID string `json:"CommitmentTableID"`
}

var customerNegativeFeedbackGetter = NewGetter(customerNegativeFeedback, &CustomerNegativeFeedbackConfig{})

func GetCustomerNegativeFeedbackConfig(ctx context.Context) (*CustomerNegativeFeedbackConfig, error) {
	res, err := customerNegativeFeedbackGetter(ctx)
	if err != nil {
		logs.CtxError(ctx, "[tcc/GetCustomerNegativeFeedbackConfig] err:%v", err)
		return nil, err
	}
	return res.(*CustomerNegativeFeedbackConfig), nil
}
