package paas

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	signer "code.byted.org/volcengine/volc-signer-golang"
)

type PaaSClient struct {
	client      *http.Client
	Ak          string
	Sk          string
	ServiceName string
}

var (
	once    sync.Once
	service *PaaSClient
)

const (
	ServiceName = "ops"
)

func GetPaaSClient() *PaaSClient {

	once.Do(func() {
		config := tcc.GetServiceConfig()
		s := &PaaSClient{
			client:      &http.Client{},
			Ak:          config.PaaSConfig.AK,
			Sk:          config.PaaSConfig.SK,
			ServiceName: ServiceName,
		}
		service = s

	})
	return service
}

func (c *PaaSClient) apiRequest(ctx context.Context, request *http.Request) ([]byte, error) {
	request.Header.Set("Content-Type", "application/json")

	req := signer.Sign(request, signer.Credentials{
		AccessKeyID:     c.Ak,
		SecretAccessKey: c.Sk,
		Region:          "cn-north-1",
		Service:         ServiceName,
	})
	response, err := c.client.Do(req)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()
	buf := make([]byte, 0)
	buffer := bytes.NewBuffer(buf)
	_, err = io.Copy(buffer, response.Body)
	if err != nil {
		return nil, err
	}
	return buffer.Bytes(), nil
}

func (p *PaaSClient) DoRequest(ctx context.Context, paasReq *PaaSRequest) (interface{}, error) {

	paasUrl, err := url.Parse(paasReq.Host)
	if err != nil {
		return nil, err
	}
	params := url.Values{}
	// params.Add("Action", "CreateDiagnoseTask")
	params.Add("Action", paasReq.Action)
	// params.Add("Version", "2024-06-20")
	params.Add("Version", paasReq.Version)
	paasUrl.RawQuery = params.Encode()
	payload, err := json.Marshal(paasReq.RequestBody)
	if err != nil {
		return nil, err
	}

	request, err := http.NewRequestWithContext(ctx, http.MethodPost, paasUrl.String(), bytes.NewBuffer(payload))
	if err != nil {
		return nil, err
	}

	var resp interface{}
	output, err := p.apiRequest(ctx, request)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(output, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil

}
