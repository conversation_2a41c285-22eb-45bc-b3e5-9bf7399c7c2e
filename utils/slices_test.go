package utils

import (
	"code.byted.org/gopkg/lang/v2/sortx"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestSliceJoin(t *testing.T) {
	mockey.Patch<PERSON>onvey("TestSliceJoin", t, func() {
		convey.So(SliceJoin([]string{"1"}, ","), convey.ShouldEqual, "1")
		convey.So(<PERSON>lice<PERSON>oin([]string{"1", "2"}, ","), convey.ShouldEqual, "1,2")
		convey.So(SliceJoin([]string{}, ","), convey.ShouldEqual, "")
		convey.So(SliceJoin([]int{1, 2}, ","), convey.ShouldEqual, "1,2")
		convey.So(SliceJoin([]int{1}, ","), convey.ShouldEqual, "1")
	})
}

func TestSliceUnion(t *testing.T) {
	mockey.<PERSON>onvey("TestSliceUnion", t, func() {
		convey.So(func() []string {
			a := SliceUnion([]string{"1"}, []string{"2"})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []string{"1", "2"})
		convey.So(func() []string {
			a := SliceUnion([]string{"1"}, []string{"2"}, []string{"2"})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []string{"1", "2"})
		convey.So(func() []string {
			a := SliceUnion([]string{"1"}, []string{})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []string{"1"})
		convey.So(func() []string {
			a := SliceUnion([]string{}, []string{"2"})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []string{"2"})
		convey.So(func() []string {
			a := SliceUnion([]string{"1"}, nil)
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []string{"1"})
		convey.So(func() []int {
			a := SliceUnion([]int{1}, []int{2})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []int{1, 2})
		convey.So(func() []int {
			a := SliceUnion([]int{1}, []int{2}, []int{2})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []int{1, 2})
		convey.So(func() []int {
			a := SliceUnion([]int{}, []int{2})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []int{2})
		convey.So(func() []int {
			a := SliceUnion([]int{1}, []int{})
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []int{1})
		convey.So(func() []int {
			a := SliceUnion([]int{1}, nil)
			sortx.SortOrdered(a)
			return a
		}(), convey.ShouldResemble, []int{1})
	})
}

func TestSliceIntersectionWithoutEmpty(t *testing.T) {
	type args[T comparable] struct {
		slices [][]T
	}
	type testCase[T comparable] struct {
		name  string
		args  args[T]
		want  []T
		want1 bool
	}
	tests := []testCase[string]{
		{
			args: struct{ slices [][]string }{[][]string{
				{"1"}, {"1", "2"}, {},
			}},
			want:  []string{"1"},
			want1: false,
		},
		{
			args: struct{ slices [][]string }{[][]string{
				{"2"}, {},
			}},
			want:  []string{"2"},
			want1: false,
		},
		{
			args: struct{ slices [][]string }{[][]string{
				{}, {},
			}},
			want:  []string{},
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, got1 := SliceIntersectionWithoutEmpty(tt.args.slices...)
			// slices_test.go:113: SliceIntersectionWithoutEmpty() got = [2 1], want [1 2]
			//if !reflect.DeepEqual(got, tt.want) {
			//	t.Errorf("SliceIntersectionWithoutEmpty() got = %v, want %v", got, tt.want)
			//}
			if got1 != tt.want1 {
				t.Errorf("SliceIntersectionWithoutEmpty() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
