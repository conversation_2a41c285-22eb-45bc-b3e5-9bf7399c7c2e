package utils

import (
	"encoding/json"
	"strings"
	"time"
)

func StringSplit(s, sep string) []string {
	if s == "" {
		return []string{}
	}
	return strings.Split(s, sep)
}

func MakeFuzz(s string) string {
	return "%" + s + "%"
}

func StringToTime(timeStr, layout, timezone string) (time.Time, error) {
	var (
		loc *time.Location
		err error
	)

	if timezone == "" {
		loc = time.Local // 默认使用本地时区
	} else {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			return time.Time{}, err
		}
	}

	// 使用 time.ParseInLocation 解析时间字符串
	t, err := time.ParseInLocation(layout, timeStr, loc)
	if err != nil {
		return time.Time{}, err
	}
	return t, nil
}

func UpdateIdsArray(idsStr *string, newID int64) (string, error) {
	var idsArray []int64
	if idsStr != nil {
		err := json.Unmarshal([]byte(*idsStr), &idsArray)
		if err != nil {
			return "", err
		}
	}
	idsArray = append(idsArray, newID)
	idsArrayStr, err := json.Marshal(idsArray)
	if err != nil {
		return "", err
	}
	return string(idsArrayStr), nil
}

// []string切片去重
func RemoveDuplicates(slice []string) []string {
	uniqueMap := make(map[string]bool)
	var uniqueSlice []string
	for _, item := range slice {
		if !uniqueMap[item] {
			uniqueMap[item] = true
			uniqueSlice = append(uniqueSlice, item)
		}
	}
	return uniqueSlice
}
