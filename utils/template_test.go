package utils

import (
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func Test_TemplateRender(t *testing.T) {
	type args struct {
		tem  string
		data interface{}
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: struct {
				tem  string
				data interface{}
			}{tem: "{{ .url | raw }}", data: map[string]interface{}{
				"url": fmt.Sprintf("https://sales-management-boe.bytedance.net/target_and_plan-statement-detail?id=%s", "580"),
			}},
			want: "https://sales-management-boe.bytedance.net/target_and_plan-statement-detail?id=580",
		},
	}
	for _, tt := range tests {
		mockey.PatchConvey(tt.name, t, func() {
			got, err := TemplateRender(context.Background(), tt.args.tem, tt.args.data)
			convey.So(err, convey.ShouldBeNil)
			convey.So(got, convey.ShouldEqual, tt.want)
		})
	}
}
