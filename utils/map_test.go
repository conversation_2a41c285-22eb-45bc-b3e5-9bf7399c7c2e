package utils

import (
	"context"
	"golang.org/x/sync/errgroup"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestNewCMap(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			key := "key1"
			m := NewCMap[int64]()
			convey.So(m, convey.ShouldNotBeNil)
			val, ok := m.Get(key)
			convey.So(val, convey.ShouldEqual, 0)
			convey.So(ok, convey.ShouldEqual, false)

			for i := 0; i < 10; i++ {
				m.Set(key, int64(i))
				val, ok = m.Get(key)
				convey.So(val, convey.ShouldEqual, i)
				convey.So(ok, convey.ShouldEqual, true)
			}

			wg, _ := errgroup.WithContext(context.Background())
			for i := 0; i < 10; i++ {
				wg.Go(func() error {
					for i := 0; i < 100; i++ {
						m.Set(key, 1)
					}
					return nil
				})
			}
			err := wg.Wait()
			convey.So(err, convey.ShouldBeNil)
			val, ok = m.Get(key)
			convey.So(val, convey.ShouldEqual, 1)
			convey.So(ok, convey.ShouldEqual, true)
		})
	})
}
