package utils

import (
	"bytes"
	"context"
	"fmt"
	"html/template"
	"reflect"
	"strings"
	texttemplate "text/template"

	"code.byted.org/gopkg/logs/v2"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/lang/v2/stringx"
)

// RenderGrafanaTemplate 专用于渲染Grafana URL模板的简单模板渲染函数
func RenderGrafanaTemplate(templateStr string, data map[string]string) (string, error) {
	tmpl, err := texttemplate.New("grafana").Parse(templateStr)
	if err != nil {
		return "", fmt.Errorf("parse grafana template failed: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("execute grafana template failed: %w", err)
	}

	return buf.String(), nil
}

func TemplateRender(ctx context.Context, tem string, data interface{}) (string, error) {
	tmpl, err := template.New("test").Funcs(template.FuncMap{
		"raw":           func(x string) template.HTML { return template.HTML(strings.ReplaceAll(x, "\n", "\\n")) },
		"isProduct":     func() bool { return env.IsProduct() },
		"isLastElement": func(index int, a interface{}) bool { return index == reflect.ValueOf(a).Len()-1 },
		"larkAt": func(email string) template.HTML {
			if stringx.IsBlank(email) {
				return ""
			}
			if !strings.HasSuffix(email, "@bytedance.com") {
				return template.HTML(email)
			}
			return template.HTML(fmt.Sprintf("<at email=%s></at>", email))
		},
		"stringContains": func(arr []string, v string) bool {
			return slicex.Contains(arr, v)
		},
		"ratioWithColor": func(ratio float64) template.HTML {
			result := ""
			if ratio < 0 {
				result = fmt.Sprintf("↓%.1f%%", ratio*100)
			} else if ratio > 0 {
				result = fmt.Sprintf("↑%.1f%%", ratio*100)
			} else {
				result = fmt.Sprintf("%.1f%%", ratio*100)
			}
			return template.HTML(result)
		},
		"joinWithLineBreak": func(line []string) string {
			return strings.Join(line, "\\n")
		},
	}).Parse(tem)

	if err != nil {
		logs.CtxError(ctx, "[utils/TemplateRenderForText] Parse err:%v", err)
		return "", err
	}

	sb := &strings.Builder{}
	// 渲染stu为动态数据, 标准输出到终端
	err = tmpl.Execute(sb, data)
	if err != nil {
		logs.CtxError(ctx, "[utils/TemplateRenderForText] Execute err:%v", err)
		return "", err
	}
	return sb.String(), nil
}
