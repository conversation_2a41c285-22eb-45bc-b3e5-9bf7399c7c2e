package utils

import (
	"context"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/constants"
)

func CtxGetEmployeeNumber(ctx context.Context) string {
	got := ctx.Value(constants.CtxEmployeeNumber)
	if got == nil {
		return "system"
	}
	return got.(string)
}

func CtxGetEmployeeEmail(ctx context.Context) string {
	got := ctx.Value(constants.CtxEmployeeEmail)
	if got == nil {
		return "system"
	}
	return got.(string)
}

func CtxSetEmployeeEmail(ctx context.Context, email string) context.Context {
	return context.WithValue(ctx, constants.CtxEmployeeEmail, email)
}

func CtxSetEmployeeNumber(ctx context.Context, number string) context.Context {
	return context.WithValue(ctx, constants.CtxEmployeeNumber, number)
}

func CtxSetOriginalEmployeeEmail(ctx context.Context, email string) context.Context {
	return context.WithValue(ctx, constants.CtxOriginalEmployeeEmail, email)
}

func CtxSetOriginalEmployeeNumber(ctx context.Context, number string) context.Context {
	return context.WithValue(ctx, constants.CtxOriginalEmployeeNumber, number)
}

func CtxGetOriginalEmployeeNumber(ctx context.Context) string {
	got := ctx.Value(constants.CtxOriginalEmployeeNumber)
	if got == nil {
		return CtxGetEmployeeNumber(ctx)
	}
	return got.(string)
}

func CtxGetOriginalEmployeeEmail(ctx context.Context) string {
	got := ctx.Value(constants.CtxOriginalEmployeeEmail)
	if got == nil {
		return CtxGetEmployeeEmail(ctx)
	}
	return got.(string)
}
