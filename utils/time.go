package utils

import (
	"time"
)

const (
	TimeOnly = "15:04:05"
	DateTime = "2006-01-02 15:04:05"
)

func TimeToDate(a time.Time) time.Time {
	return time.Date(a.Year(), a.Month(), a.Day(), 0, 0, 0, 0, a.Location())
}

func GetMonday(now time.Time) time.Time {
	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	return time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)
}

func GetLastMonday(now time.Time) time.Time {
	monday := GetMonday(now)
	return monday.AddDate(0, 0, -7)
}

func GetYearPeriod(now time.Time) (time.Time, time.Time) {
	year := now.Year()
	return time.Date(year, 1, 1, 0, 0, 0, 0, time.Local), time.Date(year+1, 1, 1, 0, 0, 0, 0, time.Local).Add(-1 * time.Second)
}

// IsBetween 判断时间是否在指定范围内，闭区间
func IsBetween(target, start, end time.Time) bool {
	return target.After(start) && target.Before(end) || target.Equal(start) || target.Equal(end)
}

// TimeToString 转换时间为字符串
func TimeToString(t time.Time, timezone string) (string, error) {
	var (
		loc *time.Location
		err error
	)

	if timezone == "" {
		loc = time.Local // 默认使用本地时区
	} else {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			return "", err
		}
	}
	localTime := t.In(loc)
	str := localTime.Format(time.DateTime)
	return str, nil
}

// ConvertToMicroseconds 判断时间戳是否是秒级，并转换为微秒级
func ConvertToMicroseconds(timestamp int64) int64 {
	// 判断时间戳是否是秒级（10位）
	if timestamp < 10000000000 {
		// 将秒级时间戳转换为微秒级时间戳
		return timestamp * 1000000
	}
	// 已经是微秒级时间戳，直接返回
	return timestamp
}

// ConvertToMilliseconds 判断时间戳是否是秒级，并转换为毫秒级
func ConvertToMilliseconds(timestamp int64) int64 {
	// 判断时间戳是否是秒级（10位）
	if timestamp < 10000000000 {
		// 将秒级时间戳转换为毫秒级时间戳
		return timestamp * 1000
	}
	// 已经是毫秒级时间戳，直接返回
	return timestamp
}
