package utils

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestTransInt2Bool(t *testing.T) {
	t.Run("TestTransInt2Bool #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			got := TransInt2Bool(0)

			// Assert
			convey.So(got, convey.ShouldEqual, false)
		})
	})
	t.Run("TestTransInt2Bool #2", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			got := TransInt2Bool(1)

			// Assert
			convey.So(got, convey.ShouldEqual, true)
		})
	})
}

func TestTransBool2Int(t *testing.T) {
	t.Run("TestTransBool2Int #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			got := TransBool2Int(false)

			// Assert
			convey.So(got, convey.ShouldEqual, 0)
		})
	})
	t.Run("TestTransBool2Int #2", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			got := TransBool2Int(true)

			// Assert
			convey.So(got, convey.ShouldEqual, 1)
		})
	})
}

func TestStringBlankDefault(t *testing.T) {
	type args struct {
		s string
		d string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				s: "",
				d: "default",
			},
			want: "default",
		},
		{
			args: args{
				s: " ",
				d: "default",
			},
			want: "default",
		},
		{
			args: args{
				s: " a",
				d: "default",
			},
			want: " a",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := StringBlankDefault(tt.args.s, tt.args.d); got != tt.want {
				t.Errorf("StringBlankDefault() = %v, want %v", got, tt.want)
			}
		})
	}
}
