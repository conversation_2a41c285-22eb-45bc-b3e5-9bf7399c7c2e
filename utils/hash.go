package utils

import (
	"code.byted.org/gopkg/logs/v2"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
)

func Sha256(data string) string {
	hash := sha256.New()
	if _, err := hash.Write([]byte(data)); err != nil {
		logs.Warn(err.Error())
	}
	raw := hash.Sum(nil)
	return hex.EncodeToString(raw)
}

func Md5(data string) string {
	hash := md5.New()
	if _, err := hash.Write([]byte(data)); err != nil {
		logs.Warn(err.Error())
	}
	raw := hash.Sum(nil)
	return hex.EncodeToString(raw)
}
