package utils

import (
	"context"

	_const "code.byted.org/bytedtrace/bytedtrace-common/go/const"

	logs "code.byted.org/gopkg/logs/v2/log"
)

type ModuleLogger struct {
	module     string
	noFunction *moduleLoggerWithFunc
}

func NewModuleLogger(module string) *ModuleLogger {
	l := &ModuleLogger{
		module: module,
	}
	l.noFunction = l.WithFunc("")
	return l
}

func (receiver *ModuleLogger) WithFunc(function string) *moduleLoggerWithFunc {
	return &moduleLoggerWithFunc{
		module:   receiver.module,
		function: function,
	}
}

func (receiver *ModuleLogger) CtxInfo(ctx context.Context, format string, v ...interface{}) {
	receiver.noFunction.CtxInfo(ctx, format, v...)
}

func (receiver *ModuleLogger) CtxWarn(ctx context.Context, format string, v ...interface{}) {
	receiver.noFunction.CtxWarn(ctx, format, v...)
}

func (receiver *ModuleLogger) CtxError(ctx context.Context, format string, v ...interface{}) {
	receiver.noFunction.CtxError(ctx, format, v...)
}

func (receiver *ModuleLogger) Error(format string, v ...interface{}) {
	receiver.noFunction.Error(format, v...)
}

type moduleLoggerWithFunc struct {
	module   string
	function string
}

func (receiver *moduleLoggerWithFunc) WithFunc(function string) *moduleLoggerWithFunc {
	return &moduleLoggerWithFunc{
		module:   receiver.module,
		function: receiver.function + "/" + function,
	}
}

func (receiver *moduleLoggerWithFunc) Info(format string, v ...interface{}) {
	if receiver.function != "" {
		params := []interface{}{receiver.module, receiver.function}
		params = append(params, v...)
		logs.V1.Info("[%s/%s] "+format, params...)
	} else {
		params := []interface{}{receiver.module}
		params = append(params, v...)
		logs.V1.Info("[%s] "+format, params...)
	}
}

func (receiver *moduleLoggerWithFunc) CtxInfo(ctx context.Context, format string, v ...interface{}) {
	if receiver.function != "" {
		params := []interface{}{receiver.module, receiver.function}
		params = append(params, v...)
		logs.V1.CtxInfo(ctx, "[%s/%s] "+format, params...)
	} else {
		params := []interface{}{receiver.module}
		params = append(params, v...)
		logs.V1.CtxInfo(ctx, "[%s] "+format, params...)
	}
}

func (receiver *moduleLoggerWithFunc) CtxWarn(ctx context.Context, format string, v ...interface{}) {
	if receiver.function != "" {
		params := []interface{}{receiver.module, receiver.function}
		params = append(params, v...)
		logs.V1.CtxWarn(ctx, "[%s/%s] "+format, params...)
	} else {
		params := []interface{}{receiver.module}
		params = append(params, v...)
		logs.V1.CtxWarn(ctx, "[%s] "+format, params...)
	}
}

func (receiver *moduleLoggerWithFunc) CtxError(ctx context.Context, format string, v ...interface{}) {
	if receiver.function != "" {
		params := []interface{}{receiver.module, receiver.function}
		params = append(params, v...)
		logs.V1.CtxError(ctx, "[%s/%s] "+format, params...)
	} else {
		params := []interface{}{receiver.module}
		params = append(params, v...)
		logs.V1.CtxError(ctx, "[%s] "+format, params...)
	}
}

func (receiver *moduleLoggerWithFunc) Error(format string, v ...interface{}) {
	if receiver.function != "" {
		params := []interface{}{receiver.module, receiver.function}
		params = append(params, v...)
		logs.V1.Error("[%s/%s] "+format, params...)
	} else {
		params := []interface{}{receiver.module}
		params = append(params, v...)
		logs.V1.Error("[%s] "+format, params...)
	}
}

func SetLogID(ctx context.Context, logID string) context.Context {
	return context.WithValue(ctx, _const.LogIdKey, logID)
}
