package utils

/*
本文件实现了AES加密和解密
*/

import (
	"crypto/aes"
	"crypto/cipher"
	b64 "encoding/base64"
)

// aes的加密字符串
var key = "astaxie12798akljzmknm.ahkjkljl;k"

// AES加密
func AesCrypto(plainText string) ([]byte, error) {
	// 创建加密算法aes的实例
	c, err := aes.NewCipher([]byte(key))
	if err != nil {
		return []byte{}, err
	}
	//加密字符串
	iv := make([]byte, aes.BlockSize)
	cfb := cipher.NewCFBEncrypter(c, iv)
	ciphertext := make([]byte, len(plainText))
	cfb.XORKeyStream(ciphertext, []byte(plainText))
	return ciphertext, nil
}

// AES解密
func AesDecrypto(cipherText []byte) ([]byte, error) {
	// 创建加密算法aes的实例
	c, err := aes.NewCipher([]byte(key))
	if err != nil {
		return []byte{}, err
	}
	// 解密字符串
	iv := make([]byte, aes.BlockSize)
	cfbdec := cipher.NewCFBDecrypter(c, iv)
	plaintext := make([]byte, len(cipherText))
	cfbdec.XORKeyStream(plaintext, cipherText)
	return plaintext, nil
}

// base64编码
func Base64Encode(src []byte) string {
	sEnc := b64.StdEncoding.EncodeToString([]byte(src))
	return sEnc
}

// base64解码
func Base64Decode(src string) ([]byte, error) {
	sDec, err := b64.StdEncoding.DecodeString(src)
	if err != nil {
		return []byte{}, err
	}
	return sDec, nil
}
