package utils

import (
	"github.com/shopspring/decimal"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestInt64Div(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			got := Int64Div(5, 10)

			// Assert
			convey.So(got, convey.ShouldEqual, 0.5)
		})
	})
}

func TestFloat64Int64Mul(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {

			got := Float64Int64Mul(0.1, 10)

			// Assert
			convey.So(got, convey.ShouldEqual, decimal.NewFromInt(1))
		})
	})
}
