package utils

import (
	cmap "github.com/orcaman/concurrent-map"
)

type Cmap[V any] struct {
	m cmap.ConcurrentMap
}

func NewCMap[V any]() *Cmap[V] {
	return &Cmap[V]{
		m: cmap.New(),
	}
}

func (m *Cmap[V]) Get(k string) (V, bool) {
	valITF, ok := m.m.Get(k)
	if !ok {
		var zero V
		return zero, false
	}
	return valITF.(V), true
}

func (m *Cmap[V]) Set(k string, v V) {
	m.m.Set(k, v)
}

func (m *Cmap[V]) Keys() []string {
	return m.m.Keys()
}
