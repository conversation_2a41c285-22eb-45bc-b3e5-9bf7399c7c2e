package utils

import "code.byted.org/gopkg/lang/v2/stringx"

func IF[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func BoolAnd(fns ...func() bool) bool {
	for _, fn := range fns {
		if !fn() {
			return false
		}
	}
	return true
}

func TransInt2Bool(n int32) bool {
	if n == 0 {
		return false
	}
	return true
}

func TransBool2Int(b bool) int32 {
	if b {
		return 1
	}
	return 0
}

func StringBlankDefault(s string, d string) string {
	if stringx.IsBlank(s) {
		return d
	}
	return s
}
