package utils

import (
	"context"
	"runtime/debug"
	"sync"

	"code.byted.org/gopkg/logs"
)

func AsyncRecover(ctx context.Context, cbs ...func(ctx context.Context, r interface{})) {
	if r := recover(); r != nil {
		logs.CtxError(ctx, "[panic] recover capture: %v\n%s\n", r, string(debug.Stack()))
		for _, cb := range cbs {
			cb(ctx, r)
		}
	}
}

type SafeInt64 struct {
	Value int64
	Mutex sync.Mutex
}

func (s *SafeInt64) Add(val int64) {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	s.Value = s.Value + val
}
func (s *SafeInt64) Get() int64 {
	s.Mutex.Lock()
	defer s.Mutex.Unlock()
	return s.Value
}
