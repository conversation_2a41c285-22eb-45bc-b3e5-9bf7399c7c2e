package utils

import (
	"github.com/bytedance/sonic"
)

// JsonToString
// 将json转换为字符串, 如果转换失败则返回空字符串"", 不抛出异常
func JsonToString(i interface{}) string {
	ret := ""
	jsBytes, jsErr := sonic.Marshal(i)
	if jsErr == nil {
		ret = string(jsBytes)
	}
	return ret
}

func JsonToStringIndent(m interface{}, i string) string {
	ret := ""
	jsBytes, jsErr := sonic.MarshalIndent(m, "", i)
	if jsErr == nil {
		ret = string(jsBytes)
	}
	return ret
}

func JsonMarshal(i interface{}) (string, error) {
	return sonic.MarshalString(i)
}

func JsonMarshalToBytes(s interface{}) ([]byte, error) {
	return sonic.Marshal(s)
}

// JsonCopy
// 深拷贝对象, 如果复制失败则返回错误
func JsonCopy(source, target interface{}) error {
	bytes, err := sonic.Marshal(source)
	if err != nil {
		return err
	}
	return sonic.Unmarshal(bytes, target)
}

// JsonUnmarshal
// 将json字符数组转换为结构体, 如果转换失败则返回错误
func JsonUnmarshal(b []byte, obj interface{}) error {
	return sonic.Unmarshal(b, obj)
}

// JsonUnmarshalString
// 将json字符串转换为结构体, 如果转换失败则返回错误
func JsonUnmarshalString(b string, obj interface{}) error {
	return sonic.UnmarshalString(b, obj)
}
