package utils

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestIsVolcAccountID(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			ok := IsVolcAccountID("**********")

			// Assert
			convey.So(ok, convey.ShouldEqual, true)
		})
	})
}
func TestIsCustomerNumber(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			ok := IsCustomerNumber("ACC-**********")

			// Assert
			convey.So(ok, convey.ShouldEqual, true)
		})
	})
}
