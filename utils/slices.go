package utils

import (
	"code.byted.org/gopkg/lang/v2/setx"
	"code.byted.org/gopkg/lang/v2/slicex"
	"strings"

	"code.byted.org/gopkg/lang/v2/conv"
)

func SliceJoin[T any](slice []T, sep string) string {
	if len(slice) <= 0 {
		return ""
	}

	sliceString := slicex.Map(slice, func(t T) string {
		return conv.StringDefault(t, "")
	})
	return strings.Join(sliceString, sep)
}

func SliceUnion[T comparable](slices ...[]T) []T {
	s := setx.NewHashSetWithCap[T](0)
	for _, slice := range slices {
		s.AddAll(slice)
	}
	return s.ToSlice()
}

func SliceIntersectionWithoutEmpty[T comparable](slices ...[]T) ([]T, bool) {
	if len(slices) <= 0 {
		return []T{}, true
	}
	allEmpty := true
	s := setx.NewHashSetFromSlice(slices[0])
	if len(slices[0]) > 0 {
		allEmpty = false
	}
	for i, slice := range slices {
		if i == 0 {
			continue
		}
		if len(slice) <= 0 {
			continue
		}
		allEmpty = false
		s.Intersection(setx.NewHashSetFromSlice(slice))
	}
	return s.ToSlice(), allEmpty
}

func SliceContainsAny[T comparable](s1, s2 []T) bool {
	set1 := setx.NewHashSetFromSlice[T](s1)
	set2 := setx.NewHashSetFromSlice[T](s2)
	return set1.Intersection(set2).Len() > 0
}
