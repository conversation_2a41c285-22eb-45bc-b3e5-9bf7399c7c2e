package utils

import (
	"context"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
)

func TestCtxGetEmployeeNumber(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			ctx := context.Background()
			got := CtxGetEmployeeNumber(ctx)
			convey.So(got, convey.ShouldEqual, "system")

			ctx = CtxSetEmployeeNumber(ctx, "unittest")
			got = CtxGetEmployeeNumber(ctx)
			convey.So(got, convey.ShouldEqual, "unittest")
		})
	})
}

func TestCtxGetEmployeeEmail(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			ctx := context.Background()
			got := CtxGetEmployeeEmail(ctx)
			convey.So(got, convey.ShouldEqual, "system")

			ctx = CtxSetEmployeeEmail(ctx, "unittest")
			got = CtxGetEmployeeEmail(ctx)
			convey.So(got, convey.ShouldEqual, "unittest")
		})
	})
}

func TestCtxGetOriginalEmployeeEmail(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			ctx := context.Background()
			got := CtxGetOriginalEmployeeEmail(ctx)
			convey.So(got, convey.ShouldEqual, "system")

			ctx = CtxSetEmployeeEmail(ctx, "unittest")
			got = CtxGetOriginalEmployeeEmail(ctx)
			convey.So(got, convey.ShouldEqual, "unittest")

			ctx = CtxSetOriginalEmployeeEmail(ctx, "unittest2")
			got = CtxGetOriginalEmployeeEmail(ctx)
			convey.So(got, convey.ShouldEqual, "unittest2")
		})
	})
}

func TestCtxGetOriginalEmployeeNumber(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			ctx := context.Background()
			got := CtxGetOriginalEmployeeNumber(ctx)
			convey.So(got, convey.ShouldEqual, "system")

			ctx = CtxSetOriginalEmployeeNumber(ctx, "unittest")
			got = CtxGetOriginalEmployeeNumber(ctx)
			convey.So(got, convey.ShouldEqual, "unittest")

			ctx = CtxSetOriginalEmployeeNumber(ctx, "unittest2")
			got = CtxGetOriginalEmployeeNumber(ctx)
			convey.So(got, convey.ShouldEqual, "unittest2")
		})
	})
}
