package utils

import (
	"github.com/shopspring/decimal"
)

func Int64Div(a, b int64) float64 {
	f, _ := decimal.NewFromInt(a).Div(decimal.NewFromInt(b)).Float64()
	return f
}

func Float64Int64Mul(a float64, b int64) decimal.Decimal {
	d := decimal.NewFromFloat(a).Mul(decimal.NewFromInt(b))
	return d
}

func IncrRatio[T int | float64](a, b T) float64 {
	if b == 0 {
		return 0
	}
	aF := float64(a)
	bF := float64(b)
	res, _ := decimal.NewFromFloat(aF).Sub(decimal.NewFromFloat(bF)).
		DivRound(decimal.NewFromFloat(bF), 3).Float64()
	return res
}
