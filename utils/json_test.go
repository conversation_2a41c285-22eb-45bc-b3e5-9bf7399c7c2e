package utils

import (
	"github.com/bytedance/sonic"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestJsonToString(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			v := map[string]interface{}{
				"a": "b",
			}

			got := JsonToString(v)

			// Assert
			convey.So(got, convey.ShouldNotBeBlank)
		})
	})
}

func TestJsonMarshal(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			v := map[string]interface{}{
				"a": "b",
			}
			got, err := JsonMarshal(v)
			// Assert
			convey.So(err, convey.ShouldBeNil)
			convey.So(got, convey.ShouldNotBeBlank)
		})
	})
}

func TestJsonCopy(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			type test struct {
				Name string
			}
			v := test{
				Name: "b",
			}
			v1 := test{}
			err := JsonCopy(v, &v1)

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

func TestJsonUnmarshal(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			type test struct {
				Name string
			}
			v := test{
				Name: "b",
			}
			bytes, _ := sonic.Marshal(v)
			v1 := test{}
			err := JsonUnmarshal(bytes, &v1)

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

func TestJsonUnmarshalString(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			v := make(map[string]string)
			err := JsonUnmarshalString("{\"Name\":\"a\"}", &v)

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
}

func TestJsonToStringIndent(t *testing.T) {
	type args struct {
		m interface{}
		i string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{
				m: map[string]interface{}{
					"a": "b",
				},
				i: "\t",
			},
			want: `{
	"a": "b"
}`,
		},
		{
			args: args{
				m: map[string]interface{}{
					"c": map[string]interface{}{
						"d": "e",
					},
				},
				i: "\t",
			},
			want: `{
	"c": {
		"d": "e"
	}
}`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := JsonToStringIndent(tt.args.m, tt.args.i); got != tt.want {
				t.Errorf("JsonToStringIndent() = %v, want %v", got, tt.want)
			}
		})
	}
}
