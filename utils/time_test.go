package utils

import (
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"reflect"
	"testing"
	"time"
)

func TestTimeToDate(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			v, _ := time.Parse("2006-01-02 15:04:05", "2023-07-01 10:10:00")
			got := TimeToDate(v)
			r, _ := time.Parse("2006-01-02 15:04:05", "2023-07-01 00:00:00")
			// Assert
			convey.So(got, convey.ShouldEqual, r)
		})
	})
}

func TestGetMonday(t *testing.T) {
	type args struct {
		now time.Time
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 2, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 2, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 4, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 2, 0, 0, 0, 0, time.Local),
		},

		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 8, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 2, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 9, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 9, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 9, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 9, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 3, 3, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 2, 26, 0, 0, 0, 0, time.Local),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetMonday(tt.args.now); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMonday() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetLastMonday(t *testing.T) {
	type args struct {
		now time.Time
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 2, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 8, 26, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 4, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 8, 26, 0, 0, 0, 0, time.Local),
		},

		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 8, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 8, 26, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 9, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 2, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 9, 9, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 9, 2, 0, 0, 0, 0, time.Local),
		},
		{
			args: struct{ now time.Time }{
				now: time.Date(2024, 3, 3, 12, 34, 12, 0, time.Local),
			},
			want: time.Date(2024, 2, 19, 0, 0, 0, 0, time.Local),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetLastMonday(tt.args.now); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLastMonday() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestIsBetween(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange

			// Act
			v, _ := time.Parse("2006-01-02 15:04:05", "2023-07-01 00:00:00")
			v1, _ := time.Parse("2006-01-02 15:04:05", "2023-07-01 00:00:00")
			v2, _ := time.Parse("2006-01-02 15:04:05", "2023-07-02 00:00:00")
			got := IsBetween(v, v1, v2)

			// Assert
			convey.So(got, convey.ShouldEqual, true)
		})
	})
	t.Run("case #2", func(t *testing.T) {})
}
