package product_category

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetProductCategoryService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("GetProductCategoryService"),
		}
	})
	return service
}

func (s *Service) QueryProductCategoryList(ctx context.Context, req product_category.QueryProductCategoryListReq) (product_category.QueryProductCategoryListResult, error) {
	logger := s.logger.WithFunc("QueryProductCategoryList")
	productCategory := &model.ProductCategory{}
	if req.Id != nil {
		productCategory.ID = *req.Id
	}
	if req.Product != nil {
		productCategory.Product = *req.Product
	}
	if req.SubProduct != nil {
		productCategory.SubProduct = *req.SubProduct
	}
	if req.ResourceType != nil {
		productCategory.ResourceType = *req.ResourceType
	}
	if req.ProductCn != nil {
		productCategory.ProductCn = *req.ProductCn
	}
	if req.SubProductCn != nil {
		productCategory.SubProductCn = *req.SubProductCn
	}
	if req.ResourceTypeCn != nil {
		productCategory.ResourceTypeCn = *req.ResourceTypeCn
	}
	productCategoryList, err := dal.QueryProductCategoryList(ctx, productCategory)
	if err != nil {
		logger.CtxError(ctx, "QueryProductCategoryList err:%v", err)
		return product_category.QueryProductCategoryListResult{}, err
	}
	mProductCategoryList := make([]*product_category.ProductCategory, 0)
	for _, category := range productCategoryList {
		arguments := make([]*product_category.Argument, 0)
		err := json.Unmarshal([]byte(*category.Arguments), &arguments)
		if err != nil {
			logger.CtxError(ctx, "Unmarshal err:%v", err)
			return product_category.QueryProductCategoryListResult{}, err
		}
		mProductCategory := &product_category.ProductCategory{
			Id:                        category.ID,
			Product:                   category.Product,
			SubProduct:                category.SubProduct,
			ResourceType:              category.ResourceType,
			ProductCn:                 category.ProductCn,
			SubProductCn:              category.SubProductCn,
			ResourceTypeCn:            category.ResourceTypeCn,
			Arguments:                 arguments,
			GrafanaTemplate:           &category.GrafanaTemplate,
			MaxDurationHour:           category.MaxDurationHour,
			MaxStartTimeBeforeNowHour: category.MaxStartTimeBeforeNowHour,
		}
		mProductCategoryList = append(mProductCategoryList, mProductCategory)
	}
	return product_category.QueryProductCategoryListResult{
		ProductCategoryList: mProductCategoryList,
	}, nil
}

func (s *Service) GetProductCategoryByResourceType(ctx context.Context, resourceType string) (*product_category.ProductCategory, error) {
	req := &product_category.QueryProductCategoryListReq{
		ResourceType: &resourceType,
	}

	result, err := s.QueryProductCategoryList(ctx, *req)
	if err != nil {
		return nil, err
	}
	if len(result.ProductCategoryList) == 0 {
		return nil, fmt.Errorf("product category not found for resource type: %s", resourceType)
	}

	return result.ProductCategoryList[0], nil
}

func (s *Service) QueryProductCategoryListByIDs(ctx context.Context, req product_category.QueryProductCategoryListByIDsReq) (product_category.QueryProductCategoryListResult, error) {
	logger := s.logger.WithFunc("QueryProductCategoryListByIDs")
	if len(req.IDs) == 0 {
		return product_category.QueryProductCategoryListResult{}, nil
	}
	productCategoryList, err := dal.ProductCategoryFindByIDs(ctx, req.IDs)
	if err != nil {
		logger.CtxError(ctx, "ProductCategoryFindByIDs err:%v", err)
		return product_category.QueryProductCategoryListResult{}, err
	}
	mProductCategoryList := make([]*product_category.ProductCategory, 0)
	for _, category := range productCategoryList {
		arguments := make([]*product_category.Argument, 0)
		err := json.Unmarshal([]byte(*category.Arguments), &arguments)
		if err != nil {
			logger.CtxError(ctx, "Unmarshal err:%v", err)
			return product_category.QueryProductCategoryListResult{}, err
		}
		mProductCategory := &product_category.ProductCategory{
			Id:                        category.ID,
			Product:                   category.Product,
			SubProduct:                category.SubProduct,
			ResourceType:              category.ResourceType,
			ProductCn:                 category.ProductCn,
			SubProductCn:              category.SubProductCn,
			ResourceTypeCn:            category.ResourceTypeCn,
			Arguments:                 arguments,
			GrafanaTemplate:           &category.GrafanaTemplate,
			MaxDurationHour:           category.MaxDurationHour,
			MaxStartTimeBeforeNowHour: category.MaxStartTimeBeforeNowHour,
		}
		mProductCategoryList = append(mProductCategoryList, mProductCategory)
	}
	return product_category.QueryProductCategoryListResult{
		ProductCategoryList: mProductCategoryList,
	}, nil
}
