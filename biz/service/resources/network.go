package resources

// import (
// 	"context"

// 	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
// 	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
// 	"code.byted.org/volcengine-support/cloud-sherlock/utils"
// 	"github.com/cloudwego/hertz/pkg/app"
// )

// type EipResourceHandler struct {
// 	Logger *utils.ModuleLogger
// }

// func (p *EipResourceHandler) GetResources(ctx context.Context, c *app.RequestContext) error {
// 	// logger := p.Logger.WithFunc("GetResources For Network")
// 	// params := &describe_resource.DescribeResourceRequest{}

// 	err := networkSubResourceHandlerFactory.HandlerResource(ctx, c)
// 	if err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return nil
// 	}
// 	return nil
// }

// func (h *EipResourceHandler) Match(ctx context.Context, input *describe_resource.DescribeResourceRequest) bool {
// 	return networkSubResourceHandlerFactory.MatchNetwork(input)
// }

// func (h *EipResourceHandler) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
// 	err := networkSubResourceHandlerFactory.HandlerResourceDependency(ctx, c)
// 	if err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return nil
// 	}
// 	return nil

// }
