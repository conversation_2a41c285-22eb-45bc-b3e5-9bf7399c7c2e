package converter

import (
	"fmt"
	"strconv"

	"code.byted.org/iaasng/volcengine-go-ops-sdk/service/ecsops"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	"github.com/samber/lo"
)

// 策略接口
type ConversionStrategy interface {
	Apply(output *ecsops.OpsFusionSearchOutput) []*cmdb.ResourceEntry
}

type StrategyFactory struct {
	strategies []ConversionStrategy
}

func NewECSStrategyFactory() *StrategyFactory {
	return &StrategyFactory{
		strategies: []ConversionStrategy{
			&ResourceMetadata{},
			&InstanceMetadata{},
		},
	}
}

type ResourceMetadata struct{}

func (r *ResourceMetadata) Apply(output *ecsops.OpsFusionSearchOutput) []*cmdb.ResourceEntry {
	return lo.Filter([]*cmdb.ResourceEntry{
		createEntry("HostId", "主机ID", output.Host.Id),
		createEntry("HostName", "主机名称", output.Host.Hostname),
		createEntry("HostType", "主机类型", output.Host.Type),
	}, validEntryFilter)
}

type InstanceMetadata struct{}

func (i *InstanceMetadata) Apply(output *ecsops.OpsFusionSearchOutput) []*cmdb.ResourceEntry {
	var entries []*cmdb.ResourceEntry

	// 遍历所有实例
	if output.Host != nil {
		entries = append(entries, lo.Filter([]*cmdb.ResourceEntry{
			createEntry("InstanceId", "实例ID", output.Host.Id),
			createEntry("InstanceName", "实例名称", output.Host.Hostname),
			createEntry("InstanceType", "实例类型", output.Host.Type),
			createEntry("InstanceStatus", "实例状态", output.Host.Status),
			createEntry("HostType", "主机类型", output.Host.Type),
		}, validEntryFilter)...)
	}

	return entries
}

// 核心转换方法
func Convert(output *ecsops.OpsFusionSearchOutput) []*cmdb.ResourceEntry {
	factory := NewECSStrategyFactory()
	var entries []*cmdb.ResourceEntry

	for _, strategy := range factory.strategies {
		entries = append(entries, strategy.Apply(output)...)
	}

	return lo.UniqBy(
		lo.Filter(entries, validEntryFilter),
		func(e *cmdb.ResourceEntry) string { return e.Key },
	)
}

// 创建资源条目工具方法
func createEntry(key, name string, value interface{}) *cmdb.ResourceEntry {
	// 处理指针类型值
	if v, ok := value.(*string); ok {
		value = lo.FromPtr(v)
	}

	// 转换为字符串
	var strValue string
	switch v := value.(type) {
	case string:
		strValue = v
	case int, int32, int64:
		strValue = fmt.Sprintf("%d", v)
	case bool:
		strValue = strconv.FormatBool(v)
	default:
		strValue = fmt.Sprintf("%v", value)
	}

	// 处理空值
	if strValue == "" || strValue == "<nil>" {
		strValue = "N/A"
	}

	return &cmdb.ResourceEntry{
		Key:   key,
		Name:  name,
		Value: strValue,
	}
}
func validEntryFilter(entry *cmdb.ResourceEntry, _ int) bool {
	return entry.Value != "N/A" && entry.Value != ""
}
