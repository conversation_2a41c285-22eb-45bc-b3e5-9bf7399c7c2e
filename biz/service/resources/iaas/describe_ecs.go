package iaas

import (
	"context"
	"fmt"
	"strings"

	fh_hertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/fh_openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/fh_openapi"

	"code.byted.org/gopkg/logs"
	"code.byted.org/iaasng/volcengine-go-ops-sdk/ops"
	"code.byted.org/iaasng/volcengine-go-ops-sdk/service/ecsops"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_ecs"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
)

type EcsResourceHandler struct {
	Logger *utils.ModuleLogger
}

// NewEcsResourceHandler 创建新的EcsResourceHandler实例
func NewEcsResourceHandler() *EcsResourceHandler {
	return &EcsResourceHandler{
		Logger: utils.NewModuleLogger("EcsResourceHandler"),
	}
}

func (h *EcsResourceHandler) GetResources(ctx context.Context, c *app.RequestContext) error {
	return h.DescribeEcsInstanceById(ctx, c)
}

func (h *EcsResourceHandler) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	return h.DescribeEcsInstanceDependencyById(ctx, c)
}

func (h *EcsResourceHandler) getGrafanaURL(ctx context.Context, instanceID, region string) (string, error) {
	// 获取产品分类信息
	productCategory, err := product_category.GetProductCategoryService().GetProductCategoryByResourceType(ctx, "ecsID")
	if err != nil || productCategory == nil || productCategory.GrafanaTemplate == nil {
		return "", fmt.Errorf("get product category failed: %v", err)
	}

	// 渲染模板
	url := strings.ReplaceAll(*productCategory.GrafanaTemplate, "{{.Region}}", region)
	url = strings.ReplaceAll(url, "{{.InstanceID}}", instanceID)

	return url, nil
}

func (h *EcsResourceHandler) DescribeEcsInstanceById(ctx context.Context, c *app.RequestContext) error {
	logs.SetLevel(logs.LevelInfo)
	params := &describe_ecs.DescribeInstanceInput{}

	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}

	// 从tcc获取ak sk
	config := tcc.GetServiceConfig()
	sess, err := session.NewSession(ops.DefaultConfig(config.EcsAccount.AK, config.EcsAccount.SK))
	if err != nil {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}

	ecsClient := ecsops.New(sess)

	respData, err := ecsClient.OpsFusionSearch(&ecsops.OpsFusionSearchInput{KeyWord: volcengine.String(params.Resources.Instances[0])})
	if err != nil {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}

	if respData.Metadata.HTTPCode == 404 {
		// handler.ErrorResponse(ctx, c, errorcode.ErrDataNotFound.WithMessage(respData.Metadata.Error.Message))
		return errorcode.ErrDataNotFound.WithMessage(respData.Metadata.Error.Message)
	}

	if respData.Metadata.HTTPCode != 200 {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithMessage(respData.Metadata.Error.Message))
		return errorcode.ErrRequestParamInvalid.WithMessage(respData.Metadata.Error.Message)
	}

	resourceResp := &cmdb.DescribeResourceResponse{}
	if respData.Instance != nil {
		resourceMetadata := []*cmdb.ResourceEntry{
			cmdb.CreateEntry("InstanceId", "实例ID", *respData.Instance.Id),
			cmdb.CreateEntry("InstanceName", "实例名称", *respData.Instance.InstanceName),
			cmdb.CreateEntry("Region", "地域", *respData.Region),
			cmdb.CreateEntry("Zone", "可用区", *respData.Instance.ZoneId),
			cmdb.CreateEntry("Status", "实例状态", *respData.Instance.Status),
			cmdb.CreateEntry("ImageId", "镜像ID", *respData.Instance.ImageId),
			cmdb.CreateEntry("VpcId", "VPC ID", *respData.Instance.VpcId),
			cmdb.CreateEntry("OsName", "操作系统", *respData.Instance.OsName),
			cmdb.CreateEntry("OsType", "系统类型", *respData.Instance.OsType),
			cmdb.CreateEntry("Cluster", "集群ID", *respData.Instance.Cluster.Id),
			cmdb.CreateEntry("HostIP", "宿主机IP", *respData.Instance.Host.Ipv4),
			cmdb.CreateEntry("HostID", "宿主机ID", *respData.Instance.Host.Id),
			cmdb.CreateEntry("KeyPairId", "密钥对ID", *respData.Instance.KeyPairId),
			cmdb.CreateEntry("KeyPairName", "密钥对名称", *respData.Instance.KeyPairName),
			cmdb.CreateEntry("ProjectName", "项目名称", *respData.Instance.ProjectName),
		}

		if respData.Instance.InstanceType != nil {
			resourceMetadata = append(resourceMetadata,
				cmdb.CreateEntry("Cpu", "CPU", *respData.Instance.InstanceType.Cpu),
				cmdb.CreateEntry("Architecture", "CPU架构", *respData.Instance.InstanceType.Architecture),
				cmdb.CreateEntry("Id", "规格", *respData.Instance.InstanceType.Id),
				cmdb.CreateEntry("Mem", "内存", *respData.Instance.InstanceType.Mem),
			)
		}

		var premaryNetworkInfo [][]*cmdb.ResourceEntry
		premaryNetworkInfo = lo.FilterMap(respData.Instance.NetworkInterfaces, func(item *ecsops.NetworkInterfaceForOpsFusionSearchOutput, _ int) ([]*cmdb.ResourceEntry, bool) {
			if *item.Type == "primary" {
				securityGroupIds := lo.Map(item.SecurityGroupIds, func(id *string, _ int) string {
					return *id
				})
				return []*cmdb.ResourceEntry{
					cmdb.CreateEntry("interfaceID", "网卡ID", item.NetworkInterfaceId),
					cmdb.CreateEntry("ipaddress", "IP地址", item.PrimaryIpAddress),
					cmdb.CreateEntry("status", "状态", item.Status),
					cmdb.CreateEntry("VPC ID", "VPC 名称", item.VpcId),
					cmdb.CreateEntry("subnetID", "子网ID", item.SubnetId),
					cmdb.CreateEntry("securityGroup", "安全组IDs", strings.Join(securityGroupIds, ",")),
				}, true
			}
			return nil, false
		})

		if len(premaryNetworkInfo) == 0 {
			premaryNetworkInfo = append(premaryNetworkInfo, []*cmdb.ResourceEntry{
				cmdb.CreateEntry("interfaceID", "网卡ID", ""),
				cmdb.CreateEntry("ipaddress", "IP地址", ""),
				cmdb.CreateEntry("status", "状态", ""),
				cmdb.CreateEntry("VPC ID", "VPC 名称", ""),
				cmdb.CreateEntry("subnetID", "子网ID", ""),
				cmdb.CreateEntry("securityGroup", "安全组IDs", ""),
			})
		}

		//根据账号信息获取客户的客户编码，生成烽火平台的跳转链接
		customerInfo := &cmdb.CustomerInfo{
			AccountId: respData.Instance.AccountId,
		}
		resp, err := fh_openapi.ListCustomerVolcAccount(&fh_hertz.ListCustomerVolcAccountRequest{
			VolcAccountID: []string{*respData.Instance.AccountId},
		})
		if err != nil {
			// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
			return errorcode.ErrRequestParamInvalid.WithArgs(err)
		}
		if len(resp.Result) <= 0 {
			customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", *respData.Instance.AccountId))
		} else {
			if resp.Result[0] != nil {
				customerInfo.CustomerLink = lo.ToPtr(fh_openapi.GetCustomerInfoLink(resp.Result[0].CustomerNumber))
				customerInfo.CustomerName = resp.Result[0].CustomerName
				logs.Infof("customerInfo.CustomerName: %v", *customerInfo.CustomerName)
			} else {
				customerInfo.CustomerName = lo.ToPtr(fmt.Sprintf("volcAccountID: %s未找到客户信息", *respData.Instance.AccountId))
			}
		}

		resourceResp = &cmdb.DescribeResourceResponse{
			ResourceType:        "EcsID",
			ResourceMetadata:    resourceMetadata,
			ResourceNetworkInfo: premaryNetworkInfo[0],
			OriginMsg:           respData,
			OriginPlatformLink:  fmt.Sprintf("https://awacs.byted.org/data/instance/detail?instanceIds:list=%s&region=%s", *respData.Instance.Id, *respData.Region),
			CustomerInfo:        customerInfo,
		}

		// 获取Grafana链接
		if grafanaURL, err := h.getGrafanaURL(ctx, *respData.Instance.Id, *respData.Region); err == nil {
			resourceResp.GrafanaLink = &grafanaURL
			resourceResp.ResourceMetadata = append(resourceResp.ResourceMetadata,
				cmdb.CreateEntry("GrafanaLink", "监控链接", grafanaURL))
		} else {
			logger.CtxWarnf(ctx, "get grafana url failed: %v", err)
		}
	}
	ehttp.DoResponse(ctx, c, resourceResp)
	return nil
}

func (h *EcsResourceHandler) DescribeEcsInstanceDependencyById(ctx context.Context, c *app.RequestContext) error {
	logs.SetLevel(logs.LevelInfo)
	params := &describe_ecs.DescribeInstanceInput{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		return err
	}

	respData, err := cmdb.GetEcsResource(ctx, params.Resources.Instances[0])
	if err != nil {
		return err
	}

	result := &cmdb.DescribeResourceDependencyResponse{}
	if respData.Instance != nil {
		if respData.Instance.Id != nil {
			result.InstanceID = *respData.Instance.Id
		}

		// 添加云硬盘信息
		ebs := &cmdb.Product{
			ProductName: "云硬盘",
		}
		ebsHeader := []*cmdb.Field{
			cmdb.AddField("ResourceID", "资源ID"),
			cmdb.AddField("ResourceName", "资源名称"),
			cmdb.AddField("VolumeType", "硬盘类型"),
			cmdb.AddField("Size", "硬盘大小"),
			cmdb.AddField("BillingType", "付费类型"),
		}
		ebsItems := make([]map[string]interface{}, 0)
		if len(respData.Instance.Volumes) > 0 {
			for _, volume := range respData.Instance.Volumes {
				item := map[string]interface{}{
					"ResourceID":   cmdb.AddValue(volume.VolumeId),
					"ResourceName": cmdb.AddValue(volume.VolumeName),
					"VolumeType":   cmdb.AddValue(volume.VolumeType),
					"Size":         cmdb.AddValue(volume.Size),
					"BillingType":  cmdb.ConvertBillingType(volume.BillingType),
				}
				ebsItems = append(ebsItems, item)
			}
		}
		ebs.Items = ebsItems
		ebs.Header = ebsHeader
		result.Products = append(result.Products, ebs)

		// 添加网卡信息
		network := &cmdb.Product{
			ProductName: "网卡",
		}
		networkHeader := []*cmdb.Field{
			cmdb.AddField("ResourceID", "资源ID"),
			cmdb.AddField("ResourceName", "资源名称"),
			cmdb.AddField("Status", "状态"),
			cmdb.AddField("PrimaryIP", "IP地址"),
			cmdb.AddField("VpcId", "VPC ID"),
			cmdb.AddField("VpcName", "VPC 名称"),
			cmdb.AddField("SubnetId", "子网ID"),
			cmdb.AddField("SecurityGroupIds", "安全组IDs"),
			cmdb.AddField("Type", "类型"),
			cmdb.AddField("MacAddress", "MAC地址"),
			cmdb.AddField("EipAddress", "EIP 地址"),
		}
		networkItems := make([]map[string]interface{}, 0)
		if len(respData.Instance.NetworkInterfaces) > 0 {
			for _, networkInterface := range respData.Instance.NetworkInterfaces {
				item := map[string]interface{}{
					"ResourceID":       cmdb.AddValue(networkInterface.NetworkInterfaceId),
					"ResourceName":     cmdb.AddValue(networkInterface.NetworkInterfaceName),
					"Status":           cmdb.AddValue(networkInterface.Status),
					"PrimaryIP":        cmdb.AddValue(networkInterface.PrimaryIpAddress),
					"VpcId":            cmdb.AddValue(networkInterface.VpcId),
					"VpcName":          cmdb.AddValue(networkInterface.VpcName),
					"SubnetId":         cmdb.AddValue(networkInterface.SubnetId),
					"SecurityGroupIds": networkInterface.SecurityGroupIds,
					"Type":             cmdb.AddValue(networkInterface.Type),
					"MacAddress":       cmdb.AddValue(networkInterface.MacAddress),
					"EipAddress":       cmdb.AddValue(networkInterface.EipAddress),
				}
				networkItems = append(networkItems, item)
			}
		}
		network.Items = networkItems
		network.Header = networkHeader
		result.Products = append(result.Products, network)

		ehttp.DoResponse(ctx, c, result)
	}

	return nil
}

// 保留其他原有方法...
