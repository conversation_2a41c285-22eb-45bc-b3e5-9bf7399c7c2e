package resources

import (
	"testing"

	dr "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
)

func TestEcsResourceHandlerMatch(t *testing.T) {
	handler := &EcsResourceHandler{}

	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{"合规小写", "i-ydunpmcxs0qc6ikv4qfv", true}, // 22位小写
		{"大写不合规", "I-YDUNPMCXS0QC6IKV", false},   // 17位大写
		{"非法前缀", "u-ydunpmcxs0qc6ikv", false},    // u-开头
		{"长度不足", "i-ydunpmcxs0qc6ik", false},     // 16位
		{"长度超长", "i-ydunpmcxs0qc6ikv4qf", false}, // 18位
		{"特殊字符", "i-ydunpmcxs0qc6ik_", false},    // 包含下划线
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &dr.DescribeResourceRequest{
				Resources: &dr.Resources{
					Instances: []string{tt.input},
				},
			}
			if got := handler.Match(nil, req); got != tt.expected {
				t.Errorf("Match() = %v, want %v (input: %s)", got, tt.expected, tt.input)
			}
		})
	}

	t.Run("明确指定resourceType", func(t *testing.T) {
		req := &dr.DescribeResourceRequest{
			Resources: &dr.Resources{
				ResourceType: "ecsID",
			},
		}
		if !handler.Match(nil, req) {
			t.Error("应该匹配ecsID类型")
		}
	})
}
