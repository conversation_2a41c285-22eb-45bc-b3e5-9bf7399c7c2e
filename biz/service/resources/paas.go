package resources

import (
	"context"
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/cloudwego/hertz/pkg/app"
)

type PaaSResourceHandler struct {
	Logger *utils.ModuleLogger
}

func (p *PaaSResourceHandler) RenderGrafanaURL(ctx context.Context, instanceID string, template string) (string, error) {
	// 获取PaaS资源信息
	handler := paasSubResourceHandlerFactory.GetHandler(&describe_resource.DescribeResourceRequest{
		Resources: &describe_resource.Resources{
			Instances: []string{instanceID},
		},
	})
	if handler == nil {
		return "", fmt.Errorf("no handler found for instance: %s", instanceID)
	}

	// 调用子handler的RenderGrafanaURL方法
	if renderer, ok := handler.(interface {
		RenderGrafanaURL(ctx context.Context, instanceID string, template string) (string, error)
	}); ok {
		return renderer.RenderGrafanaURL(ctx, instanceID, template)
	}

	return "", fmt.Errorf("render grafana url not supported for this resource")
}

func (p *PaaSResourceHandler) GetResources(ctx context.Context, c *app.RequestContext) error {
	// logger := p.Logger.WithFunc("GetResources For PaaS")
	// params := &describe_resource.DescribeResourceRequest{}

	err := paasSubResourceHandlerFactory.HandlerResource(ctx, c)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return nil
	}
	return nil
}

func (h *PaaSResourceHandler) Match(ctx context.Context, input *describe_resource.DescribeResourceRequest) bool {
	return paasSubResourceHandlerFactory.MatchPaaS(input)
}

func (h *PaaSResourceHandler) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	err := paasSubResourceHandlerFactory.HandlerResourceDependency(ctx, c)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return nil
	}
	return nil
}
