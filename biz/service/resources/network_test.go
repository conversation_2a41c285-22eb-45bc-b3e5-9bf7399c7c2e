package resources

// import (
// 	"testing"

// 	dr "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
// )

// func TestEipResourceHandleratch(t *testing.T) {
// 	handler := &EipResourceHandler{}

// 	tests := []struct {
// 		name     string
// 		input    *dr.DescribeResourceRequest
// 		expected bool
// 	}{
// 		{
// 			name: "匹配eipID类型",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "eipID",
// 					Instances: []string{
// 						"eip-2feg8v5x7r5k9s3c4q9p6z8d", // Valid format
// 						"eip-9a7b3c2d1e0f5g6h7j8k9l0m", // Valid format
// 					},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "匹配natID类型",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "natID",
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "匹配clbInstanceID类型",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "clbInstanceID",
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "类型不匹配但实例匹配eip格式",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{"eip-abc123abc123abc1"},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "类型不匹配但实例匹配ngw格式",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{"ngw-xyz987xyz987xyz9"},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "类型不匹配但实例匹配clb格式",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{"clb-rrctfk1zwxs0v0x57"},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "mixed_match_invalid_type_with_valid_instance",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "invalidType",
// 					Instances:    []string{"eip-abc123abc123abc1"},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "complete_invalid_match",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "invalidType",
// 					Instances:    []string{"invalid-id"},
// 				},
// 			},
// 			expected: false,
// 		},
// 		{
// 			name: "boundary_test_short_id",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{"eip-abc123"},
// 				},
// 			},
// 			expected: false,
// 		},
// 		{
// 			name: "boundary_test_special_characters",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{"eip-!@#$%^&*()_+{}|:<>?~"},
// 				},
// 			},
// 			expected: false,
// 		},
// 		{
// 			name: "mixed_valid_and_invalid_instances",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					Instances: []string{
// 						"eip-2feg8v5x7r5k9s3c4q9p6z8d", // valid
// 						"invalid-id",                   // invalid
// 						"eip-9a7b3c2d1e0f5g6h7j8k9l0m", // valid
// 					},
// 				},
// 			},
// 			expected: true,
// 		},
// 		{
// 			name: "empty_instances_list_with_valid_type",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "eipID",
// 					Instances:    []string{},
// 				},
// 			},
// 			expected: true, // Type match should succeed
// 		},
// 		{
// 			name: "nil_instances_list_with_valid_type",
// 			input: &dr.DescribeResourceRequest{
// 				Resources: &dr.Resources{
// 					ResourceType: "eipID",
// 				},
// 			},
// 			expected: true,
// 		},
// 	}

// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			result := handler.Match(nil, tt.input)
// 			if result != tt.expected {
// 				t.Errorf("Match failed for test case %q\nInput: %+v\nGot: %v, Want: %v",
// 					tt.name,
// 					tt.input.Resources,
// 					result,
// 					tt.expected)
// 			}
// 		})
// 	}
// }
