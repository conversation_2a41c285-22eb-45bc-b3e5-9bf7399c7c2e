package resources

import (
	"context"
	"regexp"
	"strings"

	"code.byted.org/gopkg/logs"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/resources/iaas"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	dr "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/samber/lo"
)

type EcsResourceHandler struct {
	Logger *utils.ModuleLogger
}

// GetResources 是 EcsResourceHandler 结构体的一个方法，用于获取资源信息
func (h *EcsResourceHandler) GetResources(ctx context.Context, c *app.RequestContext) error {
	// 设置日志级别为 Info
	logs.SetLevel(logs.LevelInfo)
	// 创建一个带有特定功能标签的日志记录器
	logger := h.Logger.WithFunc("GetResources For Iaas")
	// 初始化一个描述资源的请求参数结构体
	params := &describe_resource.DescribeResourceRequest{}

	// 将请求体解析为 JSON 格式，并填充到 params 结构体中
	err := utils.JsonUnmarshal(c.Request.Body(), params)

	// 如果解析过程中出现错误，返回错误响应
	if err != nil {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}

	// 如果请求参数中没有 Resources 字段，返回错误响应
	if params.Resources == nil {
		// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Resources"))
		return errorcode.ErrRequestParamInvalid.WithArgs("Resources")
	}

	// 单实例查询，支持单个实例ID模糊查询
	if params.Dimension == "instance" {
		if len(params.Resources.Instances) == 0 {
			// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Instance type need only one InstanceId."))
			return errorcode.ErrRequestParamInvalid.WithArgs("Instance type need only one InstanceId.")
		}
		if len(params.Resources.Instances) > 1 {
			// handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Query type Instance only one instanceid needed."))
			return errorcode.ErrRequestParamInvalid.WithArgs("Query type Instance only one instanceid needed.")
		}
		logger.CtxInfo(ctx, "GetResources for iaas input ", params)
		handler := iaas.NewEcsResourceHandler()
		return handler.GetResources(ctx, c)
	}
	// 账号维度查询，支持多实例，需要传入regionId、AccountID
	// if params.Dimension == "account" {
	// 	if *params.AccountId == "" || strings.TrimSpace(*params.Region) == "" {
	// 		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Query type Account need AccountId、Region"))
	// 		return errors.New("Query type Account need AccountId、Region")
	// 	}
	// 	iaas.DescribeEcsInstanceByAccount(ctx, c)
	// }

	return nil

}

func (h *EcsResourceHandler) Match(ctx context.Context, inputInfo *dr.DescribeResourceRequest) bool {
	// 优先检查明确指定的resourceType
	if strings.TrimSpace(inputInfo.Resources.ResourceType) != "" {
		return lo.Contains([]string{"ecsID", "EcsID"}, strings.TrimSpace(inputInfo.Resources.ResourceType))
	}

	// 当resourceType为空时，进行实例ID正则匹配
	regex := regexp.MustCompile(`(?i)^i-[a-z0-9]{20}$`) // i-前缀后跟17位字符，总长度19
	for _, resource := range inputInfo.Resources.Instances {
		if regex.MatchString(resource) {
			return true
		}
	}

	return false
}

func (h *EcsResourceHandler) GetResourcesDependency(ctx context.Context, c *app.RequestContext) error {
	// 设置日志级别为 Info
	logs.SetLevel(logs.LevelInfo)
	// 创建一个带有特定功能标签的日志记录器
	logger := h.Logger.WithFunc("GetResourcesDependency For Iaas")
	// 初始化一个描述资源的请求参数结构体
	params := &describe_resource.DescribeResourceRequest{}

	// 将请求体解析为 JSON 格式，并填充到 params 结构体中
	err := utils.JsonUnmarshal(c.Request.Body(), params)

	// 如果解析过程中出现错误，返回错误响应
	if err != nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(err))
		return err
	}

	// 如果请求参数中没有 Resources 字段，返回错误响应
	if params.Resources == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Resources"))
		return err
	}

	// 单实例查询，支持单个实例ID模糊查询
	if params.Dimension == "instance" {
		if len(params.Resources.Instances) == 0 {
			handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Instance type need only one InstanceId."))
			return err
		}
		if len(params.Resources.Instances) > 1 {
			handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Query type Instance only one instanceid needed."))
			return err
		}
		logger.CtxInfo(ctx, "GetResources for iaas input ", params)
		handler := iaas.NewEcsResourceHandler()
		return handler.GetResourcesDependency(ctx, c)
	}
	// 账号维度查询，支持多实例，需要传入regionId、AccountID
	// if params.Dimension == "account" {
	// 	if *params.AccountId == "" || strings.TrimSpace(*params.Region) == "" {
	// 		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Query type Account need AccountId、Region"))
	// 		return errors.New("Query type Account need AccountId、Region")
	// 	}
	// 	iaas.DescribeEcsInstanceByAccount(ctx, c)
	// }

	return nil
}
