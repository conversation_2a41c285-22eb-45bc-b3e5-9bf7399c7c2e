package resources

// import (
// 	"context"
// 	"fmt"
// 	"sync"

// 	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
// 	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
// 	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
// 	"code.byted.org/volcengine-support/cloud-sherlock/utils"
// 	"github.com/bytedance/gopkg/util/logger"
// 	"github.com/cloudwego/hertz/pkg/app"
// )

// var networkSubResourceHandlerFactory *NetworkSubResourceHandlerFactory

// func init() {
// 	networkSubResourceHandlerFactory = NewNetworkResourceHandlerFactory()
// 	networkSubResourceHandlerFactory.RegisterHandler(&cmdb.EipInstance{})
// 	networkSubResourceHandlerFactory.RegisterHandler(&cmdb.NatInstance{})
// 	networkSubResourceHandlerFactory.RegisterHandler(&cmdb.CLBInstance{})
// }

// type NetworkSubResourceHandler interface {
// 	BuildListEntitiesRequest(ctx context.Context, instances []string) (*cmdb.ListEntitiesRequest, error)
// 	Match(input *describe_resource.DescribeResourceRequest) bool // 标识是否支持处理该产品/维度
// 	GetResources(ctx context.Context, c *app.RequestContext) error
// 	GetResourcesDependency(ctx context.Context, c *app.RequestContext) error
// }

// type NetworkSubResourceHandlerFactory struct {
// 	handlers sync.Map
// }

// var factory1 *NetworkSubResourceHandlerFactory

// var once1 sync.Once

// // GetResourceHandler 获取资源处理器工厂单例
// func NewNetworkResourceHandlerFactory() *NetworkSubResourceHandlerFactory {
// 	once1.Do(func() {
// 		factory1 = &NetworkSubResourceHandlerFactory{
// 			handlers: sync.Map{},
// 		}
// 	})
// 	return factory1
// }

// // RegisterHandler 注册资源处理器
// func (f *NetworkSubResourceHandlerFactory) RegisterHandler(handler NetworkSubResourceHandler) {
// 	f.handlers.Store(handler, handler) // Handler 自身作为 key 和 value 方便查找
// 	logger.Infof("register resource  handler: %v", handler)
// }

// func (f *NetworkSubResourceHandlerFactory) GetHandler(input *describe_resource.DescribeResourceRequest) NetworkSubResourceHandler {
// 	var foundHandler NetworkSubResourceHandler
// 	f.handlers.Range(func(key, value interface{}) bool {
// 		h, ok := key.(NetworkSubResourceHandler)
// 		if ok && h.Match(input) {
// 			foundHandler = h
// 			return false // 找到第一个匹配的处理器后停止遍历
// 		}

// 		return true // 继续遍历下一个处理器
// 	})
// 	return foundHandler
// }

// func (f *NetworkSubResourceHandlerFactory) HandlerResource(ctx context.Context,
// 	c *app.RequestContext) error {
// 	params := &describe_resource.DescribeResourceRequest{}
// 	err := utils.JsonUnmarshal(c.Request.Body(), params)
// 	if err != nil {
// 		return errorcode.ErrRequestParamInvalid.WithArgs(err)
// 	}
// 	if params.Resources == nil {
// 		return errorcode.ErrRequestParamInvalid.WithArgs("Resources")
// 	}
// 	product := params.Resources.Product
// 	subProduct := params.Resources.SubProduct
// 	handler := f.GetHandler(params)
// 	if handler == nil {
// 		return errorcode.ErrCommonInternalError.WithArgs(fmt.Sprintf("No handler found for product: %s, subProduct: %s.", product, subProduct))
// 	}
// 	return handler.GetResources(ctx, c) // 调用找到的 Handler 的 GetResource 方法
// }

// func (f *NetworkSubResourceHandlerFactory) HandlerResourceDependency(ctx context.Context,
// 	c *app.RequestContext) error {
// 	params := &describe_resource.DescribeResourceRequest{}
// 	err := utils.JsonUnmarshal(c.Request.Body(), params)
// 	if err != nil {
// 		return errorcode.ErrRequestParamInvalid.WithArgs(err)
// 	}
// 	if params.Resources == nil {
// 		return errorcode.ErrRequestParamInvalid.WithArgs("Resources")
// 	}
// 	product := params.Resources.Product
// 	subProduct := params.Resources.SubProduct
// 	handler := f.GetHandler(params)
// 	if handler == nil {
// 		return errorcode.ErrCommonInternalError.WithArgs(fmt.Sprintf("No handler found for product: %s, subProduct: %s.", product, subProduct))
// 	}
// 	return handler.GetResourcesDependency(ctx, c) // 调用找到的 Handler 的 GetResource 方法
// }

// func (f *NetworkSubResourceHandlerFactory) MatchNetwork(input *describe_resource.DescribeResourceRequest) bool {
// 	// var foundHandler NetworkSubResourceHandler
// 	matched := false
// 	f.handlers.Range(func(key, value interface{}) bool {
// 		h, ok := key.(NetworkSubResourceHandler)
// 		if !ok {
// 			return true
// 		}
// 		if h.Match(input) {
// 			matched = true
// 			return false // 找到第一个匹配的处理器后停止遍历
// 		}

// 		return true // 继续遍历下一个处理器
// 	})
// 	return matched
// }
