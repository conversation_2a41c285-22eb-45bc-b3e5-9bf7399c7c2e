package app_ak_sk

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"context"
	"errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/aksk"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetAppAkSkService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("GetAkSkRunService"),
		}
	})
	return service
}

func (s *Service) CreateAppAkSk(ctx context.Context, req *aksk.CreateAppAkSkReq) (*aksk.CreateAppAkSkResult, error) {
	logger := s.logger.WithFunc("CreateAppAkSk")
	if req.AppID == "" {
		logger.CtxError(ctx, "AppID is empty")
		return nil, errorcode.ErrRequestParamInvalid.WithArgs("AppID")
	}
	// 检查 AppID 是否已存在
	appAkSk, err := dal.FindAppAkSkByAppID(ctx, req.AppID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxError(ctx, "Failed to find AppAkSk: %v", err)
		return nil, errorcode.ErrCommonInternalError
	}
	if appAkSk != nil {
		logger.CtxError(ctx, "AppID already exists")
		return nil, errorcode.ErrDataAlreadyExist
	}

	// 生成 AppKey 和 AppSecret
	appKey, err := generateRandomString(32)
	if err != nil {
		logger.CtxError(ctx, "Failed to generate AppKey: %v", err)
		return nil, errorcode.ErrCommonInternalError
	}
	appSecret, err := generateRandomString(32)
	if err != nil {
		logger.CtxError(ctx, "Failed to generate AppSecret: %v", err)
		return nil, errorcode.ErrCommonInternalError
	}
	encryptedAppSecret, err := Encrypt([]byte(appSecret))
	if err != nil {
		logger.CtxError(ctx, "Encrypt Name field err: %v", err)
		return nil, err
	}
	appAkSk = &model.AppAkSk{
		AppID:       req.AppID,
		AppName:     req.AppName,
		AppSecretEn: encryptedAppSecret,
		AppKey:      appKey,
	}
	if req.OutDateTime != nil {
		outDateTime, err := utils.StringToTime(*req.OutDateTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, errorcode.ErrRequestParamInvalid.WithArgs("OutDateTime")
		}
		appAkSk.OutDateTime = &outDateTime
	}
	err = dal.CreateAppAkSk(ctx, appAkSk)
	if err != nil {
		logger.CtxError(ctx, "Failed to create AppAkSk: %v", err)
		return nil, errorcode.ErrCommonInternalError
	}
	return &aksk.CreateAppAkSkResult{
		AppKey:    appKey,
		AppSecret: appSecret,
	}, nil
}

// 更新AppAkSk
func (s *Service) UpdateAppAkSk(ctx context.Context, req *aksk.UpdateAppAkSkReq) error {
	logger := s.logger.WithFunc("UpdateAppAkSk")
	if req.AppID == "" {
		logger.CtxError(ctx, "AppID is empty")
		return errorcode.ErrRequestParamInvalid.WithArgs("AppID")
	}
	appAkSk, err := dal.FindAppAkSkByAppID(ctx, req.AppID)
	if err != nil {
		logger.CtxError(ctx, "Failed to find AppAkSk: %v", err)
		return errorcode.ErrDataNotFound.WithArgs("AppID")
	}
	if req.AppName != nil {
		appAkSk.AppName = req.AppName
	}
	if req.OutDateTime != nil && *req.OutDateTime != "" {
		outDateTime, err := utils.StringToTime(*req.OutDateTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return errorcode.ErrRequestParamInvalid.WithArgs("OutDateTime")
		}
		appAkSk.OutDateTime = &outDateTime
	}
	err = dal.UpdateAppAkSk(ctx, appAkSk)
	if err != nil {
		logger.CtxError(ctx, "Failed to update AppAkSk: %v", err)
		return errorcode.ErrCommonInternalError
	}
	return nil
}

func (s *Service) QueryAppAkSkList(ctx context.Context, req *aksk.QueryAppAkSkListReq) (*aksk.QueryAppAkSkListResult, error) {
	logger := s.logger.WithFunc("QueryAppAkSkList")
	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	appAkSk := &model.AppAkSk{}
	if req.AppID != nil {
		appAkSk.AppID = *req.AppID
	}
	if req.AppName != nil {
		appAkSk.AppName = req.AppName
	}
	if req.AppKey != nil {
		appAkSk.AppKey = *req.AppKey
	}
	if req.OutDateTime != nil {
		outDateTime, err := utils.StringToTime(*req.OutDateTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, errorcode.ErrRequestParamInvalid.WithArgs("OutDateTime")
		}
		appAkSk.OutDateTime = &outDateTime
	}
	appAkSkList, total, err := dal.QueryAppAkSkList(ctx, appAkSk, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "Failed to query AppAkSkList: %v", err)
		return nil, errorcode.ErrDataNotFound
	}
	appAkSkListResult := make([]*aksk.AppAkSk, 0)
	for _, a := range appAkSkList {
		akskResult := &aksk.AppAkSk{
			AppID:   a.AppID,
			AppName: a.AppName,
			AppKey:  a.AppKey,
		}
		decryptedAppSecret, err := Decrypt(a.AppSecretEn)
		if err != nil {
			logger.CtxError(ctx, "Decrypt AppSecret field err: %v", err)
			return nil, err
		}
		akskResult.AppSecret = string(decryptedAppSecret)
		if a.OutDateTime != nil {
			outDateTime, err := utils.TimeToString(*a.OutDateTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, errorcode.ErrCommonInternalError
			}
			akskResult.OutDateTime = lo.ToPtr(outDateTime)
		}
		appAkSkListResult = append(appAkSkListResult, akskResult)
	}
	return &aksk.QueryAppAkSkListResult{
		AppAkSkList: appAkSkListResult,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

func (s *Service) DeleteAppAkSk(ctx context.Context, req *aksk.DeleteAppAkSkReq) error {
	logger := s.logger.WithFunc("DeleteAppAkSk")
	if req.AppID == "" {
		logger.CtxError(ctx, "AppID is empty")
		return errorcode.ErrRequestParamInvalid.WithArgs("AppID")
	}
	appAkSk, err := dal.FindAppAkSkByAppID(ctx, req.AppID)
	if err != nil {
		logger.CtxError(ctx, "Failed to find AppAkSk: %v", err)
		return errorcode.ErrDataNotFound.WithArgs("AppID")
	}
	err = dal.DeleteAppAkSk(ctx, appAkSk)
	if err != nil {
		logger.CtxError(ctx, "Failed to delete AppAkSk: %v", err)
		return errorcode.ErrCommonInternalError
	}
	return nil
}

func (s *Service) QueryAppAkSk(ctx context.Context, req *aksk.QueryAppAkSkReq) (*aksk.QueryAppAkSkResult, error) {
	logger := s.logger.WithFunc("QueryAppAkSk")
	if req.AppID == "" {
		logger.CtxError(ctx, "AppID is empty")
		return nil, errorcode.ErrRequestParamInvalid.WithArgs("AppID")
	}
	appAkSk, err := dal.FindAppAkSkByAppID(ctx, req.AppID)
	if err != nil {
		logger.CtxError(ctx, "Failed to find AppAkSk: %v", err)
		return nil, errorcode.ErrDataNotFound.WithArgs("AppID")
	}
	appAkSkResult := &aksk.AppAkSk{
		AppID:   appAkSk.AppID,
		AppName: appAkSk.AppName,
		AppKey:  appAkSk.AppKey,
	}
	decryptedAppSecret, err := Decrypt(appAkSk.AppSecretEn)
	if err != nil {
		logger.CtxError(ctx, "Decrypt AppSecret field err: %v", err)
		return nil, err
	}
	appAkSkResult.AppSecret = string(decryptedAppSecret)
	if appAkSk.OutDateTime != nil {
		outDateTime, err := utils.TimeToString(*appAkSk.OutDateTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, errorcode.ErrCommonInternalError
		}
		appAkSkResult.OutDateTime = lo.ToPtr(outDateTime)
	}
	return &aksk.QueryAppAkSkResult{
		AppAkSk: appAkSkResult,
	}, nil
}
