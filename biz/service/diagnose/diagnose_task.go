package diagnose

import (
	"context"
	"errors"
	"fmt"

	apiData "code.byted.org/cicd/byteflow/pkg/apiserver/models/api_data"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/byteflowclient"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

func TriggerDiagnoseTask(ctx context.Context, stateMachine string, input *statemachines.DiagnoseRequest, store map[string]interface{}) (*apiData.ExecutionDetails, error) {
	logger := utils.NewModuleLogger("TriggerDiagnoseTask")
	cfg := tcc.GetServiceConfig()
	// 通过JSON 定义初始化一个StateMachine
	sm, err := apiData.NewStateMachine(stateMachine)
	if err != nil {
		logger.CtxError(ctx, "[diagnose/TriggerDiagnoseTask] apiData.NewStateMachine error: %v", err)
		return nil, err
	}
	executionRequest := &apiData.StartExecutionRequest{
		Name:                 fmt.Sprintf("%v", input.DiagnoseTaskRunID),
		Input:                input, // 本次执行的输入
		NotifyExecutionEvent: true,  // 标准状态机都是触发后异步执行的，该参数表示是否发送执行结束的通知
		StateMachine:         sm,
		Store:                store}
	// 调用触发接口
	resp, err := byteflowclient.BFClient.TriggerStateMachine(ctx, cfg.ByteFlowConfig.AppToken, executionRequest)
	if err != nil {
		logger.CtxError(ctx, "[diagnose/TriggerDiagnoseTask] byteflowclient.BFClient.TriggerStateMachine error: %v", err)
		return nil, err
	}
	if resp.Code != 0 {
		logger.CtxError(ctx, "[diagnose/TriggerDiagnoseTask] byteflowclient.BFClient.TriggerStateMachine error: %v", resp.Err)
		return nil, errors.New(resp.Err)
	}

	executionID := resp.Data.ExecutionID

	// time.Sleep(time.Second * 2)

	getResp, err := byteflowclient.BFClient.DescribeExecution(ctx, cfg.ByteFlowConfig.AppToken, executionID)
	if err != nil {
		logger.CtxError(ctx, "[diagnose/TriggerDiagnoseTask] byteflowclient.BFClient.DescribeExecution error: %v", err)
		return nil, err
	}
	fmt.Printf("getResp: %+v\n", getResp)
	return resp.Data, nil

}
