// ignore_security_alert_file IDOR
package diagnose_template

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	diagnoseTemplateHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_template"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"sync"
	"time"
)

var (
	service *DiagnoseTemplateService
	once    sync.Once
)

type DiagnoseTemplateService struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseTemplateService() *DiagnoseTemplateService {
	once.Do(func() {
		service = &DiagnoseTemplateService{
			logger: utils.NewModuleLogger("DiagnoseTemplateService"),
		}
	})
	return service
}

func (s *DiagnoseTemplateService) RegisterDiagnoseTemplate(ctx context.Context, req *diagnoseTemplateHertz.RegisterDiagnoseTemplateRequest) (*diagnoseTemplateHertz.RegisterDiagnoseTemplateResult, error) {
	//todo:根据创建者的身份信息鉴权

	//注册逻辑：
	//1.先判断是否存在该诊断场景的记录
	//2.如果存在该诊断场景的记录，还需要判断该诊断场景是否已经被软删除，如果已经被软删除了，则需要先将该记录删除。
	//	如果不存在该诊断场景的记录，则创建新的记录
	//3.绑定诊断项，如果需要绑定诊断项，则需要判断该诊断项是否存在且是否未被软删除。
	//	如果不存在该诊断项的记录，或者该记录已经被软删除了则返回错误。
	//4.关联诊断项与场景模版，关联之前需要先判断该关联关系是否已经存在。如果存在返回错误，如果是被软删除的就删除关联重新创建。

	//鉴权通过后构造要插入的诊断模版
	s.logger.WithFunc("RegisterDiagnoseTemplate")
	now := time.Now()
	Inputs := ""
	if len(req.Inputs) > 0 {
		bytes, err := json.Marshal(req.Inputs)
		if err != nil {
			s.logger.CtxError(ctx, "json marshal error: %s", err)
			return nil, err
		}
		Inputs = string(bytes)
	}
	// todo:判断ProductCategoryID是否存在
	template := &model.DiagnoseTemplate{
		CreatorID:            &req.UserInfo.UserID,
		CreatorName:          &req.UserInfo.UserName,
		CreateTime:           &now,
		UpdateTime:           &now,
		UpdaterName:          &req.UserInfo.UserName,
		UpdaterID:            &req.UserInfo.UserID,
		DiagnoseTemplateName: &req.DiagnoseTemplateName,
		TemplateDescription:  &req.TemplateDescription,
		StateMachineInfo:     &req.StateMachineInfo,
		Suggestion:           &req.Suggestion,
		Status:               req.Status,
		ResponsiblePerson:    req.ResponsiblePerson,
		Inputs:               &Inputs,
		Product:              req.Product,
		Subproduct:           req.SubProduct,
		ResourceType:         req.ResourceType,
		ShowLevel:            lo.ToPtr(int32(req.ShowLevel)),
	}

	//通过productCategoryID查询对应的productCategory
	_, err := dal.QueryProductCategoryByID(ctx, req.ProductCategoryID)
	if err != nil {
		s.logger.WithFunc("RegisterDiagnoseItem").Error("QueryProductCategoryByID err:%v", err)
		return nil, err
	}
	template.ProductCategoryID = lo.ToPtr(int32(req.ProductCategoryID))

	//1.先判断是否存在该诊断场景的记录，不存在就直接添加
	res, err1 := dal.FindDiagnoseTemplateByName(ctx, req.DiagnoseTemplateName)
	if err1 != nil {
		if err1.Error() == "record not found" {
			err := dal.AddDiagnoseTemplate(ctx, template)
			if err != nil {
				s.logger.CtxError(ctx, "AddDiagnoseTemplate err:%v", err)
				return nil, err
			}
		} else {
			return nil, err1
		}
	} else if res.DeleteAt != nil {
		//2.如果存在该诊断场景的记录，还需要判断该诊断场景是否已经被软删除,如果是被软删除的就直接更新该数据
		errDelete := dal.SaveDiagnoseTemplate(ctx, template)
		if errDelete != nil {
			s.logger.CtxError(ctx, "SaveDiagnoseTemplate err:%v", errDelete)
			return nil, errDelete
		}
	} else {
		return nil, errorcode.ErrDataAlreadyExist
	}

	if len(req.DiagnosesItemIDs) > 0 {
		for _, itemID := range req.DiagnosesItemIDs {
			//1.先判断是否存在该itemID的记录
			item, errItem := dal.FindDiagnoseItemByID(ctx, itemID)
			if errItem != nil {
				s.logger.CtxError(ctx, "FindDiagnoseItemByID err:%v", errItem)
				if errItem.Error() == "record not found" {
					s.logger.CtxError(ctx, "item not found:%v", errItem)
					return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", itemID))
				}
				return nil, errItem
			}
			//2.如果存在该itemID的记录，还需要判断该Item是否已经被软删除
			if item.DeleteAt != nil {
				errDelete := dal.DeleteDiagnoseTemplate(ctx, template)
				if errDelete != nil {
					s.logger.CtxError(ctx, "DeleteDiagnoseTemplate err:%v", errDelete)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
				s.logger.CtxError(ctx, "item has been delete:%v", errDelete)
				return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
			}

			diagnoseTemplateItem := &model.ItemTemplate{
				DiagnoseTemplateID: template.DiagnoseTemplateID,
				DiagnoseItemID:     itemID,
				CreatorID:          &req.UserInfo.UserID,
				CreatorName:        &req.UserInfo.UserName,
			}
			//添加之前需要先判断该关联关系是否已经存在
			relation, errRelation := dal.FindRelationByItemIDAndTemplateID(ctx, itemID, template.DiagnoseTemplateID)
			if errRelation != nil {
				if errRelation.Error() == "record not found" {
					//记录不存在则添加记录
					err := dal.AddItemTemplate(ctx, diagnoseTemplateItem)
					if err != nil {
						s.logger.CtxError(ctx, "AddItemTemplate err:%v", err)
						return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
					}
					continue
				} else {
					s.logger.CtxError(ctx, "FindRelationByItemIDAndTemplateID err:%v", errRelation)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
			}
			//如果该记录被软删除了，则直接更新该记录
			if relation.DeleteAt != nil {
				err := dal.SaveItemTemplate(ctx, diagnoseTemplateItem)
				if err != nil {
					s.logger.CtxError(ctx, "UpdateItemTemplate err:%v", err)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
			}
		}
	}
	return &diagnoseTemplateHertz.RegisterDiagnoseTemplateResult{
		DiagnoseTemplateID: &template.DiagnoseTemplateID,
	}, nil
}

func (s *DiagnoseTemplateService) DeleteDiagnoseTemplate(ctx context.Context, req *diagnoseTemplateHertz.DeleteDiagnoseTemplateRequest) (*diagnoseTemplateHertz.DeleteDiagnoseTemplateResult, error) {
	s.logger.WithFunc("DeleteDiagnoseTemplate")
	//1.先软删除item_template关联表中templateId对应的数据
	//2.再软删除diagnose_template中的数据
	relations, err := dal.FindRelationByTemplateID(ctx, *req.DiagnoseTemplateID)
	if err != nil && err.Error() != "record not found" {
		s.logger.CtxError(ctx, "FindItemsByTemplateID err:%v", err)
		return nil, err
	}
	now := time.Now()
	if len(relations) > 0 {
		//存在关联，则软删除关联
		for _, relation := range relations {
			if relation.DeleteAt == nil {
				newRelation := &model.ItemTemplate{
					DiagnoseTemplateID: *req.DiagnoseTemplateID,
					DiagnoseItemID:     relation.DiagnoseItemID,
					DeleterName:        &req.UserInfo.UserName,
					DeleterID:          &req.UserInfo.UserID,
					DeleteAt:           &now,
				}
				err := dal.UpdateItemTemplate(ctx, newRelation)
				if err != nil {
					s.logger.WithFunc("DeleteDiagnoseTemplate").CtxError(ctx, fmt.Sprintf(" UpdateItemTemplate err :%v", err))
					return nil, err
				}
			}
		}
	}
	template := &model.DiagnoseTemplate{
		DiagnoseTemplateID: *req.DiagnoseTemplateID,
		DeleteAt:           &now,
		DeleterName:        &req.UserInfo.UserName,
		DeleterID:          &req.UserInfo.UserID,
	}
	err = dal.UpdateDiagnoseTemplate(ctx, template)
	if err != nil {
		s.logger.WithFunc("DeleteDiagnoseTemplate").CtxError(ctx, fmt.Sprintf(" UpdateDiagnoseTemplate err :%v", err))
		return nil, err
	}
	return &diagnoseTemplateHertz.DeleteDiagnoseTemplateResult{
		DiagnoseTemplateID: req.DiagnoseTemplateID,
	}, nil
}

func (s *DiagnoseTemplateService) ListDiagnoseTemplate(ctx context.Context, req *diagnoseTemplateHertz.ListDiagnoseTemplateRequest) (*diagnoseTemplateHertz.ListDiagnoseTemplateResult, error) {
	//todo:根据创建者的身份信息鉴权

	//鉴权通过后构造要插入的诊断项
	s.logger.WithFunc("ListDiagnoseTemplate")
	dbItems, count, err := dal.ListDiagnoseTemplate(ctx, int(*req.PageNumber), int(*req.PageSize), req)
	if err != nil {
		return nil, err
	}
	templates := make([]*diagnoseTemplateHertz.DiagnoseTemplate, 0)
	for _, dbItem := range dbItems {
		var updateTime, createTime string
		if dbItem.UpdateTime != nil {
			updateTime = dbItem.UpdateTime.String()
		}
		if dbItem.CreateTime != nil {
			createTime = dbItem.CreateTime.String()
		}
		// 查找对该template被哪些诊断任务所执行
		diagnoseTaskIDs, err := dal.QueryDiagTaskV2ByDiagnoseTemplateID(ctx, dbItem.DiagnoseTemplateID)
		if err != nil {
			return nil, err
		}
		// 将Inputs字段从JSON字符串转换为切片
		inputs := make([]*diagnoseTemplateHertz.Input, 0)
		if dbItem.Inputs != nil && *dbItem.Inputs != "" {
			err := json.Unmarshal([]byte(*dbItem.Inputs), &inputs)
			if err != nil {
				s.logger.WithFunc("ListDiagnoseTemplate").CtxError(ctx, fmt.Sprintf("Unmarshal err :%v", err))
				return nil, err
			}
		}
		// 更新人如果为空，则不返回
		updater := &diagnoseTemplateHertz.UserInfo{}
		if dbItem.UpdaterID != nil && dbItem.UpdaterName != nil {
			updater.UserID = *dbItem.UpdaterID
			updater.UserName = *dbItem.UpdaterName
		}

		//通过productCategoryID查询对应的productCategory
		productCategory, err := dal.QueryProductCategoryByID(ctx, int64(*dbItem.ProductCategoryID))
		if err != nil {
			s.logger.WithFunc("ListDiagnoseItem").CtxError(ctx, fmt.Sprintf("QueryProductCategoryByID err %v", err))
			return nil, err
		}

		temp := &diagnoseTemplateHertz.DiagnoseTemplate{
			DiagnoseTemplateID:   &dbItem.DiagnoseTemplateID,
			DiagnoseTemplateName: dbItem.DiagnoseTemplateName,
			Suggestion:           dbItem.Suggestion,
			UpdateTime:           &updateTime,
			Updater:              updater,
			CreateTime:           &createTime,
			Creator: &diagnoseTemplateHertz.UserInfo{
				UserID:   *dbItem.CreatorID,
				UserName: *dbItem.CreatorName},
			Status:              &dbItem.Status,
			ResponsiblePerson:   &dbItem.ResponsiblePerson,
			StateMachineInfo:    dbItem.StateMachineInfo,
			TemplateDescription: dbItem.TemplateDescription,
			Product:             dbItem.Product,
			SubProduct:          dbItem.Subproduct,
			ResourceType:        dbItem.ResourceType,
			ShowLevel:           dbItem.ShowLevel,
			DiagnoseTaskIDs:     diagnoseTaskIDs,
			Inputs:              inputs,
			ProductCategory: &product_category.ProductCategory{
				Product:        productCategory.Product,
				SubProduct:     productCategory.SubProduct,
				ResourceType:   productCategory.ResourceType,
				ProductCn:      productCategory.ProductCn,
				SubProductCn:   productCategory.SubProductCn,
				ResourceTypeCn: productCategory.ResourceTypeCn,
				Id:             productCategory.ID,
			},
		}
		relations, err := dal.FindRelationByTemplateID(ctx, dbItem.DiagnoseTemplateID)
		if err != nil {
			s.logger.WithFunc("ListDiagnoseTemplate").CtxError(ctx, fmt.Sprintf("FindRelationByItemID err %v", err))
			return nil, err
		}
		itemIds := make([]int64, 0)
		for _, relation := range relations {
			itemIds = append(itemIds, relation.DiagnoseItemID)
		}

		//
		temp.DiagnoseItemIDs = itemIds
		templates = append(templates, temp)
	}
	return &diagnoseTemplateHertz.ListDiagnoseTemplateResult{
		DiagnoseTemplates: templates,
		Pagination: &common.Pagination{
			PageNumber: int32(*req.PageNumber),
			PageSize:   int32(*req.PageSize),
			TotalCount: count,
		},
	}, nil
}

func (s *DiagnoseTemplateService) UpdateDiagnoseTemplate(ctx context.Context, req *diagnoseTemplateHertz.UpdateDiagnoseTemplateRequest) (*diagnoseTemplateHertz.UpdateDiagnoseTemplateResult, error) {
	//todo:根据创建者的身份信息鉴权

	//1.先通过ID判断该诊断项是否存在，或者已经删除
	//2.如果不存在或者已经删除，则返回错误
	//3.如果存在，则更新数据
	//4.最后更新关联关系

	res, err := dal.FindDiagnoseTemplateByID(ctx, *req.DiagnoseTemplateID)
	if err != nil {
		if err.Error() != "record not found" {
			s.logger.WithFunc("UpdateDiagnoseTemplate").Error("FindDiagnoseTemplateByID err:%v", err)
			return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseTemplate[%d]", *req.DiagnoseTemplateID))
		} else {
			s.logger.WithFunc("UpdateDiagnoseTemplate").Error("FindDiagnoseTemplateByID err:%v", err)
			return nil, err
		}
	}
	if res.DeleteAt != nil {
		s.logger.WithFunc("UpdateDiagnoseTemplate").Error("DiagnoseTemplate[%d] has been deleted", req.DiagnoseTemplateID)
		return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseTemplate[%d]", req.DiagnoseTemplateID))
	}

	//鉴权通过后构造要更新的诊断项
	s.logger.WithFunc("UpdateDiagnoseTemplate")
	template := &model.DiagnoseTemplate{
		DiagnoseTemplateID: *req.DiagnoseTemplateID,
		UpdaterID:          &req.UserInfo.UserID,
		UpdaterName:        &req.UserInfo.UserName,
	}
	if req.DiagnoseTemplateName != nil {
		template.DiagnoseTemplateName = req.DiagnoseTemplateName
	}
	if req.TemplateDescription != nil {
		template.TemplateDescription = req.TemplateDescription
	}
	if req.Suggestion != nil {
		template.Suggestion = req.Suggestion
	}
	if req.StateMachineInfo != nil {
		template.StateMachineInfo = req.StateMachineInfo
	}
	if req.Status != nil {
		template.Status = *req.Status
	}
	err = dal.UpdateDiagnoseTemplate(ctx, template)
	if err != nil {
		return nil, err
	}

	if len(req.DiagnosesItemIDs) > 0 {
		for _, itemID := range req.DiagnosesItemIDs {
			//1.先判断是否存在该itemID的记录
			item, errItem := dal.FindDiagnoseItemByID(ctx, itemID)
			if errItem != nil {
				s.logger.CtxError(ctx, "FindDiagnoseItemByID err:%v", errItem)
				if errItem.Error() == "record not found" {
					s.logger.CtxError(ctx, "item not found:%v", errItem)
					return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", itemID))
				}
				return nil, errItem
			}
			//2.如果存在该itemID的记录，还需要判断该Item是否已经被软删除
			if item.DeleteAt != nil {
				errDelete := dal.DeleteDiagnoseTemplate(ctx, template)
				if errDelete != nil {
					s.logger.CtxError(ctx, "DeleteDiagnoseTemplate err:%v", errDelete)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
				s.logger.CtxError(ctx, "item has been delete:%v", errDelete)
				return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
			}

			diagnoseTemplateItem := &model.ItemTemplate{
				DiagnoseTemplateID: template.DiagnoseTemplateID,
				DiagnoseItemID:     itemID,
				CreatorID:          &req.UserInfo.UserID,
				CreatorName:        &req.UserInfo.UserName,
			}
			//添加之前需要先判断该关联关系是否已经存在
			relation, errRelation := dal.FindRelationByItemIDAndTemplateID(ctx, itemID, template.DiagnoseTemplateID)
			if errRelation != nil {
				if errRelation.Error() == "record not found" {
					//记录不存在则添加记录
					err := dal.AddItemTemplate(ctx, diagnoseTemplateItem)
					if err != nil {
						s.logger.CtxError(ctx, "AddItemTemplate err:%v", err)
						return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
					}
					continue
				} else {
					s.logger.CtxError(ctx, "FindRelationByItemIDAndTemplateID err:%v", errRelation)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
			}
			//如果该记录被软删除了，则直接更新该记录
			if relation.DeleteAt != nil {
				err := dal.SaveItemTemplate(ctx, diagnoseTemplateItem)
				if err != nil {
					s.logger.CtxError(ctx, "UpdateItemTemplate err:%v", err)
					return nil, errorcode.ErrRegisterDiagnoseTemplateSuccessRelateItemFail.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", item.DiagnoseItemID))
				}
			}
		}
	}
	return &diagnoseTemplateHertz.UpdateDiagnoseTemplateResult{
		DiagnoseTemplateID: req.DiagnoseTemplateID,
	}, nil
}
