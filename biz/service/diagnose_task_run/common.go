package diagnose_task_run

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
)

const (
	DiagnoseResultLevelFailed   = "failed"
	DiagnoseResultLevelCritical = "critical"
	DiagnoseResultLevelError    = "error"
	DiagnoseResultLevelWarning  = "warning"
	DiagnoseResultLevelInfo     = "info"
	DiagnoseResultLevelUnknown  = "unknown"

	DiagnoseResultLevelFailedNum   = int32(15)
	DiagnoseResultLevelCriticalNum = int32(11)
	DiagnoseResultLevelErrorNum    = int32(7)
	DiagnoseResultLevelWarningNum  = int32(3)
	DiagnoseResultLevelInfoNum     = int32(1)
	DiagnoseResultLevelUnknownNum  = int32(0)

	DiagnoseTaskStatusWaiting    = "waiting"
	DiagnoseTaskStatusRunning    = "running"
	DiagnoseTaskStatusFinish     = "finish"
	DiagnoseTaskStatusFailed     = "failed"
	DiagnoseTaskStatusTerminated = "terminated"

	DiagnoseItemStatusWaiting    = "waiting"
	DiagnoseItemStatusRunning    = "running"
	DiagnoseItemStatusFinish     = "finish"
	DiagnoseItemStatusFailed     = "failed"
	DiagnoseItemStatusTerminated = "terminated"
)

func TransferToResultLevel(levelCounts []*dal.DiagnoseResultLevelCountGroupBy) diagnose_result.DiagnoseResultLevel {
	diagnoseResultLevel := diagnose_result.DiagnoseResultLevel{}
	for _, levelCount := range levelCounts {
		switch levelCount.DiagnoseResultLevel {
		case DiagnoseResultLevelCritical:
			diagnoseResultLevel.Critical = levelCount.Count
		case DiagnoseResultLevelError:
			diagnoseResultLevel.Error = levelCount.Count
		case DiagnoseResultLevelWarning:
			diagnoseResultLevel.Warning = levelCount.Count
		case DiagnoseResultLevelFailed:
			diagnoseResultLevel.Failed = levelCount.Count
		case DiagnoseResultLevelInfo:
			diagnoseResultLevel.Info = levelCount.Count
		default:
			diagnoseResultLevel.Unknown = levelCount.Count
		}
	}
	return diagnoseResultLevel
}

// TransferStringToDiagnoseResource converts a JSON string to a slice of *diagnose_task_run.DiagnoseResource
func TransferStringToDiagnoseResource(DiagnoseResource *string) ([]*diagnose_result.DiagnoseResource, error) {
	if DiagnoseResource == nil {
		return nil, nil
	}
	diagnoseResourceArray := make([]*diagnose_result.DiagnoseResource, 0)
	err := json.Unmarshal([]byte(*DiagnoseResource), &diagnoseResourceArray)
	if err != nil {
		return nil, err
	}
	return diagnoseResourceArray, nil
}

// TransferDiagnoseResourceToString 将 []*diagnose_task_run.DiagnoseResource 转换为 JSON 字符串
func TransferDiagnoseResourceToString(resources []*diagnose_result.DiagnoseResource) (string, error) {
	if resources == nil {
		return "", nil
	}
	jsonBytes, err := json.Marshal(resources)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

func TransferStringToDiagnoseItemIDs(result *string) ([]int64, error) {
	if result == nil {
		return nil, nil
	}
	diagnoseItemIDs := make([]int64, 0)
	err := json.Unmarshal([]byte(*result), &diagnoseItemIDs)
	if err != nil {
		return nil, err
	}
	return diagnoseItemIDs, nil
}

func TransferStringToArrayInt(result *string) ([]int64, error) {
	if result == nil {
		return nil, nil
	}
	intArray := make([]int64, 0)
	err := json.Unmarshal([]byte(*result), &intArray)
	if err != nil {
		return nil, err
	}
	return intArray, nil
}

//func TransferActivityDiagnoseToString(activityDiagnoses []*diagnoseTemplateHertz.ActivityDiagnose) (string, error) {
//	if activityDiagnoses == nil {
//		return "", nil
//	}
//	activityDiagnosesByte, err := json.Marshal(activityDiagnoses)
//	if err != nil {
//		return "", err
//	}
//	return string(activityDiagnosesByte), nil
//}
//func TransferStringToActivityDiagnose(result *string) ([]*diagnoseTemplateHertz.ActivityDiagnose, error) {
//	if result == nil {
//		return nil, nil
//	}
//	activityDiagnoses := make([]*diagnoseTemplateHertz.ActivityDiagnose, 0)
//	err := json.Unmarshal([]byte(*result), &activityDiagnoses)
//	if err != nil {
//		return nil, err
//	}
//	return activityDiagnoses, nil
//}

func TransferDiagnoseItemIDsToString(result []int64) (string, error) {
	diagnoseItemIDsByte, err := json.Marshal(result)
	if err != nil {
		return "", err
	}
	return string(diagnoseItemIDsByte), nil
}

// TransferDiagnoseItemStatusToString 将 map[int64]*diagnose_task_run.DiagnoseItemStatus 转换为 JSON 字符串
func TransferDiagnoseItemStatusToString(statuses map[int64]*diagnose_task_run.DiagnoseItemStatus) (string, error) {
	if statuses == nil {
		return "", nil
	}
	jsonBytes, err := json.Marshal(statuses)
	if err != nil {
		return "", err
	}
	return string(jsonBytes), nil
}

// TransferStringToDiagnoseItemStatus 将 JSON 字符串转换为 map[int64]*diagnose_task_run.DiagnoseItemStatus
func TransferStringToDiagnoseItemStatus(statusStr *string) (map[int64]*diagnose_task_run.DiagnoseItemStatus, error) {
	if statusStr == nil {
		return nil, nil
	}
	statusMap := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)
	err := json.Unmarshal([]byte(*statusStr), &statusMap)
	if err != nil {
		return nil, err
	}
	return statusMap, nil
}

func GetLevelByTaskRunIDItemID(ctx context.Context, taskRunID int64, itemID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByTaskRunIDItemID(ctx, taskRunID, itemID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunID(ctx context.Context, taskRunID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByDiagnoseTaskRunID(ctx, taskRunID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunIDProductCategoryID(ctx context.Context, taskRunID, productCategoryID int64) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByDiagnoseTaskRunIDProductCategoryID(ctx, taskRunID, productCategoryID)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

func GetLevelByTaskRunIDItemIDInstanceIDsLevels(ctx context.Context, taskRunID int64, itemID int64, instanceIDs, levels []string) (*diagnose_result.DiagnoseResultLevel, error) {
	itemGroupByLevel, err := dal.DiagnoseResultsGroupByLevelByTaskRunIDItemIDInstanceIDsLevels(ctx, taskRunID, itemID, instanceIDs, levels)
	if err != nil {
		return nil, err
	}
	itemLevel := TransferToResultLevel(itemGroupByLevel)
	return &itemLevel, nil
}

// FilterDiagnoseResourceByDiagnoseItems 通过诊断项过滤资源
func FilterDiagnoseResourceByDiagnoseItems(resource []*diagnose_result.DiagnoseResource, diagnoseItems []*diagnose_task_v2.DiagnoseItem) []*diagnose_result.DiagnoseResource {
	diagnoseResource := make([]*diagnose_result.DiagnoseResource, 0)
	resourceMap := make(map[int64]struct{})
	for _, item := range diagnoseItems {
		resourceMap[item.ProductCategory.Id] = struct{}{}
	}
	for _, item := range resource {
		if _, ok := resourceMap[item.ProductCategoryID]; ok && len(item.Instances) > 0 {
			diagnoseResource = append(diagnoseResource, item)
		}
	}
	return diagnoseResource
}

func GetDiagnoseResultLevelNum(level string) int32 {
	switch level {
	case DiagnoseResultLevelFailed:
		return DiagnoseResultLevelFailedNum
	case DiagnoseResultLevelCritical:
		return DiagnoseResultLevelCriticalNum
	case DiagnoseResultLevelError:
		return DiagnoseResultLevelErrorNum
	case DiagnoseResultLevelWarning:
		return DiagnoseResultLevelWarningNum
	case DiagnoseResultLevelInfo:
		return DiagnoseResultLevelInfoNum
	default:
		return DiagnoseResultLevelUnknownNum
	}
}

func TransferModelProductCategoryToHertzProductCategory(productCategory *model.ProductCategory) *product_category.ProductCategory {
	return &product_category.ProductCategory{
		Id:             productCategory.ID,
		Product:        productCategory.Product,
		SubProduct:     productCategory.SubProduct,
		ResourceType:   productCategory.ResourceType,
		ProductCn:      productCategory.ProductCn,
		SubProductCn:   productCategory.SubProductCn,
		ResourceTypeCn: productCategory.ResourceTypeCn,
	}
}

func GetProductCategoryByID(ctx context.Context, productCategoryID int64) (*product_category.ProductCategory, error) {
	productCategory, err := dal.QueryProductCategoryByID(ctx, productCategoryID)
	if err != nil {
		return nil, err
	}
	return TransferModelProductCategoryToHertzProductCategory(productCategory), nil
}

// MonitorTimeIsValid 验证监控时间是否符合限制
func MonitorTimeIsValid(ctx context.Context, start, end, productCategoryID int64) error {
	// 将 int64 类型的时间戳转换为 time.Time 类型
	startTime := time.Unix(start, 0).UTC()
	endTime := time.Unix(end, 0).UTC()
	// 获取产品分类的监控时间限制
	p, err := dal.QueryProductCategoryByID(ctx, productCategoryID)
	if err != nil {
		return err
	}
	maxHoursBeforeNow := p.MaxStartTimeBeforeNowHour
	maxDurationHours := p.MaxDurationHour
	productName := fmt.Sprintf("%s-%s-%s", p.ProductCn, p.SubProductCn, p.ResourceTypeCn)

	now := time.Now().UTC()
	// 检查开始时间是否超过当前时间指定小时数之前
	if maxHoursBeforeNow != nil {
		maxBeforeTime := now.Add(-time.Duration(*maxHoursBeforeNow) * time.Hour)
		if startTime.Before(maxBeforeTime) {
			return errorcode.ErrMonitorTimeBeforeNow.WithArgs(productName, *maxHoursBeforeNow)
		}
	}

	// 检查结束时间是否早于开始时间
	if endTime.Before(startTime) {
		return errorcode.ErrMonitorEndTimeBeforeStartTime
	}
	// 检查时间跨度是否超过最大允许小时数
	if maxDurationHours != nil {
		actualDuration := endTime.Sub(startTime).Hours()
		if actualDuration > float64(*maxDurationHours) {
			return errorcode.ErrMonitorTimeDuration.WithArgs(productName, *maxDurationHours)
		}
	}
	return nil
}
