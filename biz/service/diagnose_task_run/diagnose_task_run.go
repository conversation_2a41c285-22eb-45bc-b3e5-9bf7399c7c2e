package diagnose_task_run

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose"
	diagnosetask "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseTaskRunService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("GetDiagnoseTaskRunService"),
		}
	})
	return service
}

func (s *Service) QueryDiagnoseTaskRunList(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunListReq) (*diagnose_task_run.QueryDiagnoseTaskRunListResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunList")
	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	mTaskRun := &model.DiagnoseTaskRun{
		Status:   req.Status,
		TicketID: req.TicketID,
		Name:     req.DiagnoseTaskRunName,
	}
	if req.Origin != nil {
		mTaskRun.Origin = *req.Origin
	}
	if req.DiagnoseTaskID != nil {
		mTaskRun.DiagnoseTaskID = *req.DiagnoseTaskID
	}
	if req.StartTime != nil {
		startTime, err := utils.StringToTime(*req.StartTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, err
		}
		mTaskRun.StartTime = &startTime
	}
	if req.EndTime != nil {
		endTime, err := utils.StringToTime(*req.EndTime, utils.DateTime, "")
		if err != nil {
			logger.CtxError(ctx, "StringToTime err:%v", err)
			return nil, err
		}
		mTaskRun.EndTime = &endTime
	}
	asc := false
	if req.AscSortByEndTime != nil {
		asc = *req.AscSortByEndTime
	}
	// 调用数据访问层方法时传入模糊查询的名称
	diagnoseTaskRuns, total, err := dal.QueryDiagnoseTaskRunList(ctx, mTaskRun, req.DiagnoseTemplateIDs,
		req.DiagnoseTaskRunIDs, req.ProductCategoryIDs, req.DiagnoseResultLevels, asc, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunList err:%v", err)
		return nil, err
	}
	diagnoseTaskRunList := make([]*diagnose_task_run.DiagnoseTaskRun, 0)
	for _, taskRun := range diagnoseTaskRuns {
		diagnoseTaskRun := &diagnose_task_run.DiagnoseTaskRun{
			Id:                   taskRun.ID,
			DiagnoseTaskID:       taskRun.DiagnoseTaskID,
			Name:                 taskRun.Name,
			Status:               taskRun.Status,
			RunUserID:            taskRun.CreateUserID,
			DiagnoseType:         taskRun.DiagnoseType,
			DiagnoseStartTime:    taskRun.DiagnoseStartTime,
			DiagnoseEndTime:      taskRun.DiagnoseEndTime,
			DiagnoseTemplateName: taskRun.DiagnoseTemplateName,
			Origin:               taskRun.Origin,
			TicketID:             taskRun.TicketID,
		}
		//查询产品类型（通过资源查询）
		productCategories := make([]*product_category.ProductCategory, 0)
		resources, err := TransferStringToDiagnoseResource(taskRun.Resources)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
			return nil, err
		}
		for _, resource := range resources {
			productCategory, err := GetProductCategoryByID(ctx, resource.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			productCategories = append(productCategories, productCategory)
		}
		diagnoseTaskRun.ProductCategory = productCategories
		if taskRun.StartTime != nil {
			startTime, err := utils.TimeToString(*taskRun.StartTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			diagnoseTaskRun.StartTime = lo.ToPtr(startTime)
		}
		if taskRun.EndTime != nil {
			endTime, err := utils.TimeToString(*taskRun.EndTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			diagnoseTaskRun.EndTime = lo.ToPtr(endTime)
		}
		if taskRun.FeedbackID != nil {
			diagnoseTaskRun.Feedback = lo.ToPtr(true)
		} else {
			diagnoseTaskRun.Feedback = lo.ToPtr(false)
		}
		//计算level
		level, err := GetLevelByTaskRunID(ctx, taskRun.ID)
		if err != nil {
			logger.CtxError(ctx, "GetDiagnoseTaskRunLevel err:%v", err)
			return nil, err
		}
		diagnoseTaskRun.DiagnoseResultLevel = level
		diagnoseTaskRunList = append(diagnoseTaskRunList, diagnoseTaskRun)
	}

	return &diagnose_task_run.QueryDiagnoseTaskRunListResult{
		DiagnoseTaskRunList: diagnoseTaskRunList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

// DeleteDiagnoseTaskRun 根据诊断任务运行 ID 删除对应的诊断任务运行记录
func (s *Service) DeleteDiagnoseTaskRun(ctx context.Context, req *diagnose_task_run.DeleteDiagnoseTaskRunReq) error {
	logger := s.logger.WithFunc("DeleteDiagnoseTaskRun")

	// 先查询任务的状态
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果记录没找到，直接返回
			logger.CtxInfo(ctx, "Diagnose task run record not found for ID: %d", req.Id)
			return nil
		}
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return err
	}

	// 检查任务状态是否为 finished
	if taskRun.Status == nil || *taskRun.Status != DiagnoseTaskStatusFinish {
		logger.CtxError(ctx, "Cannot delete task run with status %v, only finished tasks can be deleted", taskRun.Status)
		return fmt.Errorf("cannot delete task run with status %v, only finished tasks can be deleted", taskRun.Status)
	}

	// 调用数据访问层的删除方法
	err = dal.DeleteDiagnoseTaskRunByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DeleteDiagnoseTaskRunByID err:%v", err)
		return err
	}

	return nil
}

// RunDiagnoseTask 运行诊断任务
func (s *Service) RunDiagnoseTask(ctx context.Context,
	req *diagnose_task_run.RunDiagnoseTaskReq) (*diagnose_task_run.RunDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("RunDiagnoseTask")

	// 检查诊断任务是否存在
	diagnoseTask, err := dal.DiagnoseTaskV2FindByID(ctx, req.DiagnoseTaskID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxError(ctx, "Diagnose task not found for ID: %d", req.DiagnoseTaskID)
			return nil, err
		}
		logger.CtxError(ctx, "DiagnoseTaskV2FindByID err: %v", err)
		return nil, err
	}
	//判断监控时间是否符合限制
	productCategoryIDs, err := TransferStringToArrayInt(diagnoseTask.ProductCategoryIds)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToArrayInt err:%v", err)
		return nil, err
	}
	for _, productCategoryID := range productCategoryIDs {
		err = MonitorTimeIsValid(ctx, req.DiagnoseStartTime, req.DiagnoseEndTime, productCategoryID)
		if err != nil {
			logger.CtxError(ctx, "MonitorTimeIsValid err:%v", err)
			return nil, err
		}
	}

	// 创建诊断任务运行记录
	startTime := time.Now()
	mTaskRun := &model.DiagnoseTaskRun{
		Name:               req.Name,
		DiagnoseTaskID:     req.DiagnoseTaskID,
		AccountID:          req.AccountID,
		CreateUserID:       lo.ToPtr(req.RunUserID),
		StartTime:          &startTime,
		Status:             lo.ToPtr(DiagnoseTaskStatusWaiting),
		DiagnoseStartTime:  lo.ToPtr(req.DiagnoseStartTime),
		DiagnoseEndTime:    lo.ToPtr(req.DiagnoseEndTime),
		TicketID:           diagnoseTask.TicketID,
		DiagnoseType:       diagnoseTask.DiagnoseType,
		DiagnoseTemplateID: diagnoseTask.DiagnoseTemplateID,
	}
	if diagnoseTask.Origin != nil {
		mTaskRun.Origin = *diagnoseTask.Origin
	} else {
		mTaskRun.Origin = diagnosetask.DiagnoseTaskOriginPlatform
	}
	if mTaskRun.Name == nil {
		mTaskRun.Name = diagnoseTask.Name
	}
	diagnoseResources, err := TransferDiagnoseResourceToString(req.DiagnoseResources)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
		return nil, err
	}
	mTaskRun.Resources = lo.ToPtr(diagnoseResources)
	//保存诊断项运行流程快照

	// 不同的诊断类型，获取不同的诊断项列表
	diagnoseItemIDs := make([]int64, 0)
	relations := make([]*model.ItemTemplate, 0)
	if *diagnoseTask.DiagnoseType == diagnosetask.DiagnoseTypeItem {
		ItemIDs, err := TransferStringToDiagnoseItemIDs(diagnoseTask.DiagnoseItemIds)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToDiagnoseItemIDs err:%v", err)
			return nil, err
		}
		diagnoseItemIDs = ItemIDs
	} else if *diagnoseTask.DiagnoseType == diagnosetask.DiagnoseTypeTemplate {
		rels, err := dal.FindRelationByTemplateID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			logger.CtxError(ctx, "FindRelationByTemplateID err:%v", err)
			return nil, err
		}
		relations = rels
		for _, relation := range relations {
			diagnoseItemIDs = append(diagnoseItemIDs, relation.DiagnoseItemID)
		}
		//查询StateMachineInfo
		tem, err := dal.DiagnoseTemplateFindByID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			logger.CtxError(ctx, "FindRelationByTemplateID err:%v", err)
			return nil, err
		}
		mTaskRun.StateMachineInfo = tem.StateMachineInfo
		mTaskRun.DiagnoseTemplateName = tem.DiagnoseTemplateName
		//查询activityDiagnose
		//activityDiagnoses, err := dal.FindActivityDiagnoseItemsByTemplateID(ctx, *diagnoseTask.DiagnoseTemplateID)
		//if err != nil {
		//	logger.CtxError(ctx, "FindActivityDiagnoseItemsByTemplateID err:%v", err)
		//	return nil, err
		//}
		//activityDiagnosesStr, err := TransferActivityDiagnoseToString(activityDiagnoses)
		//if err != nil {
		//	logger.CtxError(ctx, "TransferActivityDiagnoseToString err:%v", err)
		//	return nil, err
		//}
		//mTaskRun.ActivityDiagnoses = lo.ToPtr(activityDiagnosesStr)
	}

	//判断诊断项ID是否存在
	diagnoseItemMap := make(map[int64]*model.DiagnoseItem)
	for _, diagnoseItemID := range diagnoseItemIDs {
		diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, diagnoseItemID)
		if err != nil {
			logger.CtxError(ctx, "FindDiagnoseItemByID err:%v", err)
			return nil, err
		}
		diagnoseItemMap[diagnoseItemID] = diagnoseItem
	}
	//添加DiagnoseItemStatus
	// 定义 diagnoseItemStatus 为 map 类型
	diagnoseItemStatus := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)

	for diagnoseItemID, diagnoseItem := range diagnoseItemMap {
		runDiagnoseItem := &diagnose_task_v2.DiagnoseItem{
			Id:   diagnoseItem.DiagnoseItemID,
			Name: diagnoseItem.DiagnoseItemName,
		}
		//获取诊断项的产品、子产品、资源类型
		runDiagnoseItem.ProductCategory, err = GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
		if err != nil {
			logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
			return nil, err
		}
		//根据诊断项过滤资源
		itemResource := FilterDiagnoseResourceByDiagnoseItems(req.DiagnoseResources, []*diagnose_task_v2.DiagnoseItem{runDiagnoseItem})
		//if len(itemResource) == 0 {
		//	continue
		//}
		diagnoseItemStatus[diagnoseItemID] = &diagnose_task_run.DiagnoseItemStatus{
			DiagnoseItem:      runDiagnoseItem,
			Status:            DiagnoseItemStatusWaiting,
			DiagnoseResources: itemResource,
		}
	}
	diagnoseItemStatusStr, err := TransferDiagnoseItemStatusToString(diagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferDiagnoseItemStatusToString err:%v", err)
		return nil, err
	}
	mTaskRun.DiagnoseItemStatus = lo.ToPtr(diagnoseItemStatusStr)
	//todo：运行诊断任务,待确认诊断编排流程
	// 保存诊断任务运行记录
	err = dal.CreateDiagnoseTaskRun(ctx, mTaskRun)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTaskRun err: %v", err)
		return nil, err
	}

	switch *diagnoseTask.DiagnoseType {
	case diagnosetask.DiagnoseTypeItem: //原子诊断
		//遍历diagnoseItemStatus，获取诊断项ID，和每个诊断项对应的资源列表和每个诊断项的asl语言，以诊断项的维度发起诊断
		for itemId, itemStatus := range diagnoseItemStatus {
			resources := make(map[int64][]*diagnose_result.DiagnoseResource)
			resources[itemId] = itemStatus.DiagnoseResources
			diagnoseInput := &statemachines.DiagnoseRequest{
				AccountID:         req.AccountID,
				Resources:         resources,
				DiagnoseItemIDs:   []int64{itemId},
				DiagnoseTaskRunID: mTaskRun.ID,
				DiagnoseStartTime: req.DiagnoseStartTime,
				DiagnoseEndTime:   req.DiagnoseEndTime,
			}
			if diagnoseTask.TicketID != nil {
				diagnoseInput.TicketID = *diagnoseTask.TicketID
			}
			detail, err := diagnose.TriggerDiagnoseTask(ctx, diagnoseItemMap[itemId].StateMachineInfo, diagnoseInput, nil)
			if err != nil {
				logger.CtxError(ctx, "TriggerDiagnoseTask err:%v", err)
				return nil, err
			}
			logger.CtxInfo(ctx, "[CreateDiagnoseTask] diagnose.TriggerDiagnoseTask success, response:%v", detail)
		}
	case diagnosetask.DiagnoseTypeTemplate: //编排诊断
		//获取诊断模版信息
		templateInfo, err := dal.DiagnoseTemplateFindByID(ctx, *diagnoseTask.DiagnoseTemplateID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.CtxError(ctx, "Diagnose template not found for ID: %d", *diagnoseTask.DiagnoseTemplateID)
				return nil, err
			}
			logger.CtxError(ctx, "DiagnoseTemplateFindByID err:%v", err)
			return nil, err
		}
		if templateInfo.StateMachineInfo == nil {
			logger.CtxError(ctx, "StateMachineInfo is nil")
			return nil, fmt.Errorf("StateMachineInfo is nil")
		}
		resources := make(map[int64][]*diagnose_result.DiagnoseResource)
		for itemId, itemStatus := range diagnoseItemStatus {
			resources[itemId] = itemStatus.DiagnoseResources
		}
		diagnoseItemIDs := make([]int64, 0)
		for _, relation := range relations {
			//if _, ok := diagnoseItemStatus[relation.DiagnoseItemID]; ok {
			diagnoseItemIDs = append(diagnoseItemIDs, relation.DiagnoseItemID)
			//}
		}

		diagnoseInput := &statemachines.DiagnoseRequest{
			AccountID:         req.AccountID,
			Resources:         resources,
			DiagnoseItemIDs:   diagnoseItemIDs,
			DiagnoseTaskRunID: mTaskRun.ID,
			DiagnoseStartTime: req.DiagnoseStartTime,
			DiagnoseEndTime:   req.DiagnoseEndTime,
		}
		if diagnoseTask.TicketID != nil {
			diagnoseInput.TicketID = *diagnoseTask.TicketID
		}
		detail, err := diagnose.TriggerDiagnoseTask(ctx, *templateInfo.StateMachineInfo, diagnoseInput, nil)
		if err != nil {
			logger.CtxError(ctx, "TriggerDiagnoseTask err:%v", err)
			return nil, err
		}
		logger.CtxInfo(ctx, "[CreateDiagnoseTask] diagnose.TriggerDiagnoseTask success, response:%v", detail)
	}
	//更新诊断任务lastRunTime
	err = dal.UpdateDiagnoseTaskV2LastRunTimeByID(ctx, diagnoseTask.ID, lo.ToPtr(startTime))
	if err != nil {
		logger.CtxError(ctx, "UpdateDiagnoseTaskV2LastRunTimeByID err:%v", err)
		return nil, err
	}
	return &diagnose_task_run.RunDiagnoseTaskResult{
		DiagnoseTaskRunID: mTaskRun.ID,
	}, nil
}

// QueryDiagnoseTaskRunDetail 根据诊断任务运行 ID 查询执行结果详情
func (s *Service) QueryDiagnoseTaskRunDetail(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunDetailReq) (*diagnose_task_run.QueryDiagnoseTaskRunDetailResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunDetail")

	// 根据诊断任务运行 ID 查询任务运行记录
	taskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.CtxInfo(ctx, "Diagnose task run record not found for ID: %d", req.DiagnoseTaskRunID)
			return nil, err
		}
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}

	// 构建返回的诊断任务运行详情对象
	diagnoseTaskRunDetail := &diagnose_task_run.DiagnoseTaskRun{
		Id:                taskRun.ID,
		DiagnoseTaskID:    taskRun.DiagnoseTaskID,
		Name:              taskRun.Name,
		Status:            taskRun.Status,
		RunUserID:         taskRun.CreateUserID,
		DiagnoseType:      taskRun.DiagnoseType,
		TicketID:          taskRun.TicketID,
		DiagnoseStartTime: taskRun.DiagnoseStartTime,
		DiagnoseEndTime:   taskRun.DiagnoseEndTime,
		Origin:            taskRun.Origin,
	}

	// 处理开始时间
	if taskRun.StartTime != nil {
		startTime, err := utils.TimeToString(*taskRun.StartTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.StartTime = lo.ToPtr(startTime)
	}

	// 处理结束时间
	if taskRun.EndTime != nil {
		endTime, err := utils.TimeToString(*taskRun.EndTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		diagnoseTaskRunDetail.EndTime = lo.ToPtr(endTime)
	}

	// 处理反馈信息
	if taskRun.FeedbackID != nil {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(true)
	} else {
		diagnoseTaskRunDetail.Feedback = lo.ToPtr(false)
	}

	// 计算诊断结果等级
	level, err := GetLevelByTaskRunID(ctx, taskRun.ID)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunID err:%v", err)
		return nil, err
	}
	diagnoseTaskRunDetail.DiagnoseResultLevel = level

	// 诊断项运行状态查询
	diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(taskRun.DiagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	//计算诊断项结果等级
	for i, status := range diagnoseItemStatusList {
		itemLevel, err := GetLevelByTaskRunIDItemID(ctx, taskRun.ID, status.DiagnoseItem.Id)
		if err != nil {
			logger.CtxError(ctx, "GetLevelByTaskRunIDItemID err:%v", err)
			return nil, err
		}
		diagnoseItemStatusList[i].DiagnoseResultLevel = itemLevel
	}
	////查询activityDiagnose
	//_, err = TransferStringToActivityDiagnose(taskRun.ActivityDiagnoses)
	//if err != nil {
	//	logger.CtxError(ctx, "TransferStringToActivityDiagnose err:%v", err)
	//	return nil, err
	//}
	return &diagnose_task_run.QueryDiagnoseTaskRunDetailResult{
		Information: diagnoseTaskRunDetail,
		DiagnoseTaskRunByteFlow: &diagnose_task_run.DiagnoseTaskRunByteFlow{
			StateMachineInfo:       taskRun.StateMachineInfo,
			DiagnoseItemStatusList: diagnoseItemStatusList,
		},
	}, nil
}

// QueryDiagnoseTaskRunItemDetail 查询执行结果某个诊断项详情
func (s *Service) QueryDiagnoseTaskRunItemDetail(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq) (*diagnose_task_run.DiagnoseTaskRunItemDetailResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunItemDetail")

	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	// 计算诊断项结果等级
	itemGroupByLevel, err := GetLevelByTaskRunIDItemIDInstanceIDsLevels(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID, req.InstanceID, req.DiagnoseResultLevel)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunIDItemIDInstanceIDsLevels err:%v", err)
		return nil, err
	}
	diagnoseResults, total, err := dal.DiagnoseResultsFindByConSortByLevel(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID,
		req.InstanceID, req.DiagnoseResultLevel, int(*req.PageNumber), int(*req.PageSize))
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsFindByConSortByLevel err:%v", err)
		return nil, err
	}
	diagnoseResultList := make([]*diagnose_result.DiagnoseResult, 0)
	for _, diagnoseResult := range diagnoseResults {
		diagnoseResultList = append(diagnoseResultList, &diagnose_result.DiagnoseResult{
			InstanceID:          diagnoseResult.InstanceID,
			InstanceName:        diagnoseResult.InstanceName,
			Region:              diagnoseResult.Region,
			Message:             diagnoseResult.DiagnoseMessage,
			DiagnoseResultLevel: diagnoseResult.DiagnoseResultLevel,
			Suggestion:          diagnoseResult.DiagnoseSuggestion,
			Operate:             diagnoseResult.DiagnoseOperate,
		})
	}
	//查询diagnoseItem
	diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, req.DiagnoseItemID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseItemFindByID err:%v", err)
		return nil, err
	}
	//查询productCategory
	productCategory, err := GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
	if err != nil {
		logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
		return nil, err
	}
	return &diagnose_task_run.DiagnoseTaskRunItemDetailResult{
		DiagnoseItem: &diagnose_task_v2.DiagnoseItem{
			Id:              diagnoseItem.DiagnoseItemID,
			Name:            diagnoseItem.DiagnoseItemName,
			ProductCategory: productCategory,
		},
		DiagnoseResultLevel: itemGroupByLevel,
		DiagnoseResults:     diagnoseResultList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

//func (s *Service) QueryDiagnoseTaskRunItemDetailV2(ctx context.Context,
//	req *diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq) (*diagnose_task_run.DiagnoseTaskRunItemDetailResult, error) {
//	logger := s.logger.WithFunc("QueryDiagnoseTaskRunItemDetail")
//
//	if req.PageNumber == nil {
//		req.PageNumber = lo.ToPtr(defaultPageNum)
//	}
//	if req.PageSize == nil {
//		req.PageSize = lo.ToPtr(defaultPageSize)
//	}
//	// 计算诊断项结果等级
//	itemGroupByLevel, err := GetLevelByTaskRunIDItemIDInstanceIDsLevels(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID, req.InstanceID, req.DiagnoseResultLevel)
//	if err != nil {
//		logger.CtxError(ctx, "GetLevelByTaskRunIDItemIDInstanceIDsLevels err:%v", err)
//		return nil, err
//	}
//	diagnoseResults, total, err := dal.DiagnoseResultsFindByConSortByLevel(ctx, req.DiagnoseTaskRunID, req.DiagnoseItemID,
//		req.InstanceID, req.DiagnoseResultLevel, int(*req.PageNumber), int(*req.PageSize))
//	if err != nil {
//		logger.CtxError(ctx, "DiagnoseResultsFindByConSortByLevel err:%v", err)
//		return nil, err
//	}
//	sort.Slice(diagnoseResults, func(i, j int) bool {
//		return diagnoseResults[i].InstanceID < diagnoseResults[j].InstanceID
//	})
//	diagnoseResultList := make([]*diagnose_result.DiagnoseResult, 0)
//	oldInstanceID := ""
//	instanceIdInfoMap := make(map[string][]*diagnose_result.DiagnoseItemInfo)
//	for _, diagnoseResult := range diagnoseResults {
//		if diagnoseResult.InstanceID != oldInstanceID {
//			diagnoseResultList = append(diagnoseResultList, &diagnose_result.DiagnoseResult{
//				InstanceID:        diagnoseResult.InstanceID,
//				InstanceName:      diagnoseResult.InstanceName,
//				Region:            diagnoseResult.Region,
//				DiagnoseItemInfos: make([]*diagnose_result.DiagnoseItemInfo, 0),
//			})
//			oldInstanceID = diagnoseResult.InstanceID
//		}
//		instanceIdInfoMap[oldInstanceID] = append(instanceIdInfoMap[oldInstanceID],
//			&diagnose_result.DiagnoseItemInfo{
//				Message:             diagnoseResult.DiagnoseMessage,
//				DiagnoseResultLevel: diagnoseResult.DiagnoseResultLevel,
//				Suggestion:          diagnoseResult.DiagnoseSuggestion,
//				Operate:             diagnoseResult.DiagnoseOperate,
//			})
//	}
//	for i, diagnoseResult := range diagnoseResultList {
//		diagnoseResultList[i].DiagnoseItemInfos = instanceIdInfoMap[diagnoseResult.InstanceID]
//	}
//	return &diagnose_task_run.DiagnoseTaskRunItemDetailResult{
//		DiagnoseResultLevel: itemGroupByLevel,
//		DiagnoseResult:      diagnoseResultList,
//		Pagination: &common.Pagination{
//			PageNumber: *req.PageNumber,
//			PageSize:   *req.PageSize,
//			TotalCount: total,
//		},
//	}, nil
//}

func (s *Service) QueryDiagnoseTaskRunItems(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunItemsReq) (*diagnose_task_run.QueryDiagnoseTaskRunItemsResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunItems")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}
	diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(diagnoseTaskRun.DiagnoseItemStatus)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
		return nil, err
	}
	items := make([]*diagnose_task_v2.DiagnoseItem, 0)
	for _, item := range diagnoseItemStatusList {
		if len(req.ProductCategoryIDs) > 0 && !lo.Contains(req.ProductCategoryIDs, item.DiagnoseItem.ProductCategory.Id) {
			continue
		}
		items = append(items, item.DiagnoseItem)
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunItemsResult{
		DiagnoseItems: items,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunResource(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunResourceReq) (*diagnose_task_run.QueryDiagnoseTaskRunResourceResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunResource")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}
	resources, err := TransferStringToDiagnoseResource(diagnoseTaskRun.Resources)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToDiagnoseResource err:%v", err)
		return nil, err
	}
	//过滤product和subProduct
	filteredResources := make([]*diagnose_result.DiagnoseResource, 0)
	for _, resource := range resources {
		if len(req.ProductCategoryIDs) > 0 && !lo.Contains(req.ProductCategoryIDs, resource.ProductCategoryID) {
			continue
		}
		filteredResources = append(filteredResources, resource)
	}
	// 过滤诊断项对应的资源
	if len(req.DiagnoseItemIDs) > 0 {
		diagnoseItems, err := dal.DiagnoseItemFindByIDs(ctx, req.DiagnoseItemIDs)
		if err != nil {
			logger.CtxError(ctx, "DiagnoseItemFindByIDs err:%v", err)
			return nil, err
		}
		items := make([]*diagnose_task_v2.DiagnoseItem, 0)
		for _, item := range diagnoseItems {
			//查询productCategory
			productCategory, err := GetProductCategoryByID(ctx, item.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			items = append(items, &diagnose_task_v2.DiagnoseItem{
				Id:              item.DiagnoseItemID,
				Name:            item.DiagnoseItemName,
				ProductCategory: productCategory,
			})
		}
		filteredResources = FilterDiagnoseResourceByDiagnoseItems(filteredResources, items)
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunResourceResult{
		DiagnoseResources: filteredResources,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunCategory(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunCategoryReq) (*diagnose_task_run.QueryDiagnoseTaskRunCategoryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunCategory")
	diagnoseResults, err := dal.DiagnoseResultsFindByDiagnoseTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsFindByDiagnoseTaskRunID err:%v", err)
		return nil, err
	}
	diagnoseCategoryList := make([]*diagnose_result.DiagnoseTaskCategory, 0)
	productCategoryAbnormalMap := make(map[int64]int32)
	idProductCategoryMap := make(map[int64]*product_category.ProductCategory)
	itemProductCategoryMap := make(map[int64]*product_category.ProductCategory)
	for _, result := range diagnoseResults {
		prc := &product_category.ProductCategory{}
		if _, ok := itemProductCategoryMap[result.DiagnoseItemID]; ok {
			prc = itemProductCategoryMap[result.DiagnoseItemID]
		} else {
			diagnoseItem, err := dal.DiagnoseItemFindByID(ctx, result.DiagnoseItemID)
			if err != nil {
				logger.CtxError(ctx, "FindDiagnoseItemByID err:%v", err)
				return nil, err
			}
			prc, err = GetProductCategoryByID(ctx, diagnoseItem.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
				return nil, err
			}
			itemProductCategoryMap[result.DiagnoseItemID] = prc
		}
		idProductCategoryMap[prc.Id] = prc
		if _, ok := productCategoryAbnormalMap[prc.Id]; !ok {
			productCategoryAbnormalMap[prc.Id] = 0
		}
		if result.DiagnoseResultLevel != DiagnoseResultLevelInfo {
			productCategoryAbnormalMap[prc.Id]++
		}
	}
	for id, abnormalNum := range productCategoryAbnormalMap {
		diagnoseCategoryList = append(diagnoseCategoryList, &diagnose_result.DiagnoseTaskCategory{
			ProductCategory: idProductCategoryMap[id],
			AbnormalNum:     abnormalNum,
		})
	}
	return &diagnose_task_run.QueryDiagnoseTaskRunCategoryResult{
		DiagnoseTaskRunCategoryList: diagnoseCategoryList,
	}, nil
}

func (s *Service) QueryDiagnoseTaskRunSummary(ctx context.Context,
	req *diagnose_task_run.QueryDiagnoseTaskRunSummaryReq) (*diagnose_task_run.QueryDiagnoseTaskRunSummaryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskRunSummary")
	//查询诊断任务运行详情
	diagnoseTaskRun, err := dal.QueryDiagnoseTaskRunByID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return nil, err
	}

	result := &diagnose_task_run.QueryDiagnoseTaskRunSummaryResult{
		DiagnoseResultLevel:  nil,
		DiagnoseTemplateName: diagnoseTaskRun.DiagnoseTemplateName,
		CreateUserID:         diagnoseTaskRun.CreateUserID,
		CreateUserName:       diagnoseTaskRun.CreateUserName,
		Progress:             100,
		Feedback:             false,
	}
	if diagnoseTaskRun.StartTime != nil {
		startTime, err := utils.TimeToString(*diagnoseTaskRun.StartTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		result.StartTime = lo.ToPtr(startTime)
	}
	if diagnoseTaskRun.EndTime != nil {
		endTime, err := utils.TimeToString(*diagnoseTaskRun.EndTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		result.EndTime = lo.ToPtr(endTime)
	}
	if diagnoseTaskRun.DiagnoseStartTime != nil {
		result.DiagnoseStartTime = *diagnoseTaskRun.DiagnoseStartTime
	}
	if diagnoseTaskRun.DiagnoseEndTime != nil {
		result.DiagnoseEndTime = *diagnoseTaskRun.DiagnoseEndTime
	}
	if diagnoseTaskRun.FeedbackID != nil {
		result.Feedback = true
	}

	//计算进度
	if diagnoseTaskRun.Status != nil {
		switch *diagnoseTaskRun.Status {
		case DiagnoseTaskStatusFinish:
			result.Progress = 100
		case DiagnoseTaskStatusWaiting:
			result.Progress = 0
		default:
			diagnoseItemStatusList, err := TransferStringToDiagnoseItemStatus(diagnoseTaskRun.DiagnoseItemStatus)
			if err != nil {
				logger.CtxError(ctx, "TransferStringToDiagnoseItemStatus err:%v", err)
				return nil, err
			}
			total := 0
			finished := 0
			for _, diagnoseItemStatus := range diagnoseItemStatusList {
				if diagnoseItemStatus.Status != DiagnoseItemStatusWaiting && diagnoseItemStatus.Status != DiagnoseItemStatusRunning {
					finished++
				}
				total++
			}
			result.Progress = int32(float64(finished) / float64(total) * 100)
		}
	}

	//计算level
	groupLevel, err := GetLevelByTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "GetLevelByTaskRunID err:%v", err)
		return nil, err
	}
	result.DiagnoseResultLevel = groupLevel

	//计算productCategoryLevel
	//对productCategory进行分组
	productCategoryIdList, err := dal.DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID(ctx, req.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID err:%v", err)
		return nil, err
	}
	for _, categoryIDGroupBy := range productCategoryIdList {
		productGroupLevel, err := GetLevelByTaskRunIDProductCategoryID(ctx, req.DiagnoseTaskRunID, categoryIDGroupBy.ProductCategoryID)
		if err != nil {
			logger.CtxError(ctx, "GetLevelByTaskRunIDProductCategoryID err:%v", err)
			return nil, err
		}
		//查询productCategory
		productCategory, err := GetProductCategoryByID(ctx, categoryIDGroupBy.ProductCategoryID)
		if err != nil {
			logger.CtxError(ctx, "GetProductCategoryByID err:%v", err)
			return nil, err
		}
		result.ProductCategoryLevels = append(result.ProductCategoryLevels, &diagnose_task_run.ProductCategoryLevel{
			ProductCategory:     productCategory,
			DiagnoseResultLevel: productGroupLevel,
		})

	}
	return result, nil
}
