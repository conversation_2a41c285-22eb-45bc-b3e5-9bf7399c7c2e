package common

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"sync"
)

const (
	commonMapKeyDiagnoseTaskOrigin = "diagnose_task_origin"
)

var (
	service *Service
	once    sync.Once
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetCommonService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("GetCommonService"),
		}
	})
	return service
}

func (s *Service) GetCommonMapConfig(ctx context.Context, req *common.GetCommonMapConfigReq) (map[string]string, error) {
	switch req.Key {
	case commonMapKeyDiagnoseTaskOrigin:
		return map[string]string{
			"1": "诊断工具",
			"2": "火种工单",
			"3": "飞书群",
			"4": "诊断bot",
		}, nil
	}
	return nil, nil
}
