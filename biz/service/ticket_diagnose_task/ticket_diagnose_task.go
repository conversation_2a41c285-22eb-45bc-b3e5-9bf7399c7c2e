package ticket_diagnose_task

import (
	diagnoseTaskRunSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	diagnoseTaskV2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/ticket_diagnose_task"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"github.com/samber/lo"
	"sync"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize = int32(10)
	defaultPageNum  = int32(1)
	defaultUnknown  = "unknown"
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetTicketDiagnoseTaskService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("TicketDiagnoseTaskService"),
		}
	})
	return service
}

func (s *Service) DescribeTaskDiagnoseItem(ctx context.Context,
	req ticket_diagnose_task.DescribeTaskDiagnoseItemReq) (*ticket_diagnose_task.DescribeTaskDiagnoseItemResult, error) {
	logger := s.logger.WithFunc("DescribeTaskDiagnoseItem")
	queryDiagnoseTaskRunItemReq := &diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq{
		DiagnoseTaskRunID:   req.DiagnoseTaskID,
		DiagnoseItemID:      req.DiagnoseItemID,
		DiagnoseResultLevel: req.DiagnoseResultLevel,
		InstanceID:          req.InstanceID,
		PageNumber:          req.PageNumber,
		PageSize:            req.PageSize,
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItemDetail(ctx, queryDiagnoseTaskRunItemReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunItemDetail err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.DescribeTaskDiagnoseItemResult{
		DiagnoseResultLevel: result.DiagnoseResultLevel,
		DiagnoseResults:     result.DiagnoseResults,
		Pagination:          result.Pagination,
	}, nil
}

func (s *Service) QueryDiagnoseTaskCategory(ctx context.Context,
	req ticket_diagnose_task.QueryDiagnoseTaskCategoryReq) (*ticket_diagnose_task.QueryDiagnoseTaskCategoryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskCategory")
	queryDiagnoseTaskRunCategoryReq := &diagnose_task_run.QueryDiagnoseTaskRunCategoryReq{
		DiagnoseTaskRunID: req.DiagnoseTaskID,
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunCategory(ctx, queryDiagnoseTaskRunCategoryReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunCategory err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.QueryDiagnoseTaskCategoryResult{
		DiagnoseTaskCategoryList: result.DiagnoseTaskRunCategoryList,
	}, nil
}

func (s *Service) QueryDiagnoseTaskSummary(ctx context.Context,
	req ticket_diagnose_task.QueryDiagnoseTaskSummaryReq) (*ticket_diagnose_task.QueryDiagnoseTaskSummaryResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskSummary")
	queryDiagnoseTaskRunSummaryReq := &diagnose_task_run.QueryDiagnoseTaskRunSummaryReq{
		DiagnoseTaskRunID: req.DiagnoseTaskID,
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunSummary(ctx, queryDiagnoseTaskRunSummaryReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunSummary err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.QueryDiagnoseTaskSummaryResult{
		DiagnoseResultLevel:  result.DiagnoseResultLevel,
		DiagnoseTemplateName: result.DiagnoseTemplateName,
		CreateUserID:         result.CreateUserID,
		CreateUserName:       result.CreateUserName,
		Progress:             result.Progress,
		Feedback:             result.Feedback,
		StartTime:            result.StartTime,
		EndTime:              result.EndTime,
		DiagnoseStartTime:    result.DiagnoseStartTime,
		DiagnoseEndTime:      result.DiagnoseEndTime,
	}, nil
}

func (s *Service) QueryDiagnoseTaskResources(ctx context.Context,
	req ticket_diagnose_task.QueryDiagnoseTaskResourcesReq) (*ticket_diagnose_task.QueryDiagnoseTaskResourcesResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskResources")
	queryDiagnoseTaskRunResourceReq := &diagnose_task_run.QueryDiagnoseTaskRunResourceReq{
		DiagnoseTaskRunID:  req.DiagnoseTaskID,
		DiagnoseItemIDs:    req.DiagnoseItemIDs,
		ProductCategoryIDs: req.ProductCategoryIDs,
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunResource(ctx, queryDiagnoseTaskRunResourceReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunResource err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.QueryDiagnoseTaskResourcesResult{
		DiagnoseResources: result.DiagnoseResources,
	}, nil
}
func (s *Service) QueryDiagnoseTaskItems(ctx context.Context,
	req ticket_diagnose_task.QueryDiagnoseTaskItemsReq) (*ticket_diagnose_task.QueryDiagnoseTaskItemsResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskItems")
	//QueryDiagnoseTaskRunItems
	queryDiagnoseTaskRunItemsReq := &diagnose_task_run.QueryDiagnoseTaskRunItemsReq{
		DiagnoseTaskRunID:  req.DiagnoseTaskID,
		ProductCategoryIDs: req.ProductCategoryIDs,
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItems(ctx, queryDiagnoseTaskRunItemsReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunItems err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.QueryDiagnoseTaskItemsResult{
		DiagnoseItems: result.DiagnoseItems,
	}, nil
}

func (s *Service) ListTicketDiagnoseTask(ctx context.Context,
	req ticket_diagnose_task.ListTicketDiagnoseTaskReq) (*ticket_diagnose_task.ListTicketDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("ListTicketDiagnoseTask")
	queryDiagnoseTaskRunListReq := &diagnose_task_run.QueryDiagnoseTaskRunListReq{
		TicketID:         lo.ToPtr(req.TicketID),
		Status:           req.Status,
		PageNumber:       req.PageNumber,
		PageSize:         req.PageSize,
		AscSortByEndTime: req.AscSortByEndTime,
	}
	diagnoseTaskRuns, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunList(ctx, queryDiagnoseTaskRunListReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunList err:%v", err)
		return nil, err
	}
	if diagnoseTaskRuns == nil {
		return &ticket_diagnose_task.ListTicketDiagnoseTaskResult{
			DiagnoseTaskList: make([]*ticket_diagnose_task.DiagnoseTask, 0),
			Pagination: &common.Pagination{
				PageNumber: defaultPageNum,
				PageSize:   defaultPageSize,
				TotalCount: 0,
			},
		}, nil
	}
	diagnoseTaskList := make([]*ticket_diagnose_task.DiagnoseTask, 0)
	for _, taskRun := range diagnoseTaskRuns.DiagnoseTaskRunList {
		diagnoseTask := &ticket_diagnose_task.DiagnoseTask{
			DiagnoseTaskID:       taskRun.Id,
			DiagnoseTemplateName: taskRun.Name,
			Status:               taskRun.Status,
			StartTime:            taskRun.StartTime,
			EndTime:              taskRun.EndTime,
			CreateUserID:         taskRun.RunUserID,
			CreateUserName:       taskRun.RunUserID,
			TicketID:             taskRun.TicketID,
			Feedback:             taskRun.Feedback,
			DiagnoseStartTime:    taskRun.DiagnoseStartTime,
			DiagnoseEndTime:      taskRun.DiagnoseEndTime,
		}
		diagnoseTaskList = append(diagnoseTaskList, diagnoseTask)
	}
	return &ticket_diagnose_task.ListTicketDiagnoseTaskResult{
		DiagnoseTaskList: diagnoseTaskList,
		Pagination:       diagnoseTaskRuns.Pagination,
	}, nil
}

func (s *Service) ListDiagnoseTask(ctx context.Context,
	req ticket_diagnose_task.ListDiagnoseTaskReq) (*ticket_diagnose_task.ListDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("ListDiagnoseTask")
	queryDiagnoseTaskRunListReq := &diagnose_task_run.QueryDiagnoseTaskRunListReq{
		TicketID:            req.TicketID,
		Status:              req.Status,
		PageNumber:          req.PageNumber,
		PageSize:            req.PageSize,
		AscSortByEndTime:    req.AscSortByEndTime,
		StartTime:           req.StartTime,
		EndTime:             req.EndTime,
		DiagnoseTemplateIDs: req.DiagnoseTemplateIDs,
	}
	diagnoseTaskRuns, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunList(ctx, queryDiagnoseTaskRunListReq)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunList err:%v", err)
		return nil, err
	}
	if diagnoseTaskRuns == nil {
		return &ticket_diagnose_task.ListDiagnoseTaskResult{
			DiagnoseTaskList: make([]*ticket_diagnose_task.DiagnoseTask, 0),
			Pagination: &common.Pagination{
				PageNumber: defaultPageNum,
				PageSize:   defaultPageSize,
				TotalCount: 0,
			},
		}, nil
	}
	diagnoseTaskList := make([]*ticket_diagnose_task.DiagnoseTask, 0)
	for _, taskRun := range diagnoseTaskRuns.DiagnoseTaskRunList {
		diagnoseTask := &ticket_diagnose_task.DiagnoseTask{
			DiagnoseTaskID:       taskRun.Id,
			DiagnoseTemplateName: taskRun.DiagnoseTemplateName,
			Status:               taskRun.Status,
			StartTime:            taskRun.StartTime,
			EndTime:              taskRun.EndTime,
			CreateUserID:         taskRun.RunUserID,
			CreateUserName:       taskRun.RunUserID,
			TicketID:             taskRun.TicketID,
			Feedback:             taskRun.Feedback,
			DiagnoseStartTime:    taskRun.DiagnoseStartTime,
			DiagnoseEndTime:      taskRun.DiagnoseEndTime,
		}
		diagnoseTaskList = append(diagnoseTaskList, diagnoseTask)
	}
	return &ticket_diagnose_task.ListDiagnoseTaskResult{
		DiagnoseTaskList: diagnoseTaskList,
		Pagination:       diagnoseTaskRuns.Pagination,
	}, nil
}

func (s *Service) CreateDiagnoseTask(ctx context.Context,
	req ticket_diagnose_task.CreateDiagnoseTaskReq) (*ticket_diagnose_task.CreateDiagnoseTaskResult, error) {
	logger := s.logger.WithFunc("CreateDiagnoseTask")
	name := req.TicketID //todo:待确认name
	createDiagnoseTaskV2Req := &diagnose_task_v2.CreateDiagnoseTaskV2Req{
		Name:               name,
		DiagnoseTemplateID: req.DiagnoseTemplateID,
		DiagnoseItemIDs:    req.DiagnoseItemIDs,
		CreateUserID:       req.CreateUserID,
		ProductCategoryIDs: req.ProductCategoryIDs,
		Dimension:          req.Dimension,
		TicketID:           lo.ToPtr(req.TicketID),
		DiagnoseType:       req.DiagnoseType,
		Origin:             lo.ToPtr(diagnoseTaskV2Svc.DiagnoseTaskOriginTicket),
	}
	diagnoseTaskV2ID, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().CreateDiagnoseTask(ctx, *createDiagnoseTaskV2Req)
	if err != nil {
		logger.CtxError(ctx, "CreateDiagnoseTaskV2 err:%v", err)
		return nil, err
	}
	//RunDiagnoseTask
	runDiagnoseTaskReq := &diagnose_task_run.RunDiagnoseTaskReq{
		DiagnoseTaskID:    diagnoseTaskV2ID,
		RunUserID:         req.CreateUserID,
		DiagnoseResources: req.DiagnoseResources,
		DiagnoseStartTime: req.DiagnoseStartTime,
		DiagnoseEndTime:   req.DiagnoseEndTime,
		Name:              lo.ToPtr(name),
		AccountID:         req.AccountID,
	}
	runResult, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().RunDiagnoseTask(ctx, runDiagnoseTaskReq)
	if err != nil {
		logger.CtxError(ctx, "RunDiagnoseTask err:%v", err)
		return nil, err
	}
	return &ticket_diagnose_task.CreateDiagnoseTaskResult{
		DiagnoseTaskID: runResult.DiagnoseTaskRunID,
	}, nil
}
