package diagnose_item

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	diagnoseItemHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_item"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"
)

var (
	service *DiagnoseItemService
	once    sync.Once
)

type DiagnoseItemService struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseItemService() *DiagnoseItemService {
	once.Do(func() {
		service = &DiagnoseItemService{
			logger: utils.NewModuleLogger("DiagnoseItemService"),
		}
	})
	return service
}

func (s *DiagnoseItemService) RegisterDiagnoseItem(ctx context.Context, req *diagnoseItemHertz.RegisterDiagnoseItemRequest) (*diagnoseItemHertz.RegisterDiagnoseItemResult, error) {
	//todo:根据创建者的身份信息鉴权

	//鉴权通过后构造要删除的诊断项
	//1.需要先根据DiagnoseItemCode查询是否记录是否已经存在，但已经被软删除了
	//2.如果是软删除的，则需要先删除这个数据，然后再创建记录
	//3.否则就直接创建记录

	//1.查询是否已经存在
	res, err := dal.FindDiagnoseItemByCode(ctx, req.DiagnoseItemCode)
	if err == nil {
		//表示已经存在数据，则判断是否是软删除的
		if res.DeleteAt != nil {
			//表示已经被软删除了
			//删除数据
			err = dal.DeleteDiagnoseItem(ctx, res.DiagnoseItemID)
			if err != nil {
				s.logger.WithFunc("RegisterDiagnoseItem").Error("DeleteDiagnoseItem err:%v", err)
				return nil, err
			}
		} else {
			//表示已经存在数据，但是没有被软删除，直接返回错误
			return nil, errorcode.ErrDataAlreadyExist.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", res.DiagnoseItemID))
		}
	} else if err.Error() != "record not found" {
		s.logger.WithFunc("RegisterDiagnoseItem").Error("FindDiagnoseItemByCode err:%v", err)
		return nil, err
	}

	//2.表示不存在数据，直接创建记录
	s.logger.WithFunc("RegisterDiagnoseItem")
	now := time.Now()
	asl := utils.GetDiagnoseItemAsl(req.DiagnoseItemName, req.Activity)
	item := &model.DiagnoseItem{
		CreatorID:               &req.UserInfo.UserID,
		CreatorName:             &req.UserInfo.UserName,
		CreateTime:              &now,
		DiagnoseItemName:        &req.DiagnoseItemName,
		DiagnoseItemCode:        &req.DiagnoseItemCode,
		Status:                  &req.Status,
		Product:                 &req.Product,
		Subproduct:              &req.Subproduct,
		AccessType:              &req.AccessType,
		AccessResponsiblePerson: &req.AccessResponsiblePerson,
		AccessPath:              &req.AccessPath,
		InterAction:             &req.InterAction,
		InterVersion:            &req.InterVersion,
		Suggestion:              &req.Suggestion,
		SuggestionLink:          &req.SuggestionLink,
		Timeout:                 &req.Timeout,
		StateMachineInfo:        asl,
		Activity:                &req.Activity,
		ResourceType:            &req.ResourceType,
		DiagnoseItemDescription: req.DiagnoseItemDescription,
	}

	//通过productCategoryID查询对应的productCategory
	_, err = dal.QueryProductCategoryByID(ctx, req.ProductCategoryID)
	if err != nil {
		s.logger.WithFunc("RegisterDiagnoseItem").Error("QueryProductCategoryByID err:%v", err)
		return nil, errors.New("ProductCategoryID is not exist")
	}
	item.ProductCategoryID = req.ProductCategoryID

	if req.InterParams != nil {
		jsonInterParams, err := json.Marshal(req.InterParams)
		if err != nil {
			s.logger.WithFunc("RegisterDiagnoseItem ").Error("json.Marshal err:%v", err)
			return nil, err
		}
		StrInterParams := string(jsonInterParams)
		item.InterParams = &StrInterParams
	}
	err = dal.AddDiagnoseItem(ctx, item)
	if err != nil {
		return nil, err
	}
	return &diagnoseItemHertz.RegisterDiagnoseItemResult{
		DiagnoseItemID: &item.DiagnoseItemID,
	}, nil
}

func (s *DiagnoseItemService) ListDiagnoseItem(ctx context.Context, req *diagnoseItemHertz.ListDiagnoseItemRequest) (*diagnoseItemHertz.ListDiagnoseItemResult, error) {
	//todo:根据创建者的身份信息鉴权

	s.logger.WithFunc("ListDiagnoseItem")
	dbItems, count, err := dal.ListDiagnoseItem(ctx, int(req.PageNumber), int(req.PageSize), req)
	if err != nil {
		return nil, err
	}
	items := make([]*diagnoseItemHertz.DiagnoseItem, 0)
	// todo: 需要查找到每个诊断项关联的诊断任务ID
	for _, dbItem := range dbItems {
		createTime := dbItem.CreateTime.String()
		var updateTime string
		if dbItem.UpdateTime != nil {
			updateTime = dbItem.UpdateTime.String()
		}
		interParams := make(map[string]string)
		err := json.Unmarshal([]byte(*dbItem.InterParams), &interParams)
		updater := &diagnoseItemHertz.UserInfo{}
		if dbItem.UpdaterID != nil && dbItem.UpdaterName != nil {
			updater.UserID = *dbItem.UpdaterID
			updater.UserName = *dbItem.UpdaterName
		}
		//通过productCategoryID查询对应的productCategory
		productCategory, err := dal.QueryProductCategoryByID(ctx, dbItem.ProductCategoryID)
		if err != nil {
			s.logger.WithFunc("ListDiagnoseItem").CtxError(ctx, fmt.Sprintf("QueryProductCategoryByID itemID = %d,productCategoryID = %d err %v", dbItem.DiagnoseItemID, dbItem.ProductCategoryID, err))
			return nil, err
		}
		item := &diagnoseItemHertz.DiagnoseItem{
			DiagnoseItemID:          &dbItem.DiagnoseItemID,
			DiagnoseItemName:        dbItem.DiagnoseItemName,
			DiagnoseItemCode:        dbItem.DiagnoseItemCode,
			Status:                  dbItem.Status,
			Product:                 dbItem.Product,
			Subproduct:              dbItem.Subproduct,
			AccessType:              dbItem.AccessType,
			AccessResponsiblePerson: dbItem.AccessResponsiblePerson,
			AccessPath:              dbItem.AccessPath,
			InterAction:             dbItem.InterAction,
			InterVersion:            dbItem.InterVersion,
			Suggestion:              dbItem.Suggestion,
			SuggestionLink:          dbItem.SuggestionLink,
			Timeout:                 dbItem.Timeout,
			CreateTime:              &createTime,
			UpdateTime:              &updateTime,
			Creator: &diagnoseItemHertz.UserInfo{
				UserID:   *dbItem.CreatorID,
				UserName: *dbItem.CreatorName,
			},
			Updater:                 updater,
			InterParams:             interParams,
			ResourceType:            dbItem.ResourceType,
			ProductCategoryID:       &dbItem.ProductCategoryID,
			Activity:                dbItem.Activity,
			DiagnoseItemDescription: dbItem.DiagnoseItemDescription,
			ProductCategory: &product_category.ProductCategory{
				Product:        productCategory.Product,
				SubProduct:     productCategory.SubProduct,
				ResourceType:   productCategory.ResourceType,
				ProductCn:      productCategory.ProductCn,
				SubProductCn:   productCategory.SubProductCn,
				ResourceTypeCn: productCategory.ResourceTypeCn,
				Id:             productCategory.ID,
			},
		}
		relations, err := dal.FindRelationByItemID(ctx, dbItem.DiagnoseItemID)
		if err != nil {
			s.logger.WithFunc("ListDiagnoseItem").CtxError(ctx, fmt.Sprintf("FindRelationByItemID itemID = %d err %v", dbItem.DiagnoseItemID, err))
			return nil, err
		}
		templateIds := make([]int64, 0)
		for _, relation := range relations {
			templateIds = append(templateIds, relation.DiagnoseTemplateID)
		}
		item.DiagnoseTemplateIDs = templateIds
		items = append(items, item)
	}
	return &diagnoseItemHertz.ListDiagnoseItemResult{
		DiagnoseItems: items,
		Pagination: &common.Pagination{
			PageNumber: int32(req.PageNumber),
			PageSize:   int32(req.PageSize),
			TotalCount: count,
		},
	}, nil
}

func (s *DiagnoseItemService) UpdateDiagnoseItem(ctx context.Context, req *diagnoseItemHertz.UpdateDiagnoseItemRequest) (*diagnoseItemHertz.UpdateDiagnoseItemResult, error) {
	//todo:根据创建者的身份信息鉴权

	//1.先通过ID判断该诊断项是否存在，或者已经删除
	//2.如果不存在或者已经删除，则返回错误
	//3.如果存在，则更新数据
	res, err := dal.FindDiagnoseItemByID(ctx, *req.DiagnoseItemID)
	if err != nil {
		if err.Error() != "record not found" {
			s.logger.WithFunc("UpdateDiagnoseItem").Error("FindDiagnoseItemByID err:%v", err)
			return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", *req.DiagnoseItemID))
		} else {
			s.logger.WithFunc("UpdateDiagnoseItem").Error("FindDiagnoseItemByID err:%v", err)
			return nil, err
		}
	}
	if res.DeleteAt != nil {
		s.logger.WithFunc("UpdateDiagnoseItem").Error("DiagnoseItem[%d] has been deleted", *req.DiagnoseItemID)
		return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("DiagnoseItem[%d]", *req.DiagnoseItemID))
	}

	diagnoseItemName := ""

	//鉴权通过后构造要更新的诊断项
	s.logger.WithFunc("UpdateDiagnoseItem")
	item := &model.DiagnoseItem{
		DiagnoseItemID: *req.DiagnoseItemID,
		UpdaterID:      &req.UserInfo.UserID,
		UpdaterName:    &req.UserInfo.UserName,
	}
	if req.DiagnoseItemName != nil && *req.DiagnoseItemName != "" {
		item.DiagnoseItemName = req.DiagnoseItemName
		diagnoseItemName = *req.DiagnoseItemName
		item.StateMachineInfo = utils.GetDiagnoseItemAsl(*req.DiagnoseItemName, *res.Activity)
	}
	if req.DiagnoseItemCode != nil {
		item.DiagnoseItemCode = req.DiagnoseItemCode
	}
	if req.Status != nil {
		item.Status = req.Status
	}
	if req.Product != nil {
		item.Product = req.Product
	}
	if req.Subproduct != nil {
		item.Subproduct = req.Subproduct
	}
	if req.AccessType != nil {
		item.AccessType = req.AccessType
	}
	if req.AccessResponsiblePerson != nil {
		item.AccessResponsiblePerson = req.AccessResponsiblePerson
	}
	if req.AccessPath != nil {
		item.AccessPath = req.AccessPath
	}
	if req.InterAction != nil {
		item.InterAction = req.InterAction
	}
	if req.InterVersion != nil {
		item.InterVersion = req.InterVersion
	}
	if req.Suggestion != nil {
		item.Suggestion = req.Suggestion
	}
	if req.SuggestionLink != nil {
		item.SuggestionLink = req.SuggestionLink
	}
	if req.Timeout != nil {
		item.Timeout = req.Timeout
	}
	if req.DiagnoseItemDescription != nil {
		item.DiagnoseItemDescription = req.DiagnoseItemDescription
	}
	if req.Activity != nil && *req.Activity != "" {
		item.Activity = req.Activity
		if diagnoseItemName != "" {
			item.StateMachineInfo = utils.GetDiagnoseItemAsl(diagnoseItemName, *req.Activity)
		} else {
			item.StateMachineInfo = utils.GetDiagnoseItemAsl(*res.DiagnoseItemName, *req.Activity)
		}

	}
	err = dal.UpdateDiagnoseItem(ctx, item)
	if err != nil {
		return nil, err
	}
	IsSuccess := "success"
	return &diagnoseItemHertz.UpdateDiagnoseItemResult{
		IsSuccess: &IsSuccess,
	}, nil
}

func (s *DiagnoseItemService) DeleteDiagnoseItem(ctx context.Context, req *diagnoseItemHertz.DeleteDiagnoseItemRequest) (*diagnoseItemHertz.DeleteDiagnoseItemResult, error) {
	//todo:根据创建者的身份信息鉴权

	// 在软删除前需要先判断该诊断项是否还被诊断场景所绑定，如果存在关联则不能删除，需要先删除关联
	s.logger.WithFunc("DeleteDiagnoseItem")
	//1.先软删除item_template关联表中templateId对应的数据
	//2.再软删除diagnose_template中的数据
	relations, err := dal.FindRelationByItemID(ctx, *req.DiagnoseItemID)
	if err != nil && err.Error() != "record not found" {
		s.logger.CtxError(ctx, "FindItemsByTemplateID err:%v", err)
		return nil, err
	}
	now := time.Now()
	if len(relations) > 0 {
		//存在关联，则软删除关联
		for _, relation := range relations {
			if relation.DeleteAt == nil {
				newRelation := &model.ItemTemplate{
					DiagnoseTemplateID: relation.DiagnoseTemplateID,
					DiagnoseItemID:     *req.DiagnoseItemID,
					DeleterName:        &req.UserInfo.UserName,
					DeleterID:          &req.UserInfo.UserID,
					DeleteAt:           &now,
				}
				err := dal.UpdateItemTemplate(ctx, newRelation)
				if err != nil {
					s.logger.WithFunc("DeleteDiagnoseItem").CtxError(ctx, fmt.Sprintf(" UpdateItemTemplate err :%v", err))
					return nil, err
				}
			}
		}
	}

	//鉴权通过后构造要删除的诊断项
	s.logger.WithFunc("DeleteDiagnoseItem")
	item := &model.DiagnoseItem{
		DeleterID:      &req.UserInfo.UserID,
		DeleterName:    &req.UserInfo.UserName,
		DiagnoseItemID: *req.DiagnoseItemID,
		DeleteAt:       &now,
	}
	err = dal.UpdateDiagnoseItem(ctx, item)
	if err != nil {
		return nil, err
	}
	IsSuccess := "success"
	return &diagnoseItemHertz.DeleteDiagnoseItemResult{
		IsSuccess: &IsSuccess,
	}, nil
}

func (s *DiagnoseItemService) GetAllDiagnoseItemWithProduct(ctx context.Context, req *diagnoseItemHertz.GetAllDiagnoseItemWithProductRequest) (*diagnoseItemHertz.GetAllDiagnoseItemWithProductResult, error) {
	//todo:根据创建者的身份信息鉴权

	s.logger.WithFunc("ListDiagnoseItem")
	dbItems, err := dal.GetAllDiagnoseItem(ctx)
	if err != nil {
		return nil, err
	}
	result := make(map[string]*diagnoseItemHertz.SubProduct)
	for _, dbItem := range dbItems {
		createTime := dbItem.CreateTime.String()
		var updateTime string
		if dbItem.UpdateTime != nil {
			updateTime = dbItem.UpdateTime.String()
		}
		interParams := make(map[string]string)
		err := json.Unmarshal([]byte(*dbItem.InterParams), &interParams)
		item := &diagnoseItemHertz.DiagnoseItem{
			DiagnoseItemID:          &dbItem.DiagnoseItemID,
			DiagnoseItemName:        dbItem.DiagnoseItemName,
			DiagnoseItemCode:        dbItem.DiagnoseItemCode,
			Status:                  dbItem.Status,
			Product:                 dbItem.Product,
			Subproduct:              dbItem.Subproduct,
			AccessType:              dbItem.AccessType,
			AccessResponsiblePerson: dbItem.AccessResponsiblePerson,
			AccessPath:              dbItem.AccessPath,
			InterAction:             dbItem.InterAction,
			InterVersion:            dbItem.InterVersion,
			Suggestion:              dbItem.Suggestion,
			SuggestionLink:          dbItem.SuggestionLink,
			Timeout:                 dbItem.Timeout,
			CreateTime:              &createTime,
			UpdateTime:              &updateTime,
			Creator: &diagnoseItemHertz.UserInfo{
				UserID:   *dbItem.CreatorID,
				UserName: *dbItem.CreatorName,
			},
			Updater: &diagnoseItemHertz.UserInfo{
				UserID:   *dbItem.UpdaterID,
				UserName: *dbItem.UpdaterName,
			},
			InterParams: interParams,
		}
		relations, err := dal.FindRelationByItemID(ctx, dbItem.DiagnoseItemID)
		if err != nil {
			s.logger.WithFunc("ListDiagnoseItem").CtxError(ctx, fmt.Sprintf("FindRelationByItemID err %v", err))
			return nil, err
		}
		templateIds := make([]int64, 0)
		for _, relation := range relations {
			templateIds = append(templateIds, relation.DiagnoseTemplateID)
		}
		item.DiagnoseTemplateIDs = templateIds
		if result[*item.Product] == nil {
			result[*item.Product] = &diagnoseItemHertz.SubProduct{
				SubProduct: make(map[string][]*diagnoseItemHertz.DiagnoseItem),
			}
		}
		result[*item.Product].SubProduct[*item.Subproduct] = append(result[*item.Product].SubProduct[*item.Subproduct], item)
	}
	return &diagnoseItemHertz.GetAllDiagnoseItemWithProductResult{
		Product: result,
	}, nil
}
