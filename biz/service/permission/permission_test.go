package permission

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/eps_permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestPermissionService_GetUserRoles(t *testing.T) {
	t.Run("case #1:正确", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).GetUserRoles).Return([]string{""}, nil).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.GetUserRoles(context.Background(), "")

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
	t.Run("case #2:报错", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).GetUserRoles).Return([]string{""}, errors.New("")).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.GetUserRoles(context.Background(), "")

			// Assert
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

func TestPermissionService_GetUserPermission(t *testing.T) {
	t.Run("case #1:正确", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).GetUserPermission).Return([]*permission.UserPermission{&permission.UserPermission{}}, nil).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.GetUserPermission(context.Background(), "")

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
	t.Run("case #2：报错", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).GetUserPermission).Return([]*permission.UserPermission{&permission.UserPermission{}}, fmt.Errorf("your error")).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.GetUserPermission(context.Background(), "")

			// Assert
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

func TestPermissionService_CheckPermissions(t *testing.T) {
	t.Run("case #1:正确", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).CheckPermissions).Return(map[string]bool{
				"CommonResource//C360//Default01": false,
				"CommonResource//C360//Default02": false,
				"CommonResource//pdp//Default01":  false,
			}, nil).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.CheckPermissions(context.Background(), "", []*permission.Rule{&permission.Rule{}})

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
	t.Run("case #2:报错", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).CheckPermissions).Return(map[string]bool{"": false}, errors.New("")).Build()

			// Act
			v1 := GetPermissionService()
			_, err := v1.CheckPermissions(context.Background(), "", []*permission.Rule{&permission.Rule{}})

			// Assert
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

// func TestPermissionService_CheckCustomerViewablePermission(t *testing.T) {
// 	t.Run("case #1:部分测试", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("mujinyong").Build()
// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
// 			mockey.Mock((*eps_permission.EpsPermissionService).GetUserRoles).Return([]string{""}, nil).Build()

// 			mockey.Mock((*PermissionService).GetViewableCustomerList).Return(constants.ViewRangeType_Part, []string{"ACC_1"}, nil).Build()
// 			mockey.Mock((*PermissionService).GetProductRelatedViewableCustomerList).Return([]string{"ACC_2"}, nil).Build()

// 			v1 := GetPermissionService()
// 			part, list, err := v1.CheckCustomerViewablePermission(context.Background())

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(list, convey.ShouldResemble, []string{"ACC_1", "ACC_2"})
// 			convey.So(part, convey.ShouldEqual, constants.ViewRangeType_Part)
// 		})
// 	})

// 	t.Run("case #2:全部测试", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("mujinyong").Build()
// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
// 			mockey.Mock((*eps_permission.EpsPermissionService).GetUserRoles).Return([]string{""}, nil).Build()

// 			mockey.Mock((*PermissionService).GetViewableCustomerList).Return(constants.ViewRangeType_All, []string{"ACC_1"}, nil).Build()
// 			mockey.Mock((*PermissionService).GetProductRelatedViewableCustomerList).Return([]string{"ACC_2"}, nil).Build()

// 			v1 := GetPermissionService()
// 			part, list, err := v1.CheckCustomerViewablePermission(context.Background())

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(list, convey.ShouldBeNil)
// 			convey.So(part, convey.ShouldEqual, constants.ViewRangeType_All)
// 		})
// 	})

// 	t.Run("case #3:NoType测试", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("mujinyong").Build()
// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
// 			mockey.Mock((*eps_permission.EpsPermissionService).GetUserRoles).Return([]string{""}, nil).Build()

// 			mockey.Mock((*PermissionService).GetViewableCustomerList).Return(constants.ViewRangeType_No, []string{"ACC_1"}, nil).Build()
// 			mockey.Mock((*PermissionService).GetProductRelatedViewableCustomerList).Return([]string{"ACC_2"}, nil).Build()

// 			v1 := GetPermissionService()
// 			part, list, err := v1.CheckCustomerViewablePermission(context.Background())

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(list, convey.ShouldResemble, []string{"ACC_2"})
// 			convey.So(part, convey.ShouldEqual, constants.ViewRangeType_Part)
// 		})
// 	})
// }

// func TestPermissionService_CheckSingleCustomerViewable(t *testing.T) {
// 	t.Run("case #1: 部分可见", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock((*errorcode.ErrorStructure).WithArgs).Return(nil).Build()
// 			mockey.Mock((*PermissionService).CheckCustomerViewablePermission).Return(2, []string{"ACC"}, nil).Build()

// 			// Act
// 			v := &PermissionService{}
// 			ok, err := v.CheckSingleCustomerViewable(context.Background(), "ACC")

// 			// Assert
// 			convey.So(ok, convey.ShouldBeTrue)
// 			convey.So(err, convey.ShouldBeNil)
// 		})
// 	})

// 	t.Run("case #2: 正常通过", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock((*errorcode.ErrorStructure).WithArgs).Return(nil).Build()
// 			mockey.Mock((*PermissionService).CheckCustomerViewablePermission).Return(2, []string{"ACC"}, nil).Build()

// 			// Act
// 			v := &PermissionService{}
// 			ok, err := v.CheckSingleCustomerViewable(context.Background(), "ACC")

// 			// Assert
// 			convey.So(ok, convey.ShouldBeTrue)
// 			convey.So(err, convey.ShouldBeNil)
// 		})
// 	})
// 	t.Run("case #3: 不可见", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock((*errorcode.ErrorStructure).WithArgs).Return(nil).Build()
// 			mockey.Mock((*PermissionService).CheckCustomerViewablePermission).Return(0, []string{"ACC"}, nil).Build()

// 			// Act
// 			v := &PermissionService{}
// 			ok, err := v.CheckSingleCustomerViewable(context.Background(), "ACC")

// 			// Assert
// 			convey.So(ok, convey.ShouldBeFalse)
// 			convey.So(err, convey.ShouldBeNil)
// 		})
// 	})
// 	t.Run("case #4: 全部可见", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock((*errorcode.ErrorStructure).WithArgs).Return(nil).Build()
// 			mockey.Mock((*PermissionService).CheckCustomerViewablePermission).Return(1, []string{"ACC"}, nil).Build()

// 			// Act
// 			v := &PermissionService{}
// 			ok, err := v.CheckSingleCustomerViewable(context.Background(), "ACC")

// 			// Assert
// 			convey.So(ok, convey.ShouldBeTrue)
// 			convey.So(err, convey.ShouldBeNil)
// 		})
// 	})
// }

// func TestPermissionService_GetViewableCustomerList(t *testing.T) {
// 	t.Run("case #1:全部可见", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()

// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()

// 			mockey.Mock(tcc.GetPermissionConfig).Return(&tcc.PermissionConfig{
// 				CustomerAllViewRangeRoleList:  []string{"role1"},
// 				CustomerPartViewRangeRoleList: []string{"role2"},
// 				CustomerNoViewRangeRoleList:   []string{"role3"},
// 			}, nil).Build()

// 			mockey.Mock(dal.FindAccountServiceTeamByConditions).Return([]*ldi_model.AccountServiceTeam{&ldi_model.AccountServiceTeam{
// 				AccountNumber: "aaa",
// 			}}, nil).Build()

// 			// Act
// 			rds.QueryLdi = &ldi_query.Query{}
// 			v1 := GetPermissionService()
// 			rangeType, _, err := v1.GetViewableCustomerList(context.Background(), []string{"role1"})

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(rangeType, convey.ShouldEqual, 1)
// 		})
// 	})
// 	t.Run("case #2:部分可见", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()

// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()

// 			mockey.Mock(tcc.GetPermissionConfig).Return(&tcc.PermissionConfig{
// 				CustomerAllViewRangeRoleList:  []string{""},
// 				CustomerPartViewRangeRoleList: []string{"role2"},
// 				CustomerNoViewRangeRoleList:   []string{"role3"},
// 			}, nil).Build()

// 			mockey.Mock(dal.FindAccountServiceTeamByConditions).Return([]*ldi_model.AccountServiceTeam{&ldi_model.AccountServiceTeam{
// 				AccountNumber: "aaa",
// 			}}, nil).Build()

// 			// Act
// 			rds.QueryLdi = &ldi_query.Query{}
// 			v1 := GetPermissionService()
// 			rangeType, _, err := v1.GetViewableCustomerList(context.Background(), []string{"role2"})

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(rangeType, convey.ShouldEqual, 2)
// 		})
// 	})

// 	t.Run("case #3:无权限", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()

// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()

// 			mockey.Mock(tcc.GetPermissionConfig).Return(&tcc.PermissionConfig{
// 				CustomerAllViewRangeRoleList:  []string{""},
// 				CustomerPartViewRangeRoleList: []string{"role2"},
// 				CustomerNoViewRangeRoleList:   []string{"role3"},
// 			}, nil).Build()

// 			mockey.Mock(dal.FindAccountServiceTeamByConditions).Return([]*ldi_model.AccountServiceTeam{&ldi_model.AccountServiceTeam{
// 				AccountNumber: "aaa",
// 			}}, nil).Build()

// 			// Act
// 			rds.QueryLdi = &ldi_query.Query{}
// 			v1 := GetPermissionService()
// 			rangeType, _, err := v1.GetViewableCustomerList(context.Background(), []string{"role3"})

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(rangeType, convey.ShouldEqual, 0)
// 		})
// 	})

// 	t.Run("case #4:CSM", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()

// 			mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()

// 			mockey.Mock(tcc.GetPermissionConfig).Return(&tcc.PermissionConfig{
// 				CustomerAllViewRangeRoleList:  []string{""},
// 				CustomerPartViewRangeRoleList: []string{"role2"},
// 				CustomerNoViewRangeRoleList:   []string{"role3"},
// 				OrganizationNumberList:        []string{"num"},
// 			}, nil).Build()

// 			mockey.Mock(dal.FindAccountServiceTeamByConditions).Return([]*ldi_model.AccountServiceTeam{&ldi_model.AccountServiceTeam{
// 				AccountNumber: "aaa",
// 			}}, nil).Build()

// 			mockey.Mock((*organization.OrgsManagementClient).BatchGetOrganization).Return(
// 				[]*organization.Organization{&organization.Organization{}}, nil).Build()

// 			mockey.Mock(organization.LoopGetLeaderOrganizationNode).Return(&organization.Organization{}).Build()
// 			mockey.Mock(organization.LoopGetOrganizationEmailList).Return([]string{""}).Build()

// 			mockey.Mock(dal.FindCustomerByConditions).Return([]*model.Customer{&model.Customer{
// 				ID:                 0,
// 				SfID:               "",
// 				ParentID:           "",
// 				CustomerNumber:     "ACC",
// 				MainCustomerNumber: "",
// 				CustomerName:       "",
// 				CustomerShortName:  "",
// 				Industry:           "",
// 				SubIndustry:        "",
// 				OwnerID:            "",
// 				OwnerEmail:         "",
// 				Country:            "",
// 				Region:             "",
// 				Province:           "",
// 				City:               "",
// 				CustomerTier:       "",
// 				CustomerPLevel:     "",
// 				Status:             "",
// 				Display:            0,
// 			}}, nil).Build()

// 			// Act
// 			rds.QueryLdi = &ldi_query.Query{}
// 			rds.QuerySignalFire = &query.Query{}
// 			v1 := GetPermissionService()
// 			rangeType, _, err := v1.GetViewableCustomerList(context.Background(), []string{"signal_fire_csm", "signal_fire_csm_leader", "sales"})

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 			convey.So(rangeType, convey.ShouldEqual, 2)
// 		})
// 	})
// }

// func TestPermissionService_GetProductRelatedViewableCustomerList(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(utils.CtxGetEmployeeEmail).Return("<EMAIL>").Build()

// 			mockey.Mock(product_management.GetClient).Return(&product_management.ProductManagementClient{}).Build()
// 			mockey.Mock((*product_management.ProductManagementClient).ListStandardProducts).Return([]*products_client.StandardProductResp{&products_client.StandardProductResp{}}, nil).Build()
// 			mockey.Mock(dal.CustomerProductFindCustomerNumberList).Return([]string{}, nil).Build()

// 			// Act
// 			rds.QuerySignalFire = &query.Query{}
// 			v1 := &PermissionService{
// 				logger: &utils.ModuleLogger{},
// 			}
// 			_, err := v1.GetProductRelatedViewableCustomerList(context.Background(), []string{constants.RoleKey_CloudProductMember})

// 			// Assert
// 			convey.So(err, convey.ShouldBeNil)
// 		})
// 	})
// 	t.Run("case #2", func(t *testing.T) {})
// }

// func TestPermissionService_GetMenuPermissionConfig(t *testing.T) {
// 	t.Run("case #1", func(t *testing.T) {
// 		mockey.PatchConvey("", t, func() {
// 			// Arrange
// 			mockey.Mock(tcc.GetPermissionConfig).Return(&tcc.PermissionConfig{
// 				MenuPermissionList: []*permission.MenuItem{&permission.MenuItem{
// 					MenuKey:                pointer.To("1"),
// 					MenuName:               pointer.To("1"),
// 					IsHidden:               nil,
// 					OperablePermissionList: nil,
// 					ViewablePermissionList: nil,
// 					Children:               nil,
// 				}},
// 			}, nil).Build()

// 			// Act
// 			v1 := &PermissionService{
// 				logger: &utils.ModuleLogger{},
// 			}
// 			_, err := v1.GetMenuPermissionConfig(context.Background())

//				// Assert
//				convey.So(err, convey.ShouldBeNil)
//			})
//		})
//	}
func TestPermissionService_AddUserRole(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			mockey.Mock((*eps_permission.EpsPermissionService).AddUserRoles).Return(nil).Build()

			// Act
			err := GetPermissionService().AddUserRole(context.Background(), "", "", 0)

			// Assert
			convey.So(err, convey.ShouldBeNil)
		})
	})
}
func TestPermissionService_GetRoleMembers(t *testing.T) {
	t.Run("case #1", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// Arrange
			//mockey.Mock(eps_permission.GetEpsPermissionService).Return(&eps_permission.EpsPermissionService{}).Build()
			mockey.Mock((*eps_permission.EpsPermissionService).GetRoleMembers).To(func(ctx context.Context, roleKey string) ([]string, error) {
				if roleKey == "signal_fire_customer_stability" {
					return []string{"1", "2"}, nil
				} else if roleKey == "signal_fire_csm" {
					return []string{"2", "3"}, nil
				}
				return []string{}, nil
			}).Build()

			// Act
			v := &PermissionService{
				logger: &utils.ModuleLogger{},
			}
			got, err := v.GetRoleMembers(context.Background(), []string{"signal_fire_customer_stability", "signal_fire_csm"})
			slices.Sort(got)

			// Assert
			convey.So(err, convey.ShouldBeNil)
			convey.So(got, convey.ShouldHaveLength, 3)
			convey.So(got, convey.ShouldResemble, []string{"1", "2", "3"})
		})
	})
}
