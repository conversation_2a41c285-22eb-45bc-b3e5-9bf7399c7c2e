package permission

import (
	"strings"

	"code.byted.org/gopkg/lang/v2/setx"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/eps_permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/thoas/go-funk"
	"golang.org/x/sync/errgroup"

	"context"
	"sync"
)

var (
	service *PermissionService
	once    sync.Once
)

type PermissionService struct {
	logger *utils.ModuleLogger
}

func GetPermissionService() *PermissionService {
	once.Do(func() {
		service = &PermissionService{
			logger: utils.NewModuleLogger("PermissionService"),
		}
	})
	return service
}

func (s *PermissionService) GetUserRoles(ctx context.Context, email string) ([]string, error) {
	logger := s.logger.WithFunc("GetUserRoles")
	result, err := eps_permission.GetEpsPermissionService().GetUserRoles(ctx, email)
	if err != nil {
		logger.CtxError(ctx, "eps_permission GetUserRoles err:%v", err)
		return nil, err
	}
	return result, nil
}

func (s *PermissionService) GetUserPermission(ctx context.Context, email string) ([]*permission.UserPermission, error) {
	logger := s.logger.WithFunc("GetUserPermission")
	result, err := eps_permission.GetEpsPermissionService().GetUserPermission(ctx, email)
	if err != nil {
		logger.CtxError(ctx, "eps_permission GetUserPermission err:%v", err)
		return nil, err
	}
	return result, nil
}

func (s *PermissionService) CheckPermissions(ctx context.Context, email string, rules []*permission.Rule) ([]*permission.CheckUserPermissionItem, error) {
	logger := s.logger.WithFunc("CheckPermissions")
	accessMap, err := eps_permission.GetEpsPermissionService().CheckPermissions(ctx, email, rules)
	if err != nil {
		logger.CtxError(ctx, "eps_permission CheckPermissions err:%v", err)
		return nil, err
	}
	resourceActionsAccessMap := make(map[string]map[string]bool)
	funk.ForEach(accessMap, func(permissionKey string, canAccess bool) {
		permissionKeyList := strings.Split(permissionKey, "//")
		if len(permissionKeyList) != 3 {
			return
		}
		resourceKey := permissionKeyList[1]
		actionKey := permissionKeyList[2]
		if len(resourceKey) != 0 && len(actionKey) != 0 {
			if accessMap, ok := resourceActionsAccessMap[resourceKey]; ok {
				accessMap[actionKey] = canAccess
			} else {
				accessMap := make(map[string]bool)
				accessMap[actionKey] = canAccess
				resourceActionsAccessMap[resourceKey] = accessMap
			}
		}
	})
	permissionItemList := make([]*permission.CheckUserPermissionItem, 0, len(resourceActionsAccessMap))
	funk.ForEach(resourceActionsAccessMap, func(resourceKey string, actionAccessMap map[string]bool) {
		permissionItem := &permission.CheckUserPermissionItem{
			ResourceKey:     resourceKey,
			ActionAccessMap: actionAccessMap,
		}
		permissionItemList = append(permissionItemList, permissionItem)
	})

	return permissionItemList, nil
}

// 用户数据权限校验
// 返回：
// 1、错误处理：普通错误+权限错误-->统一下沉集中处理
//	无权限error：（1）无角色，（2）角色无权限，（3）角色有权限但是无可见数据
//
// 2、返回可见数据：可见范围+具体可见客户列表

// func (s *PermissionService) CheckCustomerViewablePermission(ctx context.Context) (int, []string, error) {
// 	logger := s.logger.WithFunc("CheckCustomerViewablePermission")

// 	// 1、都是按用户维度校验
// 	userEmail := utils.CtxGetEmployeeEmail(ctx)
// 	if len(userEmail) == 0 {
// 		return constants.ViewRangeType_No, nil, errorcode.ErrDataNotFound.WithArgs("email")
// 	}
// 	// 获取用户的所有角色
// 	userRoleList, err := eps_permission.GetEpsPermissionService().GetUserRoles(ctx, userEmail)
// 	if err != nil {
// 		logger.CtxError(ctx, "eps_permission GetUserRoles err:%v", err)
// 		return constants.ViewRangeType_No, nil, errorcode.ErrUpStreamSystemError.WithArgs("Eps permission")
// 	}
// 	if len(userRoleList) == 0 {
// 		return constants.ViewRangeType_No, nil, errorcode.ErrNoAuthentication
// 	}

// 	var viewRangeType int
// 	viewableCustomerNumberList := make([]string, 0)
// 	var productViewableCustomerNumberList []string
// 	eg, _ := errgroup.WithContext(ctx)
// 	eg.Go(func() error {
// 		var err error
// 		viewRangeType, viewableCustomerNumberList, err = s.GetViewableCustomerList(ctx, userRoleList)
// 		if err != nil {
// 			logger.CtxError(ctx, "GetViewableCustomerList err:%v", err)
// 			return err
// 		}
// 		return nil
// 	})
// 	eg.Go(func() error {
// 		var err error
// 		productViewableCustomerNumberList, err = s.GetProductRelatedViewableCustomerList(ctx, userRoleList)
// 		if err != nil {
// 			logger.CtxError(ctx, "GetProductRelatedViewableCustomerList err:%v", err)
// 			return err
// 		}
// 		return nil
// 	})
// 	if err := eg.Wait(); err != nil {
// 		logger.CtxError(ctx, "eg.Wait():%v", err)
// 		return constants.ViewRangeType_No, nil, err
// 	}

// 	if constants.ViewRangeType_All == viewRangeType {
// 		return constants.ViewRangeType_All, nil, nil
// 	}
// 	if constants.ViewRangeType_No == viewRangeType {
// 		if len(productViewableCustomerNumberList) == 0 {
// 			return constants.ViewRangeType_No, nil, nil
// 		} else {
// 			return constants.ViewRangeType_Part, productViewableCustomerNumberList, nil
// 		}
// 	}

// 	if len(productViewableCustomerNumberList) > 0 {
// 		viewableCustomerNumberList = append(viewableCustomerNumberList, productViewableCustomerNumberList...)
// 	}

// 	viewableCustomerNumberList = slicex.Distinct(viewableCustomerNumberList)
// 	if len(viewableCustomerNumberList) == 0 {
// 		return constants.ViewRangeType_No, nil, nil
// 	}

// 	return viewRangeType, viewableCustomerNumberList, nil
// }

// func (s *PermissionService) CheckSingleCustomerViewable(ctx context.Context, customerNumber string) (bool, error) {
// 	if len(customerNumber) == 0 {
// 		return false, errorcode.ErrRequestParamInvalid.WithArgs("customerNumber")
// 	}

// 	viewRangeType, viewableCustomerNumberList, err := s.CheckCustomerViewablePermission(ctx)
// 	if err != nil {
// 		return false, err
// 	}

// 	if viewRangeType == constants.ViewRangeType_No {
// 		return false, nil
// 	}
// 	if viewRangeType == constants.ViewRangeType_All {
// 		return true, nil
// 	}
// 	if viewRangeType == constants.ViewRangeType_Part {
// 		if !slicex.Contains(viewableCustomerNumberList, customerNumber) {
// 			return false, nil
// 		}
// 		return true, nil
// 	}

// 	return false, nil
// }

// func (s *PermissionService) GetViewableCustomerList(ctx context.Context, userRoleList []string) (int, []string, error) {
// 	logger := s.logger.WithFunc("GetProductRelatedViewableCustomerList")
// 	userEmail := utils.CtxGetEmployeeEmail(ctx)

// 	var viewRangeType int

// 	permissionConfig, err := tcc.GetPermissionConfig(ctx)
// 	if err != nil {
// 		logger.CtxError(ctx, "tcc GetPermissionConfig err:%v", err)
// 		return constants.ViewRangeType_No, nil, errorcode.ErrUpStreamSystemError.WithArgs("TCC")
// 	}
// 	// 2、角色判断
// 	var hasAllViewRange, hasPartViewRange, hasNoViewRange bool
// 	for _, role := range userRoleList {
// 		if slicex.Contains(permissionConfig.CustomerAllViewRangeRoleList, role) {
// 			hasAllViewRange = true
// 		}
// 		if slicex.Contains(permissionConfig.CustomerPartViewRangeRoleList, role) {
// 			hasPartViewRange = true
// 		}
// 		if slicex.Contains(permissionConfig.CustomerNoViewRangeRoleList, role) {
// 			hasNoViewRange = true
// 		}
// 	}
// 	// 3、优先级判断：按用户的最高权限
// 	if hasAllViewRange {
// 		return constants.ViewRangeType_All, nil, nil
// 	} else if hasPartViewRange {
// 		viewRangeType = constants.ViewRangeType_Part
// 	} else if hasNoViewRange {
// 		return constants.ViewRangeType_No, nil, nil
// 	} else {
// 		viewRangeType = constants.ViewRangeType_Part
// 	}

// 	// 4、部分可见范围，根据客户的服务团队判断用户的可见客户列表
// 	// 用户角色：销售+CSM
// 	viewableCustomerNumberList := make([]string, 0)
// 	var csmViewableCustomerNumberList, csmLeaderViewableCustomerNumberList, salesViewableCustomerNumberList []string
// 	eg, _ := errgroup.WithContext(ctx)
// 	eg.Go(func() error {
// 		if slicex.Contains(userRoleList, constants.RoleKey_CSM) {
// 			serviceTeamList, err := dal.FindAccountServiceTeamByConditions(ctx,
// 				[]gen.Condition{
// 					rds.QueryLdi.AccountServiceTeam.Email.Eq(userEmail),
// 					rds.QueryLdi.AccountServiceTeam.Role.Eq(constants.CustomerServiceTeamRole_CSM),
// 				},
// 			)
// 			if err != nil {
// 				logger.CtxError(ctx, "FindAccountServiceTeamByConditions err:%v", err)
// 				return errorcode.ErrDatabaseError.WithArgs(constants.DBName_Ldi)
// 			}

// 			csmViewableCustomerNumberList = slicex.Map(serviceTeamList, func(serviceTeam *ldi_model.AccountServiceTeam) string {
// 				return serviceTeam.AccountNumber
// 			})
// 		}
// 		return nil
// 	})
// 	eg.Go(func() error {
// 		if slicex.Contains(userRoleList, constants.RoleKey_CSMLeader) {
// 			organizationNumberList := permissionConfig.OrganizationNumberList
// 			if len(organizationNumberList) == 0 {
// 				return nil
// 			}
// 			// 查询CSM leader下挂的CSM所有可见用户
// 			organizations, err := organization.DefaultInstance.BatchGetOrganization(ctx, &organization.BatchGetOrganizationReq{
// 				OrganizationNumber: organizationNumberList,
// 				WithEmployee:       true,
// 			})
// 			if err != nil {
// 				logger.CtxError(ctx, "BatchGetOrganization err:%v", err)
// 				return errorcode.ErrUpStreamSystemError.WithArgs("Organization")
// 			}
// 			if len(organizations) == 0 {
// 				logger.CtxInfo(ctx, "BatchGetOrganization organizations is blank")
// 				return nil
// 			}
// 			// 获取当前用户是+1及其下属的CSM，取树的最底层的CSM
// 			allCsmEmailList := make([]string, 0)
// 			funk.ForEach(organizations, func(org *organization.Organization) {
// 				leaderOrganizationNode := organization.LoopGetLeaderOrganizationNode(ctx, org)
// 				if leaderOrganizationNode != nil {
// 					organizationEmailList := organization.LoopGetOrganizationEmailList(ctx, leaderOrganizationNode)
// 					if len(organizationEmailList) != 0 {
// 						allCsmEmailList = append(allCsmEmailList, organizationEmailList...)
// 					}
// 				}
// 			})
// 			if len(allCsmEmailList) == 0 {
// 				logger.CtxInfo(ctx, "LoopGetOrganizationEmailList organizations is blank")
// 				return nil
// 			}

// 			serviceTeamList, err := dal.FindAccountServiceTeamByConditions(ctx,
// 				[]gen.Condition{
// 					rds.QueryLdi.AccountServiceTeam.Email.In(allCsmEmailList...),
// 					rds.QueryLdi.AccountServiceTeam.Role.Eq(constants.CustomerServiceTeamRole_CSM),
// 				},
// 			)
// 			if err != nil {
// 				logger.CtxError(ctx, "FindAccountServiceTeamByConditions err:%v", err)
// 				return errorcode.ErrDatabaseError.WithArgs(constants.DBName_Ldi)
// 			}

// 			csmLeaderViewableCustomerNumberList = slicex.Map(serviceTeamList, func(serviceTeam *ldi_model.AccountServiceTeam) string {
// 				return serviceTeam.AccountNumber
// 			})
// 		}
// 		return nil
// 	})
// 	eg.Go(func() error {
// 		if slicex.Contains(userRoleList, constants.RoleKey_EpsSales) {
// 			customerList, err := dal.FindCustomerByConditions(ctx,
// 				[]gen.Condition{
// 					rds.QuerySignalFire.Customer.OwnerEmail.Eq(userEmail),
// 				},
// 			)
// 			if err != nil {
// 				logger.CtxError(ctx, "FindCustomerByConditions err:%v", err)
// 				return errorcode.ErrDatabaseError.WithArgs(constants.DBName_SignalFire)
// 			}
// 			if len(customerList) == 0 {
// 				logger.CtxInfo(ctx, "FindCustomerByConditions customerList is blank")
// 				return nil
// 			}
// 			salesViewableCustomerNumberList = slicex.Map(customerList, func(customer *model.Customer) string {
// 				return customer.CustomerNumber
// 			})
// 		}
// 		return nil
// 	})
// 	if err := eg.Wait(); err != nil {
// 		logger.CtxError(ctx, "eg.Wait():%v", err)
// 		return constants.ViewRangeType_No, nil, err
// 	}

// 	viewableCustomerNumberList = append(csmViewableCustomerNumberList, salesViewableCustomerNumberList...)
// 	viewableCustomerNumberList = append(viewableCustomerNumberList, csmLeaderViewableCustomerNumberList...)
// 	viewableCustomerNumberList = slicex.Distinct(viewableCustomerNumberList)

// 	return viewRangeType, viewableCustomerNumberList, nil
// }

// func (s *PermissionService) GetMenuPermissionConfig(ctx context.Context) (*permission.GetMenuPermissionConfigResult, error) {
// 	logger := s.logger.WithFunc("GetMenuPermissionConfig")
// 	permissionConfig, err := tcc.GetPermissionConfig(ctx)
// 	if err != nil {
// 		logger.CtxError(ctx, "GetPermissionConfig err:%v", err)
// 		return nil, errorcode.ErrUpStreamSystemError.WithArgs("TCC")
// 	}
// 	if permissionConfig == nil || len(permissionConfig.MenuPermissionList) == 0 {
// 		return &permission.GetMenuPermissionConfigResult{}, nil
// 	}
// 	return &permission.GetMenuPermissionConfigResult{
// 		List: permissionConfig.MenuPermissionList,
// 	}, nil
// }

func (s *PermissionService) GetRoleMembers(ctx context.Context, roles []string) ([]string, error) {
	logger := s.logger.WithFunc("GetRoleUsers")

	m := cmap.New[[]string]()
	permissionService := eps_permission.GetEpsPermissionService()
	eg, _ := errgroup.WithContext(ctx)
	for _, role := range roles {
		tmpRole := role
		eg.Go(func() error {
			defer utils.AsyncRecover(ctx)

			roleMembers, err := permissionService.GetRoleMembers(ctx, tmpRole)
			if err != nil {
				logger.CtxError(ctx, "GetRoleMembers err:%v, roleKey:%s", err, tmpRole)
				return err
			}
			m.Set(tmpRole, roleMembers)
			return nil
		})
	}

	err := eg.Wait()
	if err != nil {
		logger.CtxError(ctx, "Batch GetRoleMembers err:%v", err)
		return nil, err
	}

	set := setx.NewHashSetWithCap[string](0)
	for _, val := range m.Items() {
		set.AddAll(val)
	}
	return set.ToSlice(), nil
}

func (s *PermissionService) AddUserRole(ctx context.Context, email string, roleKey string, expireAt int64) error {
	logger := s.logger.WithFunc("AddUserRole")
	err := eps_permission.GetEpsPermissionService().AddUserRoles(ctx, email, roleKey, expireAt)
	if err != nil {
		logger.CtxError(ctx, "eps_permission AddUserRole err:%v", err)
		return err
	}
	return nil
}

func (s *PermissionService) GetResources(ctx context.Context, Resources []string) ([]*permission.GetResourcesResp, error) {
	logger := s.logger.WithFunc("GetResources")

	if result, err := eps_permission.GetEpsPermissionService().GetResources(ctx, Resources); err != nil {
		logger.CtxError(ctx, "eps_permission GetResources err:%v", err)
		return nil, err
	} else {
		return result, nil
	}
}
