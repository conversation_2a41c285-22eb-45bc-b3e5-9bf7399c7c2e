package permission

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	permissionHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
)

// CheckUserPermission  鉴权
func CheckUserPermission(ctx context.Context, c *app.RequestContext) error {

	req := &permissionHertz.CheckUserPermissionReq{}

	if err := binding.BindAndValidate(c, req); err != nil {
		return err
	}

	email := utils.CtxGetEmployeeEmail(ctx)

	if len(email) == 0 {
		return errorcode.ErrRequestParamInvalid.WithArgs("email")
	}

	if len(req.Rules) == 0 {
		return errorcode.ErrRequestParamInvalid.WithArgs("rules")
	}

	if rsp, err := GetPermissionService().CheckPermissions(ctx, email, req.Rules); err != nil {
		return err
	} else {
		for _, result := range rsp {
			for _, flag := range result.ActionAccessMap {
				if !flag {
					return errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
				}
			}
		}
	}

	return nil
}
