package feedback

import (
	"context"
	"errors"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

var (
	service *Service
	once    sync.Once
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetFeedbackService() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("FeedbackService"),
		}
	})
	return service
}

func (s *Service) SubmitDiagnoseTaskFeedback(ctx context.Context, feedback feedback.SubmitDiagnoseTaskFeedbackReq) error {
	logger := s.logger.WithFunc("SubmitDiagnoseTaskFeedback")
	// 2、校验是否已经提交过
	feedbackRecord, err := dal.FeedbackFindByDiagnoseTaskRunID(ctx, feedback.DiagnoseTaskRunID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.CtxError(ctx, "FeedbackFindByDiagnoseTaskRunID err:%v", err)
		return err
	}
	if feedbackRecord != nil {
		logger.CtxWarn(ctx, "FeedbackRecord already exist")
		return errorcode.ErrDataAlreadyExist
	}
	// 4、保存反馈记录
	problemItems, err := TransferProblemItemsToString(feedback.ProblemItems)
	if err != nil {
		logger.CtxError(ctx, "TransferStringToProblemItems err:%v", err)
		return err
	}
	// 新增反馈时间字段，赋值为当前时间
	feedbackTime := time.Now()
	feedbackRecord = &model.Feedback{
		DiagnoseTaskID:    feedback.DiagnoseTaskID,
		DiagnoseTaskRunID: feedback.DiagnoseTaskRunID,
		Resolved:          feedback.Resolved,
		ProblemItems:      problemItems,
		FeedbackUserID:    feedback.FeedbackUserID,
		Description:       feedback.Description,
		// 添加反馈时间字段到结构体
		FeedbackTime: lo.ToPtr(feedbackTime),
	}
	//通过任务ID查找任务创建人
	task, err := dal.QueryDiagnoseTaskRunByID(ctx, feedback.DiagnoseTaskRunID)
	if err != nil {
		logger.CtxError(ctx, "QueryDiagnoseTaskRunByID err:%v", err)
		return err
	}
	feedbackRecord.DiagnoseRunUserID = task.CreateUserID
	err = dal.CreateFeedback(ctx, feedbackRecord)
	if err != nil {
		logger.CtxError(ctx, "FeedbackSave err:%v", err)
		return err
	}
	err = dal.DiagnoseTaskRunUpdateFeedbackIDByID(ctx, feedback.DiagnoseTaskRunID, lo.ToPtr(feedbackRecord.ID))
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTaskUpdate err:%v", err)
		return err
	}
	return nil
}
