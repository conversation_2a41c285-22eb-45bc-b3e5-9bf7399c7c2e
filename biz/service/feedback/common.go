package feedback

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	"encoding/json"
	"github.com/samber/lo"
)

// TransferProblemItemsToString 转换ProblemItems为string
func TransferProblemItemsToString(problemItems []*feedback.ProblemItem) (*string, error) {
	if len(problemItems) == 0 {
		return nil, nil
	}
	problemItemsByte, err := json.Marshal(problemItems)
	if err != nil {
		return nil, err
	}
	return lo.ToPtr(string(problemItemsByte)), nil
}

// TransferStringToProblemItems 转换string为ProblemItems
func TransferStringToProblemItems(problemItems *string) ([]*feedback.ProblemItem, error) {
	if problemItems == nil {
		return nil, nil
	}
	problemItemsList := make([]*feedback.ProblemItem, 0)
	err := json.Unmarshal([]byte(*problemItems), &problemItemsList)
	if err != nil {
		return nil, err
	}
	return problemItemsList, nil
}
