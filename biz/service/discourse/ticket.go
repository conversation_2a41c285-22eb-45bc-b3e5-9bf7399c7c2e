package discourse

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/kv/redis-v6"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	resourceHandler "code.byted.org/volcengine-support/cloud-sherlock/biz/handler/describe_resource"
	diagnoseTaskRunSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	diagnoseTaskV2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/openapi"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"errors"
	"github.com/samber/lo"
	"regexp"
	"strings"
)

var thoughtRegex = regexp.MustCompile(`"event": "agent_thought",`)

func (s *Service) TicketSummary(c context.Context, req *openapi.TicketSummaryReq) (*openapi.TicketResp, error) {
	logger := s.logger.WithFunc("TicketSummary")

	// 直接创建 session
	sessionID, err := CreateSession(c, TCC_KEY_TICKET_INTENTION, "ticket", map[string]string{
		"user_id":     "ticket",
		"department":  "官网",
		"product":     req.SubProductID,
		"ticket_id":   req.TicketID,
		"ticket_info": utils.JsonToString(req.Data),
	})

	if err != nil {
		return nil, err
	}

	// 由于只转发一个包，所以需要额外处理

	ten := make(chan []byte, 1)
	errs := make(chan struct{}, 1)

	defer func() {
		close(ten)
		close(errs)
	}()

	go func() {
		ChatV2Stream(c, req.Data, sessionID, "ticket", TCC_KEY_TICKET_INTENTION, ten, errs, thoughtRegex)
		go func() {
			s.Task(c, req, sessionID)
		}()
	}()

	for {
		select {

		case <-errs:
			return nil, errors.New("模型生成失败，请稍候再试")

		case data := <-ten:
			tenMsg := &discourseHertz.ThoughtMsg{}
			if err := utils.JsonUnmarshal(data, tenMsg); err != nil {
				logger.CtxError(c, "JsonUnmarshal error:%v", err)
				return nil, err
			}

			// 转为结构体
			return &openapi.TicketResp{
				Choices: &[]openapi.DoChoices{{
					FinishReason: "stop",
					Index:        0,
					Message: openapi.DoSubMessage{
						Content:          tenMsg.Thought,
						Role:             "assistant",
						ReasoningContent: nil,
					},
				}},
				Created:   tenMsg.CreatedAt,
				ID:        tenMsg.ID,
				SessionID: sessionID,
			}, nil
		}
	}
}

func (s *Service) TicketReport(c context.Context, req *openapi.TicketReportReq) *openapi.TicketResp {
	logger := s.logger.WithFunc("TicketReport")

	obj := &openapi.TicketRedisObj{}

	report := &openapi.TicketResp{}

	if err := rds_redis.GetObj(req.SessionID, obj); err != nil {
		if errors.Is(err, redis.Nil) {
			report.Status = "running"
			goto Return
		} else {
			logs.CtxError(c, "GetObj err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}
	}

	if obj.Status == "error" {
		logger.CtxError(c, "GetObj err:%v", obj)
		{
			report.Status = "error"
			goto Return
		}
	}

	// 主动触发中台诊断
	if obj.TaskRunID != 0 {

		status, err := dal.QueryDiagnoseTaskRunStatusByID(c, &obj.TaskRunID)

		if err != nil {
			logger.CtxError(c, "QueryDiagnoseTaskRunStatusByID err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}

		if status != "finish" {
			report.Status = "running"
			goto Return
		}

		// 主动触发
		taskDetail, err := s.queryDiagnoseTaskRunDetailBot(c,
			&diagnose_task_run.QueryDiagnoseTaskRunDetailReq{
				DiagnoseTaskRunID: obj.TaskRunID,
				Action:            "",
			},
		)

		if err != nil {
			logger.CtxError(c, "QueryDiagnoseTaskRunDetail err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}

		summaryReq := &discourseHertz.SummaryReq{
			Information: taskDetail.Information,
		}

		for key := range taskDetail.DiagnoseTaskRunByteFlow.DiagnoseItemStatusList {
			itemd, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItemDetail(
				c, &diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq{
					DiagnoseTaskRunID: obj.TaskRunID,
					DiagnoseItemID:    key,
					DiagnoseResultLevel: []string{
						"critical",
						"error",
						"warning",
						"failed",
						"info",
					},
					PageSize:   lo.ToPtr(int32(999)),
					PageNumber: lo.ToPtr(int32(1)),
				},
			)
			if err != nil {
				logger.CtxError(c, "QueryDiagnoseTaskRunItemDetail err:%v", err)
				{
					report.Status = "error"
					goto Return
				}
			}
			summaryReq.Items = append(summaryReq.Items, itemd)
		}

		summaryReqStr := doubleEncodeJson(summaryReq)

		// 创建 session
		summary, err := CreateSession(c, TCC_KEY_SUMMARY, "ticket", map[string]interface{}{"user_id": "ticket", "department": "官网", "report": summaryReqStr})

		rsp := ChatV2Block(c, "请调用【智能总结】工作流", summary, "ticket", TCC_KEY_SUMMARY)

		if rsp == "" {
			logger.CtxError(c, "ChatV2Block err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}

		dReport := &discourseHertz.AnswerInfo{}
		if err := utils.JsonUnmarshalString(rsp, dReport); err != nil {
			logger.CtxError(c, "JsonUnmarshalString err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}

		{
			data, err := summary2MD(dReport.Answer.(string))
			if err != nil {
				logger.CtxError(c, "summary2MD err:%v", err)
				{
					report.Status = "error"
					goto Return
				}
			}
			report.Status = "finish"
			report.Choices = &[]openapi.DoChoices{{
				FinishReason: "stop",
				Index:        0,
				Message: openapi.DoSubMessage{
					Content:          data,
					Role:             "assistant",
					ReasoningContent: nil,
				},
			}}
			report.Created = dReport.CreatedTime
			report.ID = dReport.ID
		}
	} else if obj.Status == "finish" {
		data, err := summary2MD(obj.AnswerInfo.Answer.(string))
		if err != nil {
			logger.CtxError(c, "summary2MD err:%v", err)
			{
				report.Status = "error"
				goto Return
			}
		}
		report.Status = "finish"
		report.Choices = &[]openapi.DoChoices{{
			FinishReason: "stop",
			Index:        0,
			Message: openapi.DoSubMessage{
				Content:          data,
				Role:             "assistant",
				ReasoningContent: nil,
			},
		}}
		report.Created = obj.AnswerInfo.CreatedTime
	} else {
		report.Status = "running"
	}

Return:
	return report
}

func (s *Service) Task(ctx context.Context, req *openapi.TicketSummaryReq, intention string) {

	logger := s.logger.WithFunc("task")

	obj := &openapi.TicketRedisObj{}

	var info *discourseHertz.AnswerInfo
	var msg string
	var messages *discourseHertz.SSEDetail

	apikey, err := getApiKey(TCC_KEY_INTENTION)

	if err != nil {
		logs.CtxError(ctx, "get apikey error")
		obj.Status = "error"
		goto Store
	}

	// 查询数据
	messages, err = GetConversationMessages(ctx, apikey, intention, "<EMAIL>")

	if err != nil || messages == nil || len(messages.Messages) == 0 {
		logger.CtxError(ctx, "GetConversationMessages err:%v", err)
		obj.Status = "error"
		goto Store
	}

	info = messages.Messages[0].AnswerInfo
	msg = info.Answer.(string)

	// 发起诊断
	if tagRegex.MatchString(msg) {

		msg = strings.Replace(msg, "tag\n", "", 1)
		form, err := s.str2TicketInfo(ctx, msg)

		if err != nil {
			logger.CtxError(ctx, "str2TicketInfo err:%v", err)
			{
				obj.Status = "error"
				goto Store
			}
		}

		// 中台诊断
		if form.Id > 0 {
			ticket, err := ticket2Diagnose(ctx, form)
			if err != nil {
				logger.CtxError(ctx, "ticket2Diagnose err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}

			// 只检测资源 id是否异常
			product, err := dal.QueryProductCategoryByID(ctx, ticket.ProductCategoryIDs[0])
			if err != nil {
				logger.CtxError(ctx, "QueryProductCategoryByID err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}

			body := describe_resource.DescribeResourceRequest{
				Dimension: "instance",
				Resources: &describe_resource.Resources{
					ResourceType: product.ResourceType,
					Product:      product.Product,
					SubProduct:   product.SubProduct,
					Instances:    ticket.DiagnoseResources[0].Instances,
				},
			}

			// 创建一个app.RequestContext
			resourceCtx := app.NewContext(1)
			resourceCtx.Request.SetBodyString(utils.JsonToString(body))

			if err = resourceHandler.NewResourceHandlerFactory().HandlerResource(ctx, resourceCtx); err != nil {
				// 现在只有实例 iD不对的情况下直接走保底问答
				s.logger.CtxError(ctx, "NewResourceHandler err:%v Goto BackStop", err)

				{
					// 直接创建 session
					backStop, err := CreateSession(ctx, TCC_KEY_TICKET_INTENTION, req.TicketID, map[string]string{
						"user_id":     "ticket",
						"department":  "官网",
						"product":     req.SubProductID,
						"ticket_id":   req.TicketID,
						"ticket_info": utils.JsonToString(req.Data),
						"is_qa":       "问答",
					})
					if err != nil || backStop == "" {
						logger.CtxError(ctx, "CreateSession err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					sMsg := ChatV2Block(ctx, req.Data, backStop, "ticket", TCC_KEY_TICKET_INTENTION)
					if sMsg == "" {
						logger.CtxError(ctx, "ChatV2Block err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					dReport := &discourseHertz.AnswerInfo{}
					if err := utils.JsonUnmarshalString(sMsg, dReport); err != nil {
						logger.CtxError(ctx, "JsonUnmarshalString err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					obj.AnswerInfo = dReport
				}
				goto Store

			} else if _, ok := resourceCtx.Keys["__has_error__"]; ok {
				s.logger.CtxError(ctx, "NewResourceHandler err:%v Goto BackStop", err)
				{
					// 直接创建 session
					backStop, err := CreateSession(ctx, TCC_KEY_TICKET_INTENTION, req.TicketID, map[string]string{
						"user_id":     "ticket",
						"department":  "官网",
						"product":     req.SubProductID,
						"ticket_id":   req.TicketID,
						"ticket_info": utils.JsonToString(req.Data),
						"is_qa":       "问答",
					})
					if err != nil || backStop == "" {
						logger.CtxError(ctx, "CreateSession err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					sMsg := ChatV2Block(ctx, req.Data, backStop, "ticket", TCC_KEY_TICKET_INTENTION)
					if sMsg == "" {
						logger.CtxError(ctx, "ChatV2Block err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					dReport := &discourseHertz.AnswerInfo{}
					if err := utils.JsonUnmarshalString(sMsg, dReport); err != nil {
						logger.CtxError(ctx, "JsonUnmarshalString err:%v", err)
						{
							obj.Status = "error"
							goto Store
						}
					}
					obj.AnswerInfo = dReport
				}
				goto Store
			}

			createDiagnoseTaskV2Req := &diagnose_task_v2.CreateDiagnoseTaskV2Req{
				Name:               req.TicketID,
				DiagnoseTemplateID: ticket.DiagnoseTemplateID,
				DiagnoseItemIDs:    ticket.DiagnoseItemIDs,
				CreateUserID:       ticket.CreateUserID,
				ProductCategoryIDs: ticket.ProductCategoryIDs,
				Dimension:          ticket.Dimension,
				TicketID:           lo.ToPtr(req.TicketID),
				DiagnoseType:       ticket.DiagnoseType,
				Origin:             lo.ToPtr(diagnoseTaskV2Svc.DiagnoseTaskOriginBot),
			}

			// 创建诊断任务
			diagnoseTaskV2ID, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().CreateDiagnoseTask(ctx, *createDiagnoseTaskV2Req)

			if err != nil {
				logger.CtxError(ctx, "CreateDiagnoseTaskV2 err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}

			//RunDiagnoseTask
			runDiagnoseTaskReq := &diagnose_task_run.RunDiagnoseTaskReq{
				DiagnoseTaskID:    diagnoseTaskV2ID,
				RunUserID:         "inner",
				DiagnoseResources: ticket.DiagnoseResources,
				DiagnoseStartTime: ticket.DiagnoseStartTime,
				DiagnoseEndTime:   ticket.DiagnoseEndTime,
				Name:              lo.ToPtr(req.TicketID),
				AccountID:         ticket.AccountID,
			}

			// 执行诊断任务
			result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().RunDiagnoseTask(ctx, runDiagnoseTaskReq)

			if err != nil {
				logger.CtxError(ctx, "CreateDiagnoseTask err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}

			// 更新到 redis中
			obj.TaskRunID = result.DiagnoseTaskRunID
			goto Store

			//自建诊断
		} else {
			ticket, err := ticket2ADiagnose(form)
			if err != nil {
				logger.CtxError(ctx, "ticket2ADiagnose err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			diagnose, err := CreateSession(ctx, TCC_KEY_DIAGNOSE, "ticket", map[string]string{"user_id": "ticket", "department": "官网"})
			if err != nil || diagnose == "" {
				logger.CtxError(ctx, "CreateSession err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			dMsg := ChatV2Block(ctx, ticket, diagnose, "ticket", TCC_KEY_DIAGNOSE)
			if dMsg == "" {
				logger.CtxError(ctx, "ChatV2Block err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			info := &discourseHertz.SSEMessage{}
			if err := utils.JsonUnmarshalString(dMsg, info); err != nil {
				logger.CtxError(ctx, "JsonUnmarshalString err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			// 总结
			summary, err := CreateSession(ctx, TCC_KEY_SUMMARY, "ticket", map[string]string{"user_id": "ticket", "department": "官网", "report": info.Answer.(string)})
			// 发起总结
			if err != nil || summary == "" {
				logger.CtxError(ctx, "CreateSession err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			sMsg := ChatV2Block(ctx, "请调用【智能总结】工作流", summary, "ticket", TCC_KEY_SUMMARY)
			if sMsg == "" {
				logger.CtxError(ctx, "ChatV2Block err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			dReport := &discourseHertz.AnswerInfo{}
			if err := utils.JsonUnmarshalString(sMsg, dReport); err != nil {
				logger.CtxError(ctx, "JsonUnmarshalString err:%v", err)
				{
					obj.Status = "error"
					goto Store
				}
			}
			obj.AnswerInfo = dReport
			obj.Status = "finish"
			goto Store
		}
	} else {
		// 对话流，无诊断过程，直接包装返回
		obj.AnswerInfo = info
		obj.Status = "finish"
	}

Store:
	if err = rds_redis.StoreObj(intention, obj, TICKET_TTL); err != nil {
		logs.CtxError(ctx, "StoreObj err:%v", err)
	}
}
