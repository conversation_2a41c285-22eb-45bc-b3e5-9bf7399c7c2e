package discourse

import (
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/byted/consts"
	"code.byted.org/middleware/hertz/pkg/protocol"
	discourseHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
	"errors"
	"fmt"
	"github.com/hertz-contrib/sse"
	"github.com/samber/lo"
	"regexp"
	"strconv"
	"strings"
	"time"
)

type UpdateVarReq struct {
	AppConversationID string      `json:"AppConversationID"`
	Inputs            interface{} `json:"Inputs"`
	UserID            string      `json:"UserID"`
}

type QueryReq struct {
	Query             string      `json:"Query"`
	AppConversationID string      `json:"AppConversationID"`
	ResponseMode      string      `json:"ResponseMode"`
	UserID            string      `json:"UserID"`
	QueryExtends      interface{} `json:"QueryExtends"`
}

type GetConversationsReq struct {
	AppConversationID string `json:"AppConversationID"`
	UserID            string `json:"UserID"`
	Limit             int    `json:"Limit"`
}

type GetConversationReq struct {
	MessageID string `json:"MessageID"`
	UserID    string `json:"UserID"`
}

type QueryStruct struct {
	SseCtx     context.Context `json:"SseCtx"`
	Query      interface{}     `json:"Query"`
	Session    string          `json:"Session"`
	UserId     string          `json:"UserId"`
	SessionKey string          `json:"SessionKey"`
	Data       *chan string    `json:"Data"`
	Stream     *sse.Stream     `json:"Stream"`
	IsBlocking bool            `json:"IsBlocking"`
	NeedBlock  bool            `json:"NeedBlock"`
}

var (
	BLOCK  = "blocking"
	STREAM = "streaming"
)

// CreateSession 创建 session
func CreateSession(c context.Context, key string, userId string, inputs interface{}) (string, error) {

	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/CreateSession] GetHttpClient err:%v", err)
		return "", err
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	apikey, err := getApiKey(key)

	if err != nil {
		return "", err
	}

	hreq := &protocol.Request{}
	hreq.SetHeader("apikey", apikey)
	input := struct {
		Inputs interface{}
		UserID string
	}{
		inputs,
		userId,
	}

	{
		hreq.SetBody([]byte(utils.JsonToString(input)))
		hreq.SetHeader("Content-Type", "application/json")
		hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/create_conversation")
		hreq.SetMethod("POST")
	}

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &discourseHertz.CreateSessionResp{}
		err = utils.JsonUnmarshal(resp.Body(), msg)
		if err != nil {
			return "", err
		}
		return msg.Conversation.AppConversationID, nil
	} else {
		return "", errors.New("create session fail")
	}
}

func UpdateAgentVar(c context.Context, session, sessionKey, userId string, updateVar interface{}) error {
	hcli, err := GetHttpClient()
	if err != nil {
		logs.CtxError(c, "[discourse/UpdateAgentVar] GetHttpClient err:%v", err)
		return err
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	up := &UpdateVarReq{
		AppConversationID: session,
		Inputs:            updateVar,
		UserID:            userId,
	}

	body, err := utils.JsonMarshalToBytes(up)

	if err != nil {
		return err
	}

	hreq := &protocol.Request{}

	apikey, err := getApiKey(sessionKey)
	if err != nil {
		return err
	}

	{
		hreq.SetHeader("apikey", apikey)
		hreq.SetHeader("Content-Type", "application/json")
		hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
		hreq.SetBody(body)
		hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/update_conversation")
		hreq.SetMethod("POST")
	}

	resp := &protocol.Response{}
	if err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout); err != nil {
		return err
	}

	if resp.StatusCode() != 200 {
		return errors.New("update agent var failed")
	}
	return nil
}

// ChatV2 对话流程
func ChatV2(ctx, sseCtx context.Context,
	query interface{},
	session, userId, sessionKey string,
	data chan string,
	stream *sse.Stream,
	isBlocking bool,
	needBlock bool,
) string {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		sendErrorEvent(stream, "网络链接错误", err)
		return ""
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	start := time.Now()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))
	if err != nil {
		sendErrorEvent(stream, "网络链接错误", err)
		return ""
	}

	apikey, err := getApiKey(sessionKey)

	if err != nil {
		sendErrorEvent(stream, "网络链接错误", err)
		return ""
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		sendErrorEvent(stream, "连接关闭，请稍候再试", ctx.Err())
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: session,
			ResponseMode:      lo.Ternary(isBlocking, BLOCK, STREAM),
			UserID:            userId,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))
		// 使用JSON序列化来安全地构造请求体
		requestBody, err := utils.JsonMarshalToBytes(queryReq)
		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			sendErrorEvent(stream, "请求构造错误", err)
			return ""
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetBodyRaw(requestBody)
		}
	}

	if isBlocking {
		resp := protocol.Response{}
		err = hcli.DoTimeout(ctx, hreq, &resp, DefaultTimeout)
		if err != nil || resp.StatusCode() != 200 || resp.Body() == nil || len(resp.Body()) == 0 {
			sendErrorEvent(stream, "网络链接错误", err)
			return ""
		}
		return string(resp.Body())
	} else {
		hreq.SetHeader("Connection", "keep-alive")
		hreq.SetHeader("Accept", "text/event-stream")
		dataMsg := strings.Builder{}
		first := true
		if err = cli.SubscribeWithContext(sseCtx, func(msg *sse.Event) {
			if msg.Data != nil {
				resp := string(msg.Data)
				// 需要拦截所有 message数据
				if needBlock && strings.HasPrefix(resp, "{\"event\": \"message\"") {
					ssem := &discourseHertz.SSEMessage{}
					err = utils.JsonUnmarshal(msg.Data, ssem)
					if err != nil {
						logs.CtxError(ctx, "ChatV2 JsonUnmarshal err:%v", err)
					}
					dataMsg.WriteString(ssem.Answer.(string))
					// 只拦截首个 message且开头为 tag的数据
				} else if first && strings.HasPrefix(resp, "{\"event\": \"message\"") {
					first = false
					ssem := &discourseHertz.SSEMessage{}
					err = utils.JsonUnmarshal(msg.Data, ssem)
					if err != nil {
						logs.CtxError(ctx, "ChatV2 JsonUnmarshal err:%v", err)
					}
					if strings.HasPrefix(ssem.Answer.(string), "tag\n") {
						ssem.Answer = strings.Replace(ssem.Answer.(string), "tag\n", "", 1)
						dataMsg.WriteString(utils.JsonToString(ssem))
						return // 不能转发这个包
					}
				}
				if err = stream.Publish(msg); err != nil {
					logs.CtxError(ctx, "ChatV2 Info %s Publish err:%v", msg, err)
				}
			}
		}, sse.WithRequest(hreq)); err != nil {
			sendErrorEvent(stream, "网络链接错误", err)
		} else if dataMsg.Len() > 0 {
			select {
			case data <- dataMsg.String():
			default:
				return ""
			}
		}
	}
	logs.CtxInfo(ctx, "hiagent discourse cost time: %v", time.Now().Sub(start))
	return ""
}

func ChatV2Stream(ctx context.Context,
	query interface{},
	session, userId, sessionKey string,
	data chan []byte,
	errs chan struct{},
	regex *regexp.Regexp,
) {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		select {
		case errs <- struct{}{}:
		default:
		}
		return
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))

	if err != nil {
		logs.CtxError(ctx, "ChatV2 NewClientWithOptions err:%v", err)
		select {
		case errs <- struct{}{}:
		default:
		}
		return
	}

	apikey, err := getApiKey(sessionKey)

	if err != nil {
		logs.CtxError(ctx, "ChatV2Stream GetApiKey err:%v", err)
		select {
		case errs <- struct{}{}:
		default:
		}
		return
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		logs.CtxInfo(ctx, "ChatV2Stream Connection Close")
		select {
		case errs <- struct{}{}:
		default:
		}
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: session,
			ResponseMode:      STREAM,
			UserID:            userId,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))
		// 使用JSON序列化来安全地构造请求体
		requestBody, err := utils.JsonMarshalToBytes(queryReq)

		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			select {
			case errs <- struct{}{}:
			default:
			}
			return
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetBodyRaw(requestBody)
		}
	}
	hreq.SetHeader("Connection", "keep-alive")
	hreq.SetHeader("Accept", "text/event-stream")

	if err = cli.Subscribe(func(msg *sse.Event) {
		if msg.Data != nil && regex != nil && regex.Match(msg.Data) {
			data <- msg.Data
			regex = nil
		}
	}, sse.WithRequest(hreq)); err != nil {
		logs.CtxError(ctx, "ChatV2 Info %s Subscribe err:%v", session, err)
		select {
		case errs <- struct{}{}:
		default:
		}
	}
}

func ChatV2Block(ctx context.Context,
	query interface{},
	session, userId, sessionKey string,
) string {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetHttpClient err:%v", err)
		return ""
	}

	defer func() {
		PutHttpClient(hcli)
	}()

	cli, err := sse.NewClientWithOptions(sse.WithHertzClient(hcli))
	if err != nil {
		return ""
	}

	apikey, err := getApiKey(sessionKey)

	if err != nil {
		logs.CtxError(ctx, "ChatV2 GetApiKey err:%v", err)
		return ""
	}

	cli.SetDisconnectCallback(func(ctx context.Context, client *sse.Client) {
		logs.CtxInfo(ctx, "ChatV2Block Connection Close")
	})

	// 准备请求
	hreq := &protocol.Request{}
	{
		// 构造查询请求结构体，避免字符串转义问题
		queryReq := QueryReq{
			AppConversationID: session,
			ResponseMode:      BLOCK,
			UserID:            userId,
			QueryExtends:      map[string]interface{}{}, // 使用空的map而不是空的interface{}
		}
		if _, ok := query.(string); ok {
			queryReq.Query = fmt.Sprintf("%v", query) // 将interface{}转换为字符串
		} else {
			queryReq.Query = utils.JsonToString(query)
		}

		logs.CtxInfo(ctx, "ChatV2 queryReq:%v", utils.JsonToString(queryReq))
		// 使用JSON序列化来安全地构造请求体
		requestBody, err := utils.JsonMarshalToBytes(queryReq)
		if err != nil {
			logs.CtxError(ctx, "ChatV2 JsonMarshal err:%v", err)
			return ""
		}

		{
			hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/chat_query_v2")
			hreq.SetHeader("apikey", apikey)
			hreq.SetHeader("Content-Type", "application/json")
			hreq.SetHeader(consts.HeaderTimeout, strconv.Itoa(Header))
			hreq.SetBodyRaw(requestBody)
		}
	}

	resp := protocol.Response{}
	err = hcli.DoTimeout(ctx, hreq, &resp, DefaultTimeout)
	if err != nil || resp.StatusCode() != 200 || resp.Body() == nil || len(resp.Body()) == 0 {
		logs.CtxError(ctx, "ChatV2 Info %s Subscribe err:%v", session, err)
		return ""
	}
	return string(resp.Body())
}

// GetConversationMessages 查询记录
func GetConversationMessages(c context.Context, apikey, session, userId string) (*discourseHertz.SSEDetail, error) {
	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/GetConversationMessages] GetHttpClient err:%v", err)
		return nil, err
	}
	defer func() {
		PutHttpClient(hcli)
	}()

	hreq := &protocol.Request{}
	body, err := utils.JsonMarshalToBytes(&GetConversationsReq{
		AppConversationID: session,
		UserID:            userId,
		Limit:             100,
	})
	if err != nil {
		return nil, err
	}
	hreq.SetHeader("apikey", apikey)
	hreq.SetHeader("Content-Type", "application/json")
	hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/get_conversation_messages")
	hreq.SetBody(body)
	hreq.SetMethod("POST")

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &discourseHertz.SSEDetail{}
		_ = utils.JsonUnmarshal(resp.Body(), msg)
		return msg, nil
	}

	return nil, err
}

// GetConversationMessage 查询单条记录
func GetConversationMessage(c context.Context, apikey, messageID, userId string) (*discourseHertz.DetailMessage, error) {

	hcli, err := GetHttpClient()

	if err != nil {
		logs.CtxError(c, "[discourse/GetConversationMessages] GetHttpClient err:%v", err)
		return nil, err
	}
	defer func() {
		PutHttpClient(hcli)
	}()

	hreq := &protocol.Request{}
	body, err := utils.JsonMarshalToBytes(&GetConversationReq{
		MessageID: messageID,
		UserID:    userId,
	})
	if err != nil {
		return nil, err
	}
	hreq.SetHeader("apikey", apikey)
	hreq.SetHeader("Content-Type", "application/json")
	hreq.SetRequestURI(hiAgentConfig.BaseUrl + "/get_message_info")
	hreq.SetBody(body)
	hreq.SetMethod("POST")

	resp := &protocol.Response{}
	err = hcli.DoTimeout(c, hreq, resp, DefaultTimeout)

	if resp.StatusCode() == 200 && len(resp.Body()) > 0 {
		msg := &struct {
			MessageInfo *discourseHertz.DetailMessage
		}{}
		_ = utils.JsonUnmarshal(resp.Body(), msg)
		return (*msg).MessageInfo, nil
	}

	return nil, err
}
