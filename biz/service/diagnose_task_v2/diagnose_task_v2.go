package diagnose_task_v2

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"context"
	"errors"
	"sync"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/samber/lo"
)

var (
	service *Service
	once    sync.Once
)

const (
	defaultPageSize            = int32(10)
	defaultPageNum             = int32(1)
	DiagnoseTaskOriginPlatform = int32(1)
	DiagnoseTaskOriginTicket   = int32(2)
	DiagnoseTaskOriginBot      = int32(4)
	DiagnoseTypeItem           = int32(1)
	DiagnoseTypeTemplate       = int32(2)
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetDiagnoseTaskV2Service() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("DiagnoseTaskV2Service"),
		}
	})
	return service
}

// QueryDiagnoseTaskList 查询诊断任务列表
func (s *Service) QueryDiagnoseTaskList(ctx context.Context,
	req diagnose_task_v2.QueryDiagnoseTaskListReq) (*diagnose_task_v2.QueryDiagnoseTaskListResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskList")
	if req.PageNumber == nil {
		req.PageNumber = lo.ToPtr(defaultPageNum)
	}
	if req.PageSize == nil {
		req.PageSize = lo.ToPtr(defaultPageSize)
	}
	taskV2 := &model.DiagnoseTaskV2{
		Name:               req.Name,
		DiagnoseTemplateID: req.DiagnoseTemplateID,
		Origin:             req.Origin,
	}
	asc := false
	if req.AscSortByLastRunTime != nil && *req.AscSortByLastRunTime {
		asc = true
	}
	diagnoseTaskV2s, total, err := dal.DiagnoseTaskV2sFindByConditionSortByCreateTime(ctx, taskV2, int(*req.PageNumber), int(*req.PageSize), asc)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTasksFindByConditionSortByCreateTime err:%v", err)
		return nil, err
	}
	diagnoseTaskList := make([]*diagnose_task_v2.DiagnoseTask, 0)
	for _, task := range diagnoseTaskV2s {
		d := &diagnose_task_v2.DiagnoseTask{
			Id:                 task.ID,
			Name:               task.Name,
			DiagnoseType:       task.DiagnoseType,
			UpdateUserID:       task.UpdateUserID,
			CreateUserID:       task.CreateUserID,
			Origin:             task.Origin,
			Dimension:          task.Dimension,
			TicketID:           task.TicketID,
			DiagnoseTemplateID: task.DiagnoseTemplateID,
		}
		if task.UpdateTime != nil {
			updateTime, err := utils.TimeToString(*task.UpdateTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			d.UpdateTime = &updateTime
		}
		if task.LastRunTime != nil {
			lastRunTime, err := utils.TimeToString(*task.LastRunTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			d.LastRunTime = &lastRunTime
		}
		if task.CreateTime != nil {
			createTime, err := utils.TimeToString(*task.CreateTime, "")
			if err != nil {
				logger.CtxError(ctx, "TimeToString err:%v", err)
				return nil, err
			}
			d.CreateTime = &createTime
		}
		if task.ProductCategoryIds != nil {
			d.ProductCategorys, err = FindProductCategoryByProductCategoryIds(ctx, task.ProductCategoryIds)
			if err != nil {
				logger.CtxError(ctx, "FindProductCategoryByProductCategoryIds err:%v", err)
				return nil, err
			}
		}
		if task.DiagnoseItemIds != nil {
			ids, err := TransferStringToIntArray(task.DiagnoseItemIds)
			if err != nil {
				logger.CtxError(ctx, "TransferStringToIntArray err:%v", err)
				return nil, err
			}
			diagnoseItems, err := dal.DiagnoseItemFindByIDs(ctx, ids)
			if err != nil {
				logger.CtxError(ctx, "DiagnoseItemFindByIDs err:%v", err)
				return nil, err
			}
			items := make([]*diagnose_task_v2.DiagnoseItem, 0)
			for _, item := range diagnoseItems {
				productCategory, err := dal.QueryProductCategoryByID(ctx, item.ProductCategoryID)
				if err != nil {
					logger.CtxError(ctx, "QueryProductCategoryByID err:%v", err)
					return nil, err
				}
				items = append(items, &diagnose_task_v2.DiagnoseItem{
					Id:   item.DiagnoseItemID,
					Name: item.DiagnoseItemName,
					ProductCategory: &product_category.ProductCategory{
						Product:        productCategory.Product,
						SubProduct:     productCategory.SubProduct,
						ResourceType:   productCategory.ResourceType,
						ProductCn:      productCategory.ProductCn,
						SubProductCn:   productCategory.SubProductCn,
						ResourceTypeCn: productCategory.ResourceTypeCn,
						Id:             productCategory.ID,
					},
				})
			}
			d.DiagnoseItems = items
		}
		diagnoseTaskList = append(diagnoseTaskList, d)
	}
	return &diagnose_task_v2.QueryDiagnoseTaskListResult{
		DiagnoseTaskList: diagnoseTaskList,
		Pagination: &common.Pagination{
			PageNumber: *req.PageNumber,
			PageSize:   *req.PageSize,
			TotalCount: total,
		},
	}, nil
}

// DeleteDiagnoseTask 删除诊断任务
func (s *Service) DeleteDiagnoseTask(ctx context.Context,
	req diagnose_task_v2.DeleteDiagnoseTaskReq) error {
	logger := s.logger.WithFunc("DeleteDiagnoseTask")
	err := dal.DeleteDiagnoseTaskV2ByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DeleteDiagnoseTaskV2ByID err:%v", err)
		return err
	}
	return nil
}

// QueryDiagnoseTaskDetail 查询诊断任务详情
func (s *Service) QueryDiagnoseTaskDetail(ctx context.Context,
	req diagnose_task_v2.QueryDiagnoseTaskDetailReq) (*diagnose_task_v2.QueryDiagnoseTaskDetailResult, error) {
	logger := s.logger.WithFunc("QueryDiagnoseTaskDetail")
	task, err := dal.DiagnoseTaskV2FindByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTaskV2FindByID err:%v", err)
		return nil, err
	}
	d := &diagnose_task_v2.DiagnoseTask{
		Id:                 task.ID,
		Name:               task.Name,
		DiagnoseType:       task.DiagnoseType,
		UpdateUserID:       task.UpdateUserID,
		CreateUserID:       task.CreateUserID,
		Origin:             task.Origin,
		Dimension:          task.Dimension,
		TicketID:           task.TicketID,
		DiagnoseTemplateID: task.DiagnoseTemplateID,
	}
	if task.UpdateTime != nil {
		updateTime, err := utils.TimeToString(*task.UpdateTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		d.UpdateTime = &updateTime
	}
	if task.LastRunTime != nil {
		lastRunTime, err := utils.TimeToString(*task.LastRunTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		d.LastRunTime = &lastRunTime
	}
	if task.CreateTime != nil {
		createTime, err := utils.TimeToString(*task.CreateTime, "")
		if err != nil {
			logger.CtxError(ctx, "TimeToString err:%v", err)
			return nil, err
		}
		d.CreateTime = &createTime
	}
	if task.ProductCategoryIds != nil {
		d.ProductCategorys, err = FindProductCategoryByProductCategoryIds(ctx, task.ProductCategoryIds)
		if err != nil {
			logger.CtxError(ctx, "FindProductCategoryByProductCategoryIds err:%v", err)
			return nil, err
		}
	}
	if task.DiagnoseItemIds != nil {
		ids, err := TransferStringToIntArray(task.DiagnoseItemIds)
		if err != nil {
			logger.CtxError(ctx, "TransferStringToIntArray err:%v", err)
			return nil, err
		}
		diagnoseItems, err := dal.DiagnoseItemFindByIDs(ctx, ids)
		if err != nil {
			logger.CtxError(ctx, "DiagnoseItemFindByIDs err:%v", err)
			return nil, err
		}
		items := make([]*diagnose_task_v2.DiagnoseItem, 0)
		for _, item := range diagnoseItems {
			productCategory, err := dal.QueryProductCategoryByID(ctx, item.ProductCategoryID)
			if err != nil {
				logger.CtxError(ctx, "QueryProductCategoryByID err:%v", err)
				return nil, err
			}
			items = append(items, &diagnose_task_v2.DiagnoseItem{
				Id:   item.DiagnoseItemID,
				Name: item.DiagnoseItemName,
				ProductCategory: &product_category.ProductCategory{
					Product:        productCategory.Product,
					SubProduct:     productCategory.SubProduct,
					ResourceType:   productCategory.ResourceType,
					ProductCn:      productCategory.ProductCn,
					SubProductCn:   productCategory.SubProductCn,
					ResourceTypeCn: productCategory.ResourceTypeCn,
					Id:             productCategory.ID,
				},
			})
		}
		d.DiagnoseItems = items
	}
	return &diagnose_task_v2.QueryDiagnoseTaskDetailResult{DiagnoseTaskInfo: d}, nil
}

// CreateDiagnoseTask 创建诊断任务
func (s *Service) CreateDiagnoseTask(ctx context.Context,
	req diagnose_task_v2.CreateDiagnoseTaskV2Req) (int64, error) {
	logger := s.logger.WithFunc("CreateDiagnoseTask")
	t := time.Now()
	diagnoseTaskV2 := &model.DiagnoseTaskV2{
		Name:               lo.ToPtr(req.Name),
		DiagnoseType:       lo.ToPtr(req.DiagnoseType),
		DiagnoseTemplateID: req.DiagnoseTemplateID,
		Origin:             req.Origin,
		CreateUserID:       lo.ToPtr(req.CreateUserID),
		CreateTime:         lo.ToPtr(t),
		UpdateUserID:       lo.ToPtr(req.CreateUserID),
		UpdateTime:         lo.ToPtr(t),
		Dimension:          lo.ToPtr(req.Dimension),
		TicketID:           req.TicketID,
	}
	if diagnoseTaskV2.Origin == nil {
		diagnoseTaskV2.Origin = lo.ToPtr(DiagnoseTaskOriginPlatform)
	}
	if len(req.ProductCategoryIDs) == 0 {
		logger.CtxError(ctx, "ProductCategoryIDs is nil")
		return 0, errors.New("ProductCategoryIDs is nil")
	} else {
		productCategoryIdsStr, err := TransferIntArrayToString(req.ProductCategoryIDs)
		if err != nil {
			logger.CtxError(ctx, "TransferIntArrayToString err:%v", err)
			return 0, err
		}
		diagnoseTaskV2.ProductCategoryIds = &productCategoryIdsStr
	}
	taskItemIdArray := make([]int64, 0)
	if req.DiagnoseType == DiagnoseTypeTemplate {
		if req.DiagnoseTemplateID == nil {
			logger.CtxError(ctx, "DiagnoseTemplateID is nil")
			return 0, errors.New("DiagnoseTemplateID is nil")
		}
		//todo:通过模板ID获取诊断项ID
		relations, err := dal.FindRelationByTemplateID(ctx, *req.DiagnoseTemplateID)
		if err != nil {
			logger.CtxError(ctx, "FindRelationByTemplateID err:", err)
			return 0, errors.New("FindRelationByTemplateID err:")
		}
		for _, relation := range relations {
			taskItemIdArray = append(taskItemIdArray, relation.DiagnoseItemID)
		}
	} else {
		if req.DiagnoseItemIDs == nil {
			logger.CtxError(ctx, "DiagnoseItemIDs is nil")
			return 0, errors.New("DiagnoseItemIDs is nil")
		}
		taskItemIdArray = req.DiagnoseItemIDs
	}
	diagnoseItemIdsStr, err := TransferIntArrayToString(taskItemIdArray)
	if err != nil {
		logger.CtxError(ctx, "TransferIntArrayToString err:%v", err)
		return 0, err
	}
	diagnoseTaskV2.DiagnoseItemIds = &diagnoseItemIdsStr
	err = dal.CreateDiagnoseTaskV2(ctx, diagnoseTaskV2)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTaskV2Create err:%v", err)
		return 0, err
	}
	return diagnoseTaskV2.ID, nil
}

// UpdateDiagnoseTask 更新诊断任务
func (s *Service) UpdateDiagnoseTask(ctx context.Context,
	req diagnose_task_v2.UpdateDiagnoseTaskReq) error {
	logger := s.logger.WithFunc("UpdateDiagnoseTask")

	// 从数据库中获取现有的诊断任务
	task, err := dal.DiagnoseTaskV2FindByID(ctx, req.Id)
	if err != nil {
		logger.CtxError(ctx, "DiagnoseTaskV2FindByID err:%v", err)
		return err
	}

	// 更新任务信息
	if req.Name != nil {
		task.Name = req.Name
	}
	if req.DiagnoseType != nil {
		task.DiagnoseType = req.DiagnoseType
	}
	if req.DiagnoseTemplateID != nil {
		task.DiagnoseTemplateID = req.DiagnoseTemplateID
	}
	if req.Dimension != nil {
		task.Dimension = req.Dimension
	}
	if req.TicketID != nil {
		task.TicketID = req.TicketID
	}

	// 更新时间
	t := time.Now()
	task.UpdateUserID = lo.ToPtr(req.UpdateUserID)
	task.UpdateTime = lo.ToPtr(t)

	if req.ProductCategoryIDs != nil {
		productCategoryIdsStr, err := TransferIntArrayToString(req.ProductCategoryIDs)
		if err != nil {
			logger.CtxError(ctx, "TransferIntArrayToString err:%v", err)
			return err
		}
		task.ProductCategoryIds = &productCategoryIdsStr
	}
	if req.DiagnoseItemIDs != nil {
		diagnoseItemIdsStr, err := TransferIntArrayToString(req.DiagnoseItemIDs)
		if err != nil {
			logger.CtxError(ctx, "TransferIntArrayToString err:%v", err)
			return err
		}
		task.DiagnoseItemIds = &diagnoseItemIdsStr
	}

	// 保存更新后的任务
	err = dal.UpdateDiagnoseTaskV2(ctx, task)
	if err != nil {
		logger.CtxError(ctx, "UpdateDiagnoseTaskV2 err:%v", err)
		return err
	}

	return nil
}
