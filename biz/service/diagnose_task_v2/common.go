package diagnose_task_v2

import (
	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"context"
	"encoding/json"
)

func TransferStringToIntArray(diagnoseItemIds *string) ([]int64, error) {
	if diagnoseItemIds == nil {
		return nil, nil
	}
	diagnoseItemIdsArray := make([]int64, 0)
	err := json.Unmarshal([]byte(*diagnoseItemIds), &diagnoseItemIdsArray)
	if err != nil {
		return nil, err
	}
	return diagnoseItemIdsArray, nil
}
func TransferIntArrayToString(diagnoseItemIds []int64) (string, error) {
	diagnoseItemIdsByte, err := json.Marshal(diagnoseItemIds)
	if err != nil {
		return "", err
	}
	return string(diagnoseItemIdsByte), nil
}

func FindProductCategoryByProductCategoryIds(ctx context.Context, productCategoryIdsStr *string) ([]*product_category.ProductCategory, error) {
	productCategoryIds, err := TransferStringToIntArray(productCategoryIdsStr)
	if err != nil {
		return nil, err
	}
	productCategorys, err := dal.ProductCategoryFindByIDs(ctx, productCategoryIds)
	if err != nil {
		return nil, err
	}
	pCategory := make([]*product_category.ProductCategory, 0)
	for _, category := range productCategorys {
		pCategory = append(pCategory, &product_category.ProductCategory{
			Product:        category.Product,
			SubProduct:     category.SubProduct,
			ResourceType:   category.ResourceType,
			ProductCn:      category.ProductCn,
			SubProductCn:   category.SubProductCn,
			ResourceTypeCn: category.ResourceTypeCn,
			Id:             category.ID,
		})
	}
	return pCategory, nil
}
