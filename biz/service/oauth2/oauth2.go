package oauth2

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"sync"
	"time"

	"golang.org/x/oauth2"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/middleware"
	OAuth2 "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/oauth2"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

var (
	service *Service
	once    sync.Once
	// 保存到 redis中的token的过期时间为 7 天
	ttlNanoSeconds = (time.Hour * 24 * 7).Nanoseconds()
)

type Service struct {
	logger *utils.ModuleLogger
}

func GetOauth2Service() *Service {
	once.Do(func() {
		service = &Service{
			logger: utils.NewModuleLogger("oauth2Service"),
		}
	})
	return service
}

// 1、生成随机唯一state,防止CSRF攻击
// 2、将当前 randomState 和 original_path 建立映射关系，保存到map中
// 3、生成授权地址
// 4、返回前端
// 返回生成的获取授权码的url
func (s *Service) AuthUrl(ctx context.Context, authUrlReq *OAuth2.AuthUrlReq) (*OAuth2.AuthUrlResult, error) {
	// 创建日志记录器
	logger := s.logger.WithFunc("AuthUrl")
	// 生成随机唯一state,防止CSRF攻击
	randomState := string(utils.RandStringBytes(12))

	// 如果 original_path 为空，返回错误
	if authUrlReq.OriginalPath == "" {
		err := fmt.Errorf("request original_path is empty")
		logger.CtxError(ctx, "%v", err)
		return nil, err
	}
	// 将当前 randomState 和 original_path 建立映射关系，保存到map中
	middleware.OriginalPath[randomState] = authUrlReq.OriginalPath
	// 生成授权地址
	redirectURL := sso.Conf.AuthCodeURL(randomState, oauth2.AccessTypeOnline)
	result := &OAuth2.AuthUrlResult{
		AuthUrl: redirectURL,
	}

	return result, nil
}

// 1、校验state,防止CSRF攻击. 如果 state 存在，则在函数结束时删除对应的state
// 2、获取 token
// 3、获取用户信息
// 4、将用户信息写入redis 中 ，格式为 "token" + tokenSHA256: base64(ase(userInfo json字符串）)
// 5、返回前端token，和 userinfo lite 版信息
func (s *Service) Token(ctx context.Context, tokenReq *OAuth2.TokenReq) (*OAuth2.TokenResult, error) {
	// 创建日志记录器
	logger := s.logger.WithFunc("Token")
	// 获取code
	code := tokenReq.Code
	// 	获取state
	state := tokenReq.State
	// 如果code或者state为空，则返回错误信息
	if code == "" || state == "" {
		logger.CtxError(ctx, "code or state is empty")
		return nil, fmt.Errorf("code or state is empty")
	}

	// 校验state,防止CSRF攻击,如果 state 不存在，则返回错误信息
	if middleware.OriginalPath[state] == "" {
		logger.CtxError(ctx, "invalid auth state")
		return nil, errorcode.ErrorCodeAuthStateInvalid
	}
	// 从map中删除对应的state
	defer func() {
		delete(middleware.OriginalPath, state)
	}()
	// 获取 token
	token, err := sso.Conf.Exchange(ctx, code)
	// 如果获取token失败，则返回错误信息
	if err != nil {
		logger.CtxError(ctx, "oauth2 get token failed: %v", err)
		return nil, err
	}

	// 生成token的hash值
	tokenHash := utils.Sha256(token.AccessToken)
	// 获取用户信息
	httpClient := sso.Conf.Client(ctx, token)
	resp, err := httpClient.Get(sso.Conf.UserInfoURL)
	// 如果获取用户信息失败，则返回错误信息
	if err != nil {
		logger.CtxError(ctx, "oauth2 get userinfo failed: %v", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取用户信息，如果失败，则返回错误信息
	bs, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		logger.CtxError(ctx, "oauth2 read userinfo failed: %v", err)
		return nil, err
	}

	// 解析用户信息，如果失败，则返回错误信息
	var userInfo = sso.NewUserInfo()
	if err := json.Unmarshal(bs, &userInfo); err != nil {
		logger.CtxError(ctx, "oauth2 unmarshal userinfo failed: %v", err)
		return nil, err
	}

	// 将用户的信息写入到 redis 中
	// err = userInfo.StoreToRedis("token_"+tokenHash, time.Since(token.Expiry).Seconds())
	err = userInfo.StoreToRedis("token_"+tokenHash, ttlNanoSeconds)
	// 如果写入redis失败，则返回错误信息
	if err != nil {
		logger.CtxError(ctx, "oauth2 store userinfo to redis failed: %v", err)
		return nil, err
	}

	result := &OAuth2.TokenResult{
		OriginalPath: middleware.OriginalPath[state],
		Token:        token.AccessToken,
		Username:     userInfo.Username,
		Email:        userInfo.Email,
		Department:   userInfo.Department,
	}
	return result, nil
}

// 1、获取用户传递过来的token，如果无法获取 token，则返回错误信息
// 2、从redis中获取用户信息，如果存在，则返回用户信息
// 3、如果不存在，则返回查询sso userinfo 接口
// 4、如果查询sso userinfo 接口失败，则返回错误信息
// 5、如果查询sso userinfo 接口成功，则将用户信息写入redis中，并返回用户信息
func (s *Service) UserInfo(ctx context.Context, UserInfoReq *OAuth2.UserInfoReq) (*OAuth2.UserInfoResult, error) {
	// 创建日志记录器
	logger := s.logger.WithFunc("Token")

	// 如果token为空，则返回错误信息
	if UserInfoReq.Token == "" {
		err := fmt.Errorf("token is empty")
		logger.CtxError(ctx, "%v", err)
		return nil, err
	}
	// 从redis中获取用户信息,如果存在，则返回用户信息
	u, err := sso.GetUserInfoByTokenFromRedis(UserInfoReq.Token)
	if (err != nil) && (u != nil) && (u.Name != "") {
		// 如果从redis中获取用户信息成功，则返回用户信息
		result := &OAuth2.UserInfoResult{
			Username:   u.Username,
			Email:      u.Email,
			Department: u.Department,
		}
		return result, nil
	}

	// 如果从redis中获取用户信息失败，则返回查询sso userinfo 接口
	userInfo, err := sso.GetUserInfoByToken(UserInfoReq.Token)
	if err != nil {
		logger.CtxError(ctx, "%v", err)
		return nil, err
	}

	// 将用户信息写入redis中
	err = userInfo.StoreToRedis("token_"+utils.Sha256(UserInfoReq.Token), ttlNanoSeconds)
	if err != nil {
		logger.CtxError(ctx, "%v", err)
		return nil, err
	}

	// 返回用户信息
	result := &OAuth2.UserInfoResult{
		Username:   userInfo.Username,
		Email:      userInfo.Email,
		Department: userInfo.Department,
	}
	return result, nil
}
