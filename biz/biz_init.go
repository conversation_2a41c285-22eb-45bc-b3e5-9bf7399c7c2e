package biz

import (
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/event"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/byteflowclient"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/eps_permission"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
)

func Init() {
	// 初始化天穹
	// databus_go.InitUBACollector(env.IsProduct())

	// 配置初始化
	tcc.InitServiceConfig()
	// rds初始化
	rds.Init()
	// redis 初始化
	rds_redis.InitRedis()
	// sso 配置初始化
	sso.InitSSOconfig()
	// 飞书初始化
	// larkclient.Init()
	// 权限初始化
	eps_permission.Init()

	// 飞书事件订阅初始化
	// lark_event.InitLarkEventConsumer()
	event.InitEvent()
	byteflowclient.Init()

}
