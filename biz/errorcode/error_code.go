package errorcode

import (
	"fmt"
	"net/http"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/error_code"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/cloudwego/hertz/pkg/app"
)

type ErrorStructure struct {
	Code      string
	Args      []interface{}
	MessageZh string
	HTTPCode  int
}

var (
	// 通用
	ErrCommonInternalError = &ErrorStructure{
		Code:      error_code.ErrorCodeCommonError.String(),
		MessageZh: "内部服务错误",
		HTTPCode:  500,
	}
	ErrUnAuthentication = &ErrorStructure{
		Code:      error_code.ErrorCodeUnAuthentication.String(),
		MessageZh: "用户未鉴权",
		HTTPCode:  401,
	}

	ErrNoAuthentication = &ErrorStructure{
		Code:      error_code.ErrorCodeNoAuthentication.String(),
		MessageZh: "用户无权限",
		HTTPCode:  403,
	}

	ErrNoViewableData = &ErrorStructure{
		Code:      error_code.ErrorCodeUserHasNoViewableData.String(),
		MessageZh: "用户无可见数据",
		HTTPCode:  403,
	}

	ErrRequestParamInvalid = &ErrorStructure{
		Code:      error_code.ErrorCodeRequestParamInvalid.String(),
		MessageZh: "参数 [%s] 缺失或不合法",
		HTTPCode:  400,
	}

	ErrDataAlreadyExist = &ErrorStructure{
		Code:      error_code.ErrorCodeDataAlreadyExist.String(),
		MessageZh: "记录已存在",
		HTTPCode:  400,
	}

	ErrDataNotFound = &ErrorStructure{
		Code:      error_code.ErrorCodeDataNotFound.String(),
		MessageZh: "数据不存在",
		HTTPCode:  200,
	}

	ErrRegisterDiagnoseTemplateSuccessRelateItemFail = &ErrorStructure{
		Code:      error_code.ErrorCodeErrRegisterDiagnoseTemplateSuccessRelateItemFailed.String(),
		MessageZh: "注册诊断模版成功，但绑定诊断项失败",
		HTTPCode:  500,
	}

	ErrUpStreamSystemError = &ErrorStructure{
		Code:      error_code.ErrorCodeUpStreamSystemError.String(),
		MessageZh: "上游系统 [%s] 异常",
		HTTPCode:  500,
	}

	ReLoadingFrontEndError = &ErrorStructure{
		Code:      error_code.ErrorCodeReLoadingFrontEndError.String(),
		MessageZh: "重新加载前端页面",
	}

	ErrTccConfigNotFound = &ErrorStructure{
		Code:      error_code.ErrorCodeTccConfigNotFound.String(),
		MessageZh: "TCC配置 [%s] 不存在",
		HTTPCode:  500,
	}

	ErrUndefinedError = &ErrorStructure{
		Code:      error_code.ErrorCodeUndefinedError.String(),
		MessageZh: "未定义错误",
		HTTPCode:  500,
	}

	ErrBizError = &ErrorStructure{
		Code:      error_code.ErrorCodeBizError.String(),
		MessageZh: "业务异常",
		HTTPCode:  200,
	}

	ErrorCodeAuthStateInvalid = &ErrorStructure{
		Code:      error_code.ErrorCodeAuthStateInvalid.String(),
		MessageZh: "auth state 值不合法",
		// 为了让前端拦截器判断未鉴权的请求，因此这里返回 401
		HTTPCode: http.StatusUnauthorized,
	}

	ErrorCodeAuthTokenInvalid = &ErrorStructure{
		Code:      error_code.ErrorCodeAuthTokenInvalid.String(),
		MessageZh: "无效的 auth token ",
		// 为了让前端拦截器判断未鉴权的请求，因此这里返回 401
		HTTPCode: http.StatusUnauthorized,
	}

	ErrorCodeAuthTokenMissing = &ErrorStructure{
		Code:      error_code.ErrorCodeAuthTokenMissing.String(),
		MessageZh: "auth Token 参数缺失",
		// 为了让前端拦截器判断未鉴权的请求，因此这里返回 401
		HTTPCode: http.StatusUnauthorized,
	}

	ErrorCodeStoreRedisError = &ErrorStructure{
		Code:      error_code.ErrorCodeStoreRedisError.String(),
		MessageZh: "token 存储到 redis 失败",
		// 为了让前端拦截器判断未鉴权的请求，因此这里返回 401
		HTTPCode: http.StatusUnauthorized,
	}

	AkSkMissing = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorCodeAkSkMissing.String(),
		MessageZh: "AK SK 参数缺失",
		HTTPCode:  http.StatusUnauthorized,
	}
	AkSkInvalid = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorCodeAkSkInvalid.String(),
		MessageZh: "AK SK 无效",
		HTTPCode:  http.StatusUnauthorized,
	}
	AkSkOutDate = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorCodeAkSkExpired.String(),
		MessageZh: "AK SK 已过期",
		HTTPCode:  http.StatusUnauthorized,
	}
	RedisConnectError = &ErrorStructure{
		Code:      error_code.ErrorCodeStoreRedisError.String(),
		MessageZh: "redis 连接失败",
		HTTPCode:  http.StatusInternalServerError,
	}
	ErrRunTaskExist = &ErrorStructure{
		Code:      error_code.ErrorCodeDataAlreadyExist.String(),
		MessageZh: "存在正在执行的任务",
		HTTPCode:  400,
	}
	ErrMonitorTimeDuration = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorMonitorTimeDuration.String(),
		MessageZh: "监控查询时间跨度过大,%s产品监控查询最大时间跨度为%d小时",
		HTTPCode:  400,
	}
	ErrMonitorTimeBeforeNow = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorMonitorTimeBeforeNow.String(),
		MessageZh: "监控查询时间过久，%s产品仅支持查询最近%d小时之内的数据",
		HTTPCode:  400,
	}
	ErrMonitorEndTimeBeforeStartTime = &ErrorStructure{
		Code:      error_code.ErrorCodeErrorMonitorEndTimeBeforeStartTime.String(),
		MessageZh: "监控结束时间早于开始时间",
		HTTPCode:  400,
	}
	ErrRunTaskError = &ErrorStructure{
		Code:      error_code.ErrorCodeDataAlreadyExist.String(),
		MessageZh: "执行智能诊断任务失败",
		HTTPCode:  500,
	}
)

// GetCode GetCode
func (e *ErrorStructure) GetCode() string {
	return e.Code
}

func (e *ErrorStructure) Error() string {
	msg := fmt.Sprintf(e.MessageZh, e.Args...)
	return fmt.Sprintf("code: %s, message: %s", e.Code, msg)
}

func (e *ErrorStructure) WithArgs(a ...interface{}) ehttp.APIError {
	e2 := *e
	e2.Args = append(e2.Args, a...)
	return &e2
}

func (e *ErrorStructure) WithMessage(msg string, a ...any) ehttp.APIError {
	e2 := *e
	e2.MessageZh = fmt.Sprintf(msg, a...)
	return &e2
}

// GetMessage GetMessage
func (e *ErrorStructure) GetMessage(c *app.RequestContext) string {
	return e.MessageZh
}

func (e *ErrorStructure) HttpStatus() int {
	return e.HTTPCode
}

// GetCodeInt GetCodeInt
func (e *ErrorStructure) GetCodeInt() int {
	return 0
}

// GetArgs GetArgs
func (e *ErrorStructure) GetArgs() []interface{} {
	return e.Args
}

// GetArgs GetArgs
func (e *ErrorStructure) IsSame(err error) bool {
	if errE, ok := err.(*ErrorStructure); !ok {
		return false
	} else {
		return errE.GetCode() == e.GetCode()
	}
}
