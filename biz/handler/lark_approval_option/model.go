package lark_approval_option

type ApprovalOptionResult struct {
	Options       []*ApprovalOption       `json:"options"`
	I18NResources []*ApprovalI18NResource `json:"i18nResources"`
}

type ApprovalOption struct {
	ID        string `json:"id"`
	Value     string `json:"value"`
	IsDefault bool   `json:"isDefault,omitempty"`
}

type ApprovalI18NResource struct {
	Locale    string            `json:"locale"`
	IsDefault bool              `json:"isDefault"`
	Texts     map[string]string `json:"texts"`
}

type ApprovalOptionParams struct {
	UserID        string            `json:"user_id"`
	EmployeeID    string            `json:"employee_id"`
	Token         string            `json:"token,required"`
	LinkageParams map[string]string `json:"linkage_params"`
	Query         string            `json:"query"`
}
