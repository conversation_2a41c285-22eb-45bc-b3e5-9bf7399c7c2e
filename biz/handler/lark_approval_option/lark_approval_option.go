package lark_approval_option

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

var optionProviders = []ApprovalOptionProvider{}

type ApprovalOptionProvider interface {
	MatchToken(token string) bool
	Provide(ctx context.Context, params *ApprovalOptionParams) (*ApprovalOptionResult, error)
}

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func LarkApprovalOptionProvide(ctx context.Context, c *app.RequestContext) {
	params := &ApprovalOptionParams{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		c.<PERSON>(http.StatusOK, map[string]interface{}{
			"code": -1,
		})
		return
	}

	ret, err := provide(ctx, params)
	if err != nil {
		c.JSON(http.StatusOK, map[string]interface{}{
			"code": -1,
		})
		return
	}
	c.JSON(http.StatusOK, map[string]interface{}{
		"code": 0,
		"data": map[string]interface{}{
			"result": ret,
		},
	})
}

func provide(ctx context.Context, params *ApprovalOptionParams) (*ApprovalOptionResult, error) {
	logs.CtxInfo(ctx, "lark approval options params:%s", utils.JsonToString(params))

	for _, provider := range optionProviders {
		if provider.MatchToken(params.Token) {
			return provider.Provide(ctx, params)
		}
	}
	return nil, errorcode.ErrBizError.WithArgs("没有对应的选项提供")
}
