package common

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	commonSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"

	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) GetCommonMapConfig20241201(ctx context.Context, c *app.RequestContext) {
	req := &common.GetCommonMapConfigReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := commonSvc.GetCommonService().GetCommonMapConfig(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
