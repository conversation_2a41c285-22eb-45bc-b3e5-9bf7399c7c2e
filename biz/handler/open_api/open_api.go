package open_api

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	taskRunSrvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	opApiSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/open_api"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/openapi"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) ListDiagnoseItem20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.ListDiagnoseItemRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().ListDiagnoseItem(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListDiagnoseTemplate20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.ListDiagnoseTemplateRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().ListDiagnoseTemplate(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListProductCategory20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.ListProductCategoryRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().ListProductCategory(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) CreateDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.CreateDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().CreateDiagnoseTask(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskRunSummary20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_run.QueryDiagnoseTaskRunSummaryReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := taskRunSrvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunSummary(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryDiagnoseTaskResult20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.QueryDiagnoseTaskResultRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().QueryDiagnoseTaskResult(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) UpdateDiagnoseItemName20241201(ctx context.Context, c *app.RequestContext) {
	req := &openapi.UpdateDiagnoseItemNameRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}
	if req.DiagnoseItemID <= 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseItemId"))
		return
	}

	result, err := opApiSvc.GetOpenApiService().UpdateDiagnoseItemName(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) TicketSummary20250701(ctx context.Context, c *app.RequestContext) {
	req := &openapi.TicketSummaryReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := opApiSvc.GetOpenApiService().TicketSummary(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) TicketReport20250701(ctx context.Context, c *app.RequestContext) {
	req := &openapi.TicketReportReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, opApiSvc.GetOpenApiService().TicketReport(ctx, req))
}
