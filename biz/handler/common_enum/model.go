package common_enum

type ScheduleEventReq struct {
	TaskName string
	Args     map[string]interface{}
}

type ScheduleRequestReq struct {
	// Method
	HTTPMethod string

	// path, like /abc
	Path string

	// ip:port
	RemoteAddr string

	// parsed path params
	PathParameters map[string]string

	// parsed url query string params. ex: [a,b,c] parsed to "a,b,c" when query values is array.
	QueryStringParameters map[string]string

	// http headers
	Headers map[string]string

	// body in raw bytes
	Body string
}
