package common_enum

import (
	"context"

	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/event"
	"code.byted.org/volcengine-support/cloud-sherlock/faas_biz/request"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

// func (h *ActionHandler) GetCommonEnumConfig20240801(ctx context.Context, c *app.RequestContext) {
// 	req := &enumHertz.GetCommonEnumConfigReq{}
// 	if err := binding.BindAndValidate(c, req); err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return
// 	}

// 	result, err := enumSvc.GetCommonService().GetCommonEnumConfig(ctx)
// 	if err != nil {
// 		handler.ErrorResponse(ctx, c, errorcode.ErrDataNotFound.WithArgs("Common enum config"))
// 		return
// 	}

// 	ehttp.DoResponse(ctx, c, result.CommonEnumList)
// }

// func (h *ActionHandler) TriggerLarkEventCallback(ctx context.Context, c *app.RequestContext) {
// 	req := &lark_event.ApprovalInstanceEvent{}
// 	if err := binding.BindAndValidate(c, req); err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return
// 	}

// 	err := lark_event.LarkApprovalInstanceEventHandle(ctx, req)
// 	if err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return
// 	}
// 	ehttp.DoResponse(ctx, c, nil)
// }

func (h *ActionHandler) ScheduleEvent(ctx context.Context, c *app.RequestContext) {
	req := &ScheduleEventReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	err := event.ScheduleEvent(ctx, req.TaskName, req.Args)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) ScheduleRequest(ctx context.Context, c *app.RequestContext) {
	req := &ScheduleRequestReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	resp, err := request.ScheduleRequest(ctx, &events.HTTPRequest{
		HTTPMethod:            req.HTTPMethod,
		Path:                  req.Path,
		PathParameters:        req.PathParameters,
		QueryStringParameters: req.QueryStringParameters,
		Headers:               req.Headers,
		Body:                  []byte(req.Body),
	})
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, resp)
}
