package product_category

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	productCategorySvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) QueryProductCategoryList20241201(ctx context.Context, c *app.RequestContext) {
	req := &product_category.QueryProductCategoryListReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := productCategorySvc.GetProductCategoryService().QueryProductCategoryList(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryProductCategoryListByIDs20241201(ctx context.Context, c *app.RequestContext) {
	req := &product_category.QueryProductCategoryListByIDsReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := productCategorySvc.GetProductCategoryService().QueryProductCategoryListByIDs(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
