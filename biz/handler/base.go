package handler

import (
	"context"
	"encoding/json"

	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/bytedance/go-tagexpr/v2/binding"
)

// ErrorResponse 错误处理
func ErrorResponse(ctx context.Context, c *app.RequestContext, err error) {
	switch t := err.(type) {
	case *binding.Error:
		ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs(t.<PERSON>ail<PERSON>))
	case *errorcode.ErrorStructure:
		ehttp.ErrorResp(ctx, c, t)
	case *json.UnmarshalTypeError:
		ehttp.ErrorResp(ctx, c, errorcode.ErrCommonInternalError.WithArgs(t.Field))
	//case *decoder.MismatchTypeError:
	//	ehttp.ErrorResp(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("请求类型不匹配", t.Field))
	default:
		switch t.Error() {
		case "record not found":
			ehttp.ErrorResp(ctx, c, errorcode.ErrDataNotFound)
		default:
			logs.CtxError(ctx, "undefined error %+v", t)
			ehttp.ErrorResp(ctx, c, errorcode.ErrUndefinedError)
		}
	}
}
