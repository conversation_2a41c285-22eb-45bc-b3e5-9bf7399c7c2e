package diagnose_task_v2

import (
	"context"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	diagnoseTaskV2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) QueryDiagnoseTaskList20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_v2.QueryDiagnoseTaskListReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().QueryDiagnoseTaskList(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

// CreateDiagnoseTask20241201 CreateDiagnoseTask 创建诊断任务的 handler 函数
func (h *ActionHandler) CreateDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_v2.CreateDiagnoseTaskV2Req{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().CreateDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

// UpdateDiagnoseTask20241201 UpdateDiagnoseTask 更新诊断任务的 handler 函数
func (h *ActionHandler) UpdateDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_v2.UpdateDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().UpdateDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

// DeleteDiagnoseTask20241201 DeleteDiagnoseTask 删除诊断任务的 handler 函数
func (h *ActionHandler) DeleteDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_v2.DeleteDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().DeleteDiagnoseTask(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

// QueryDiagnoseTaskDetail20241201 查询诊断任务详情的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskDetail20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_v2.QueryDiagnoseTaskDetailReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := diagnoseTaskV2Svc.GetDiagnoseTaskV2Service().QueryDiagnoseTaskDetail(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
