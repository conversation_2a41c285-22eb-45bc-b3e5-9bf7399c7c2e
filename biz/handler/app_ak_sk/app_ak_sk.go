package app_ak_sk

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	appAkSkSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/app_ak_sk"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/aksk"

	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) CreateAppAkSk20241201(ctx context.Context, c *app.RequestContext) {
	req := &aksk.CreateAppAkSkReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := appAkSkSvc.GetAppAkSkService().CreateAppAkSk(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) QueryAppAkSkList20241201(ctx context.Context, c *app.RequestContext) {
	req := &aksk.QueryAppAkSkListReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := appAkSkSvc.GetAppAkSkService().QueryAppAkSkList(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) DeleteAppAkSk20241201(ctx context.Context, c *app.RequestContext) {
	req := &aksk.DeleteAppAkSkReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := appAkSkSvc.GetAppAkSkService().DeleteAppAkSk(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}

func (h *ActionHandler) UpdateAppAkSk20241201(ctx context.Context, c *app.RequestContext) {
	req := &aksk.UpdateAppAkSkReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	err := appAkSkSvc.GetAppAkSkService().UpdateAppAkSk(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}
