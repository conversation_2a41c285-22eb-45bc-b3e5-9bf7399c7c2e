package oauth2

import (
	"context"

	"code.byted.org/eps-platform/biz_gopkg/epsauth"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	oauth2Svc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/oauth2"
	OAuth2 "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/oauth2"
	redisStorage "code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

// action handler
type ActionHandler struct {
	ehttp.CommonActionHandler
}

// 生成申请授权码的url
func (a *ActionHandler) AuthUrl20250312(ctx context.Context, c *app.RequestContext) {
	// 创建请求参数结构体，绑定参数
	req := &OAuth2.AuthUrlReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用service
	result, err := oauth2Svc.GetOauth2Service().AuthUrl(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	ehttp.DoResponse(ctx, c, result)

}

// 获取token
func (a *ActionHandler) Token20250312(ctx context.Context, c *app.RequestContext) {
	// 创建请求参数结构体，绑定参数
	req := &OAuth2.TokenReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	// 调用service
	result, err := oauth2Svc.GetOauth2Service().Token(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	ehttp.DoResponse(ctx, c, result)
}

// 通过用户传递过来的token，查询 sso userinfo 接口，获取用户信息，最后将用户信息返回前端
func (a *ActionHandler) UserInfo20250312(ctx context.Context, c *app.RequestContext) {
	// 创建本函数的日志记录器
	userInfoLogger := utils.NewModuleLogger("UserInfo")
	// 创建请求参数结构体，绑定参数
	req := &OAuth2.UserInfoReq{}

	userInfo := &OAuth2.UserInfoResult{}
	// 检查请求中是否携带了 token 这个 key
	if err := binding.BindAndValidate(c, req); err != nil {
		// err = errorcode.ErrorCodeAuthTokenMissing
		// handler.ErrorResponse(ctx, c, err)
		userInfoLogger.CtxWarn(ctx, "请求中缺少 token: %+v\n", err)
		// 缺少 token 参数，直接返回错误
		err = errorcode.ErrorCodeAuthTokenMissing
		handler.ErrorResponse(ctx, c, err)
		return
	}

	// 这里要分别对 byted sso 和 eps sso 进行认证
	// 先尝试 byted sso
	userInfo, err := oauth2Svc.GetOauth2Service().UserInfo(ctx, req)
	if (err != nil) || (userInfo.Username == "") {
		// 认证 byted sso 失败,这里不能直接返回错误，因为可能是 eps sso 认证成功
		// 所有继续尝试 eps sso 认证
		userInfoLogger.CtxWarn(ctx, "byted sso 认证失败，尝试进行 eps sso 认证: %+v\n", err)
		// 同步删除 redis 中的 token
		keyName := "token_" + utils.Sha256(req.Token)
		redisStorage.RedisClient.Del(keyName)
		// err = errorcode.ErrorCodeAuthTokenInvalid
		// handler.ErrorResponse(ctx, c, err)
	} else {
		// byted sso 认证成功，可以返回
		userInfoLogger.CtxWarn(ctx, "byted sso 认证成功: %s\n", userInfo.Username)
		ehttp.DoResponse(ctx, c, userInfo)
		return
	}

	// 如果 byted sso 认证失败，尝试 eps sso
	epsUser, err := epsauth.Authentication(ctx, c)
	if err != nil {
		// eps sso 认证失败，到这里，byte sso 和 eps sso 认证都失败了，此时返回错误
		userInfoLogger.CtxWarn(ctx, "eps sso 认证失败 %+v\n", err)
		err = errorcode.ErrorCodeAuthTokenInvalid
		// 同步删除 redis 中的 token
		keyName := "token_" + utils.Sha256(string(c.Cookie("X-EPS-AUTH-TOKEN")))
		redisStorage.RedisClient.Del(keyName)
		handler.ErrorResponse(ctx, c, err)
		return
	} else {
		// eps sso 认证成功
		userInfoLogger.CtxWarn(ctx, "eps sso 认证成功: %s\n", epsUser.Name)
		userInfo.Email = epsUser.Email
		userInfo.Username = epsUser.Name
		ehttp.DoResponse(ctx, c, userInfo)
		return
	}

}
