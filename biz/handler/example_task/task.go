package example_task

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/statemachines"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/cloudwego/hertz/pkg/app"
	"github.com/cloudwego/hertz/pkg/common/utils"
)

type ExampleTaskHandler struct {
	ehttp.CommonActionHandler
}

func (h *ExampleTaskHandler) CreateTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &statemachines.DiagnoseRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	store := map[string]interface{}{
		"task_id": "aaaaa",
	}
	detail, err := diagnose.TriggerDiagnoseTask(ctx, statemachines.WithAsyncActivityTask(), req, store)
	handleResponse(ctx, c, detail, err)
}
func handleResponse(ctx context.Context, c *app.RequestContext, details interface{}, err error) {
	if err != nil {
		logs.CtxError(ctx, "request error: %s", err)
		c.JSON(500, utils.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(200, utils.H{
		"execution_details": details,
	})
}
