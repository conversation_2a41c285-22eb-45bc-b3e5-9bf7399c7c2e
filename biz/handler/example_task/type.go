package example_task

type DiagnoseMessage struct {
	DiagnoseTaskID     string   `json:"DiagnoseTaskID"`
	DiagnoseItemID     string   `json:"DiagnoseItemID"`
	AccountID          string   `json:"AccountID"`
	DiagnoseTemplateID string   `json:"DiagnoseTemplateID"`
	Resources          []string `json:"Resources"`
}

type DiagnoseRequest struct {
	TicketID           string              `json:"TicketID"`
	AccountID          string              `json:"AccountID"`
	DiagnoseTemplateID string              `json:"DiagnoseTemplateID"`
	DiagnoseItemID     string              `json:"DiagnoseItemID"`
	Resources          []*DiagnoseResource `json:"Resources"`
}

type DiagnoseResource struct {
	Product    string   `json:"Product"`
	SubProduct string   `json:"SubProduct"`
	InstanceID []string `json:"InstanceID"`
}
