package describe_resource

import (
	"context"
	"fmt"
	"sync"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/describe_resource"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/bytedance/gopkg/util/logger"
	"github.com/cloudwego/hertz/pkg/app"
)

// ResourceHandler 通用的资源查询接口
type ResourceHandler interface {
	GetResources(ctx context.Context, c *app.RequestContext) error
	Match(ctx context.Context, input *describe_resource.DescribeResourceRequest) bool // 标识是否支持处理该产品/维度
	GetResourcesDependency(ctx context.Context, c *app.RequestContext) error
}

type ResourceHandlerFactory struct {
	handlers sync.Map
}

var factory *ResourceHandlerFactory

var once sync.Once

// GetResourceHandler 获取资源处理器工厂单例
func NewResourceHandlerFactory() *ResourceHandlerFactory {
	once.Do(func() {
		factory = &ResourceHandlerFactory{
			handlers: sync.Map{},
		}
	})
	return factory
}

// RegisterHandler 注册资源处理器
func (f *ResourceHandlerFactory) RegisterHandler(handler ResourceHandler) {
	f.handlers.Store(handler, handler) // Handler 自身作为 key 和 value 方便查找
	logger.Infof("register resource handler: %v", handler)
}

// GetHandler 获取资源处理器
func (f *ResourceHandlerFactory) GetHandler(input *describe_resource.DescribeResourceRequest) ResourceHandler {
	var foundHandler ResourceHandler
	f.handlers.Range(func(key, value interface{}) bool {
		h, ok := key.(ResourceHandler)
		if ok && h.Match(context.Background(), input) {
			foundHandler = h
			return false // 找到第一个匹配的处理器后停止遍历
		}
		return true // 继续遍历下一个处理器
	})
	return foundHandler
}

// HandlerResource 处理资源查询请求
func (f *ResourceHandlerFactory) HandlerResource(ctx context.Context,
	c *app.RequestContext) error {
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}
	if params.Resources == nil {
		return errorcode.ErrRequestParamInvalid.WithArgs("Resources")
	}
	product := params.Resources.Product
	handler := f.GetHandler(params)
	if handler == nil {
		return errorcode.ErrRequestParamInvalid.WithArgs(fmt.Sprintf("No handler found for product: %s.", product))
	}

	// 调用GetResources获取资源信息
	return handler.GetResources(ctx, c)

}

func (f *ResourceHandlerFactory) HandlerResourceDependency(ctx context.Context,
	c *app.RequestContext) error {
	params := &describe_resource.DescribeResourceRequest{}
	err := utils.JsonUnmarshal(c.Request.Body(), params)
	if err != nil {
		return errorcode.ErrRequestParamInvalid.WithArgs(err)
	}
	if params.Resources == nil {
		return errorcode.ErrRequestParamInvalid.WithArgs("Resources")
	}
	product := params.Resources.Product
	handler := f.GetHandler(params)
	if handler == nil {
		return errorcode.ErrRequestParamInvalid.WithArgs(fmt.Sprintf("No handler found for product: %s.", product))
	}
	return handler.GetResourcesDependency(ctx, c) // 调用找到的 Handler 的 GetResource 方法
}
