package describe_resource

import (
	"context"

	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	productCategorySvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/resources"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/cmdb"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"github.com/cloudwego/hertz/pkg/app"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

var resourceHandlerFactory *ResourceHandlerFactory

func init() {
	resourceHandlerFactory = NewResourceHandlerFactory()
	//注册 ECS 资源处理器
	resourceHandlerFactory.RegisterHandler(&resources.EcsResourceHandler{
		Logger: utils.NewModuleLogger("EcsResourceHandler"),
	})
	// resourceHandlerFactory.RegisterHandler(&resources.PaaSResourceHandler{
	// 	Logger: utils.NewModuleLogger("PaaSResourceHandler"),
	// })

	resourceHandlerFactory.RegisterHandler(&cmdb.VKEClusterInstance{})
	resourceHandlerFactory.RegisterHandler(&cmdb.VkeNodePool{})
	resourceHandlerFactory.RegisterHandler(&cmdb.VkePod{})
	resourceHandlerFactory.RegisterHandler(&cmdb.VkeNode{})

	resourceHandlerFactory.RegisterHandler(&cmdb.EipInstance{})
	resourceHandlerFactory.RegisterHandler(&cmdb.CLBInstance{})
	resourceHandlerFactory.RegisterHandler(&cmdb.NatInstance{})
}

func (h *ActionHandler) GetResources20241201(ctx context.Context, c *app.RequestContext) {
	err := resourceHandlerFactory.HandlerResource(ctx, c)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
}

func (h *ActionHandler) GetResourcesDependency20241201(ctx context.Context, c *app.RequestContext) {
	err := resourceHandlerFactory.HandlerResourceDependency(ctx, c)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
}

func (h *ActionHandler) GetResourceTypes20241201(ctx context.Context, c *app.RequestContext) {
	req := &product_category.QueryProductCategoryListReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := productCategorySvc.GetProductCategoryService().QueryProductCategoryList(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)

}

func (h *ActionHandler) GetResourceTypesByIDs20241201(ctx context.Context, c *app.RequestContext) {
	req := &product_category.QueryProductCategoryListByIDsReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := productCategorySvc.GetProductCategoryService().QueryProductCategoryListByIDs(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
