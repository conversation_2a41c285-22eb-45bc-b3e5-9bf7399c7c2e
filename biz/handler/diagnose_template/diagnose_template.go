package diagnose_template

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	DiagnoseSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_template"
	diagnoseTemplateHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_template"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

var (
	defaultPageSize   int64 = 10
	defaultPageNumber int64 = 1
)

func (h *ActionHandler) RegisterDiagnoseTemplate20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseTemplateHertz.RegisterDiagnoseTemplateRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}

	if req.DiagnoseTemplateName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTemplateName "))
		return
	}

	if req.TemplateDescription == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("TemplateDescription "))
		return
	}

	if req.Suggestion == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Suggestion "))
		return
	}

	if req.StateMachineInfo == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("StateMachineInfo "))
		return
	}
	if req.ResponsiblePerson == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("ResponsiblePerson "))
		return
	}
	if req.Status == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("Status "))
		return
	}
	if req.ProductCategoryID <= 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("ProductCategoryID "))
		return
	}
	if req.ShowLevel <= 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("ShowLevel "))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseTemplateService().RegisterDiagnoseTemplate(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) DeleteDiagnoseTemplate20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseTemplateHertz.DeleteDiagnoseTemplateRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}

	if req.DiagnoseTemplateID == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTemplateID"))
		return
	}
	result, err := DiagnoseSvc.GetDiagnoseTemplateService().DeleteDiagnoseTemplate(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) ListDiagnoseTemplate20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseTemplateHertz.ListDiagnoseTemplateRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}

	if req.DiagnoseTemplateIDs == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTemplateID"))
		return
	}
	if req.PageSize == nil {
		req.PageSize = &defaultPageSize
	}
	if req.PageNumber == nil {
		req.PageNumber = &defaultPageNumber
	}
	result, err := DiagnoseSvc.GetDiagnoseTemplateService().ListDiagnoseTemplate(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) UpdateDiagnoseTemplate20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnoseTemplateHertz.UpdateDiagnoseTemplateRequest{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if req.UserInfo == nil || req.UserInfo.UserID == "" || req.UserInfo.UserName == "" {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("UserInfo"))
		return
	}

	if req.DiagnoseTemplateID == nil {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTemplateID "))
		return
	}

	result, err := DiagnoseSvc.GetDiagnoseTemplateService().UpdateDiagnoseTemplate(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
