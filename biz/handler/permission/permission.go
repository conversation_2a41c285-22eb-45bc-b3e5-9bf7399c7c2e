package permission

import (
	"context"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	permissionSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/permission"
	permissionHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) ListPermissionRoles20240801(ctx context.Context, c *app.RequestContext) {
	req := &permissionHertz.ListPermissionRolesReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	email := utils.CtxGetEmployeeEmail(ctx)
	if len(email) == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("email"))
		return
	}

	result, err := permissionSvc.GetPermissionService().GetUserRoles(ctx, email)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, &permissionHertz.PermissionRolesResult{
		RoleList: result,
	})
}

func (h *ActionHandler) GetUserPermission20240801(ctx context.Context, c *app.RequestContext) {
	req := &permissionHertz.GetUserPermissionReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	email := utils.CtxGetEmployeeEmail(ctx)
	if len(email) == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("email"))
		return
	}

	result, err := permissionSvc.GetPermissionService().GetUserPermission(ctx, email)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

func (h *ActionHandler) CheckUserPermission20240801(ctx context.Context, c *app.RequestContext) {
	req := &permissionHertz.CheckUserPermissionReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	email := utils.CtxGetEmployeeEmail(ctx)
	if len(email) == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("email"))
		return
	}

	if len(req.Rules) == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("rules"))
		return
	}

	result, err := permissionSvc.GetPermissionService().CheckPermissions(ctx, email, req.Rules)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, &permissionHertz.CheckUserPermissionResult{
		List: result,
	})
}

// func (h *ActionHandler) GetMenuPermissionConfig20241001(ctx context.Context, c *app.RequestContext) {
// 	req := &permissionHertz.GetMenuPermissionConfigReq{}
// 	if err := binding.BindAndValidate(c, req); err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return
// 	}

// 	result, err := permissionSvc.GetPermissionService().GetMenuPermissionConfig(ctx)
// 	if err != nil {
// 		handler.ErrorResponse(ctx, c, err)
// 		return
// 	}
// 	ehttp.DoResponse(ctx, c, result)
// }

// GetResources20240801 获取一批资源的权限
func (h *ActionHandler) GetResources20250417(ctx context.Context, c *app.RequestContext) {
	req := &permissionHertz.GetResourcesReq{}

	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}

	if len(req.Resources) == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("resources"))
		return
	}

	result, err := permissionSvc.GetPermissionService().GetResources(ctx, req.Resources)

	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}
