package diagnose_task_run

import (
	"context"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	diagnoseTaskRunSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) QueryDiagnoseTaskRunList20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_run.QueryDiagnoseTaskRunListReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunList(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

// DeleteDiagnoseTaskRun20241201 删除诊断任务运行记录的 handler 函数
func (h *ActionHandler) DeleteDiagnoseTaskRun20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.DeleteDiagnoseTaskRunReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的删除方法
	err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().DeleteDiagnoseTaskRun(ctx, req)
	if err != nil {
		// 若删除过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 删除成功，返回空响应
	ehttp.DoResponse(ctx, c, nil)
}

// RunDiagnoseTask20241201  运行诊断任务的 handler 函数
func (h *ActionHandler) RunDiagnoseTask20241201(ctx context.Context, c *app.RequestContext) {
	req := &diagnose_task_run.RunDiagnoseTaskReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().RunDiagnoseTask(ctx, req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunItemDetail20241201 查询诊断任务运行项详情的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunItemDetail20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunItemDetailReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询运行项详情方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItemDetail(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunDetail20241201 查询诊断任务运行详情的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunDetail20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunDetailReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询详情方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunDetail(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunCategory20241201 查询诊断任务运行分类信息的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunCategory20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunCategoryReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询分类信息方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunCategory(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunItems20241201 查询诊断任务运行项列表的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunItems20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunItemsReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询运行项列表方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunItems(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunResource20241201 查询诊断任务运行资源的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunResource20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunResourceReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询运行资源方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunResource(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}

// QueryDiagnoseTaskRunSummary20241201 查询诊断任务运行摘要信息的 handler 函数
func (h *ActionHandler) QueryDiagnoseTaskRunSummary20241201(ctx context.Context, c *app.RequestContext) {
	// 创建请求对象
	req := &diagnose_task_run.QueryDiagnoseTaskRunSummaryReq{}
	// 绑定并验证请求数据
	if err := binding.BindAndValidate(c, req); err != nil {
		// 若验证失败，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 调用服务层的查询摘要信息方法
	result, err := diagnoseTaskRunSvc.GetDiagnoseTaskRunService().QueryDiagnoseTaskRunSummary(ctx, req)
	if err != nil {
		// 若查询过程出现错误，返回错误响应
		handler.ErrorResponse(ctx, c, err)
		return
	}
	// 查询成功，返回结果响应
	ehttp.DoResponse(ctx, c, result)
}
