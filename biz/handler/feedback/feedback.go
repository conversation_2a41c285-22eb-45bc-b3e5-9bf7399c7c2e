package feedback

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	feedbackSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/feedback"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
	"context"
)

type ActionHandler struct {
	ehttp.CommonActionHandler
}

func (h *ActionHandler) SubmitDiagnoseTaskFeedback20241201(ctx context.Context, c *app.RequestContext) {
	req := &feedback.SubmitDiagnoseTaskFeedbackReq{}
	if err := binding.BindAndValidate(c, req); err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	if req.DiagnoseTaskRunID == 0 {
		handler.ErrorResponse(ctx, c, errorcode.ErrRequestParamInvalid.WithArgs("DiagnoseTaskRunID"))
		return
	}
	err := feedbackSvc.GetFeedbackService().SubmitDiagnoseTaskFeedback(ctx, *req)
	if err != nil {
		handler.ErrorResponse(ctx, c, err)
		return
	}
	ehttp.DoResponse(ctx, c, nil)
}
