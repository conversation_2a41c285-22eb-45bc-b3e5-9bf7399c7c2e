package constants

const (
	AlarmLevel_P0 = "P0"
	AlarmLevel_P1 = "P1"
	AlarmLevel_P2 = "P2"
)

const (
	ProductAlarmStatus_HasAlarm = "HasAlarm"
	ProductAlarmStatus_NoAlarm  = "NoAlarm"
)

const (
	AlarmStatus_Pending    = "Pending"
	AlarmStatus_Processing = "Processing"
	AlarmStatus_Closed     = "Closed"
	AlarmStatus_Silenced   = "Silenced"
)

const (
	AlarmHandlerStatus_Pending         = "Pending"
	AlarmHandlerStatus_ProcessingL1    = "ProcessingL1"
	AlarmHandlerStatus_TicketSubmitted = "TicketSubmitted"
	AlarmHandlerStatus_Oncall          = "Oncall"
	AlarmHandlerStatus_Closed          = "Closed"
)

var UnsolvedAlarmHandlerStatusList = []string{
	AlarmHandlerStatus_Pending,
	AlarmHandlerStatus_ProcessingL1,
	AlarmHandlerStatus_TicketSubmitted,
	AlarmHandlerStatus_Oncall,
}

const (
	AlarmBatchType_BatchTake  = "BatchTake"
	AlarmBatchType_BatchClose = "BatchClose"
)

const (
	AlarmBatchOperateType_SingleAlarm = "SingleAlarm"
	AlarmBatchOperateType_MultiAlarm  = "MultiAlarm"
)

const (
	AlarmOperateLog_SingleAlarmTake      = "SingleAlarmTake"
	AlarmOperateLog_SingleAlarmClose     = "SingleAlarmClose"
	AlarmOperateLog_MultiAlarmBatchTake  = "MultiAlarmBatchTake"
	AlarmOperateLog_MultiAlarmBatchClose = "MultiAlarmBatchClose"
)

const (
	AlarmBatchFuncType_AllMatchNoOperate   = "AllMatchNoOperate"
	AlarmBatchFuncType_AllMatchHasOperate  = "AllMatchHasOperate"
	AlarmBatchFuncType_PartMatchNoOperate  = "PartMatchNoOperate"
	AlarmBatchFuncType_PartMatchHasOperate = "PartMatchHasOperate"
)

var (
	AlarmToTicketPriorityMap = map[string]string{
		"P0": "高",
		"P1": "标准",
		"P2": "低",
	}
)
