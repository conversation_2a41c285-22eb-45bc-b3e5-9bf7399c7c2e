package constants

const (
	AlarmRuleAccessMothod_Default = "hosting"
	AlarmRuleCreateMode_Default   = "template"
	AlarmRuleSelectorMode_Default = "ref"
)

const (
	ExternalAlarmRule_Compute      = "Compute" // 计算
	ExternalAlarmRule_VNet         = "VNet"    // 网络
	ExternalAlarmRule_Storage      = "Storage" //存储
	ExternalAlarmRule_PaasVKE      = "PaasVKE" //pass
	ExternalAlarmRule_PaasCR       = "PaasCR"
	ExExternalAlarmRule_PaasVEFaas = "PaasVEFaas"
)

const (
	AlarmRuleUpgradeLevel_First  = 1
	AlarmRuleUpgradeLevel_Second = 2
	AlarmRuleUpgradeLevel_Third  = 3
)

const (
	AlarmRuleSelectorType_Ka               = "ka"
	AlarmRuleSelectorFieldKey_AccountOwner = "account_owner"
)

const (
	AlarmRuleSystemManager          = "spacex_alarm_manager"
	AlarmRuleSystemManager_ShowName = "系统账号"
)
