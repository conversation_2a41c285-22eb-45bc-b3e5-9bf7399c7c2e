package constants

// 变更单类型
const (
	ImpactTicketType_Apply     = "CHANGE_APPLY"
	ImpactTicketType_Execution = "CHANGE_EXECUTION"
)

// 变更执行状态
const (
	ImpactExecutionStatus_Running    = "running"
	ImpactExecutionStatus_Finished   = "finished"
	ImpactExecutionStatus_Failed     = "failed"
	ImpactExecutionStatus_Cancelled  = "cancelled"
	ImpactExecutionStatus_Rollbacked = "rollbacked"
	ImpactExecutionStatus_WaitStart  = "wait_start"
)

var ImpactExecutionStatus_All = []string{
	ImpactExecutionStatus_Running, ImpactExecutionStatus_Finished,
	ImpactExecutionStatus_Failed, ImpactExecutionStatus_Cancelled,
	ImpactExecutionStatus_Rollbacked,
	ImpactExecutionStatus_WaitStart,
}

// 变更通知消息的聚合维度
const (
	ImpactNotifyGroupByDimension_Customer    = "customer"
	ImpactNotifyGroupByDimension_VolcAccount = "volc_account"
)

var ImpactNotifyGroupByDimension_All = []string{ImpactNotifyGroupByDimension_Customer, ImpactNotifyGroupByDimension_VolcAccount}
