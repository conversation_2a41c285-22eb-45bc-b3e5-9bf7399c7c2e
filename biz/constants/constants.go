package constants

/*
*
display       1      1      1

	重保中   客情    TCC
*/
const (
	Display_HasGuarantee = 0b100
	Display_HasAffinity  = 0b010
	Display_HasTCC       = 0b001
)

const (
	DBName_SignalFire = "eps_signal_fire"
	DBName_Pdp        = "cloud_pdp_volc"
	DBName_Ldi        = "eps_ldi_data"
)

// 常量请维护在这里
const (
	// CtxEmployeeEmail 用户邮箱
	CtxEmployeeEmail = "x-employee-email"
	// CtxEmployeeNumber 用户工号
	CtxEmployeeNumber = "x-employee-number"
	// CtxOriginalEmployeeEmail 原始用户邮箱
	CtxOriginalEmployeeEmail = "x-original-employee-email"
	// CtxOriginalEmployeeNumber 原始用户工号
	CtxOriginalEmployeeNumber = "x-original-employee-number"
	MaxRequestLimit           = 5000
	// ServiceModeHTTP 服务运行模式http
	ServiceModeHTTP = "http"
	//ServiceModeCron 服务运行模式cronjob
	ServiceModeCron = "cron"
)

// 实体类型
const (
	EntityType_Customer = "customer"
	EntityType_Alarm    = "alarm"
	EntityType_Ticket   = "ticket"
	EntityType_Failer   = "failer"
	EntityType_Product  = "product"
	EntityType_Affnity  = "affinity"
)

const (
	StringJoinSep = ","
)
