package middleware

import (
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
	permissionSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/permission"
	permissionHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"context"
)

// 需要鉴权的接口
var permissionPaths = []string{
	"/diagnose/task",
}

// checkUserPermission 鉴权
func checkUserPermission(ctx context.Context, c *app.RequestContext) error {

	req := &permissionHertz.CheckUserPermissionReq{}

	if err := binding.BindAndValidate(c, req); err != nil {
		return err
	}

	email := utils.CtxGetEmployeeEmail(ctx)

	if len(email) == 0 {
		return errorcode.ErrRequestParamInvalid.WithArgs("email")
	}

	if len(req.Rules) == 0 {
		return errorcode.ErrRequestParamInvalid.WithArgs("rules")
	}

	if rsp, err := permissionSvc.GetPermissionService().CheckPermissions(ctx, email, req.Rules); err != nil {
		return err
	} else {
		for _, result := range rsp {
			for _, flag := range result.ActionAccessMap {
				if !flag {
					return errorcode.ErrUpStreamSystemError.WithMessage("Eps permission")
				}
			}
		}
	}

	return nil
}

func Permission() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		if !utils.SliceContainsAny([]string{c.FullPath()}, permissionPaths) {
			c.Next(ctx)
			return
		}
		if err := checkUserPermission(ctx, c); err != nil {
			handler.ErrorResponse(ctx, c, err)
			return
		}
		c.Next(ctx)
	}
}
