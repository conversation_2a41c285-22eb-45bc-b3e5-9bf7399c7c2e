package middleware

import (
	"context"
	"strings"

	"code.byted.org/gopkg/env"
	logs "code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/security/uba_sdk/databus_go"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
)

// TiReport 天穹日志上报
func TiReport() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {

		module := strings.Replace(string(c.Request.Path()), "/", "", 1)
		action, _ := c.Get<PERSON>uery("Action")
		emailPrefix := strings.Split(utils.CtxGetEmployeeEmail(ctx), "@")[0]
		employeeID := utils.CtxGetEmployeeNumber(ctx)

		err := databus_go.UBACollect(map[string]string{
			"source":      "signal_fire",
			"psm":         env.PSM(),
			"module":      module,
			"user_id":     employeeID,
			"user_name":   emailPrefix,
			"ip":          c.Client<PERSON>(),
			"ua":          string(c.UserAgent()),
			"referer":     string(c.GetHeader("referer")),
			"xff":         string(c.GetHeader("X-Forwarded-For")),
			"url":         c.URI().String(),
			"path":        string(c.Path()),
			"host":        string(c.Host()),
			"title":       "",
			"action_type": action,
			"action_desc": action,
			"extra":       string(c.Request.Body()),
		})
		if err != nil {
			logs.V1.CtxError(ctx, "report ti error %v", err)
		}
	}
}
