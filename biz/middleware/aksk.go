package middleware

import (
	appAkSkSvc "code.byted.org/volcengine-support/cloud-sherlock/biz/service/app_ak_sk"
	"context"
	"errors"
	"gorm.io/gorm"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/biz/dal"

	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/handler"
)

// AKSKAuthMiddleware 创建 AK SK 鉴权中间件
func AKSKAuthMiddleware() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		// 从请求头中获取 AK 和 SK
		ak := c.GetHeader("X-Access-Key")
		sk := c.GetHeader("X-Access-Secret")
		if len(ak) == 0 || len(sk) == 0 {
			// 处理缺少 AK 或 SK 的情况
			handler.ErrorResponse(ctx, c, errorcode.AkSkMissing)
			c.Abort()
			return
		}
		// 从数据库中查找对应的 SK
		storedSK, err := dal.FindAppAkSkByAppKey(ctx, string(ak))
		if errors.Is(err, gorm.ErrRecordNotFound) || storedSK == nil {
			// 处理记录不存在的情况
			handler.ErrorResponse(ctx, c, errorcode.AkSkInvalid)
			c.Abort()
			return
		}
		if storedSK.OutDateTime != nil && storedSK.OutDateTime.Before(time.Now()) {
			// 处理记录过期的情况
			handler.ErrorResponse(ctx, c, errorcode.AkSkOutDate)
			c.Abort()
			return
		}
		decryptedSK, err := appAkSkSvc.Decrypt(storedSK.AppSecretEn)
		if err != nil {
			handler.ErrorResponse(ctx, c, errorcode.AkSkInvalid)
			c.Abort()
			return
		}
		if string(decryptedSK) != string(sk) {
			handler.ErrorResponse(ctx, c, errorcode.AkSkInvalid)
			c.Abort()
			return
		}
		//encryptedSK, err := appAkSkSvc.Encrypt(sk)
		//if err != nil {
		//	handler.ErrorResponse(ctx, c, errorcode.AkSkInvalid)
		//	c.Abort()
		//	return
		//}
		//if encryptedSK != storedSK.AppSecretEn {
		//	handler.ErrorResponse(ctx, c, errorcode.AkSkInvalid)
		//	c.Abort()
		//	return
		//}
		c.Next(ctx)
	}
}
