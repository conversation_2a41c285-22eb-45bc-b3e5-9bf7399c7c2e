package middleware

import (
	"context"
	"strings"
	"time"

	logs "code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/compat"
	"code.byted.org/videoarch/golib/http2curl"
)

// RequestLog 打印请求日志
func RequestLog() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		begin := time.Now()
		if req, err := compat.GetCompatRequest(&c.Request); err == nil {
			if cmd, err := http2curl.GetCurlCommand(req); err == nil {
				logs.V1.CtxInfo(ctx, "%s", strings.ReplaceAll(cmd.String(), "@bytedance.com", "$$$$$$$$$"))
			}
		}
		c.Next(ctx)
		logs.V1.CtxInfo(ctx, "request finish, cost %s", time.Since(begin).String())
	}
}
