package middleware

import (
	"context"

	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"code.byted.org/eps-platform/biz_gopkg/epsauth"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"
)

func Authentication() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		allowMockUsers := make([]string, 0)
		if users, err := tcc.GetMockUserAllowList(ctx); err == nil {
			allowMockUsers = users
		}
		userInfo, err := epsauth.Authentication(ctx, c, epsauth.WithIsGetMock(true), epsauth.WithMockAllowUsers(allowMockUsers))
		if err != nil {
			ehttp.ErrorResp(ctx, c, errorcode.ErrUnAuthentication)
			return
		}

		ctx = utils.CtxSetEmployeeEmail(ctx, userInfo.Email)
		ctx = utils.CtxSetEmployeeNumber(ctx, userInfo.EmployeeNumber)
		if userInfo.OriginUserInfo != nil {
			ctx = utils.CtxSetOriginalEmployeeEmail(ctx, userInfo.OriginUserInfo.Email)
			ctx = utils.CtxSetOriginalEmployeeNumber(ctx, userInfo.OriginUserInfo.EmployeeNumber)
		}
		c.Next(ctx)
	}
}
