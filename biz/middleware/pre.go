package middleware

import (
	"context"

	"code.byted.org/middleware/hertz/pkg/app"
)

const (
	CtxAction         = "Action"
	CtxVersion        = "Version"
	CtxService        = "Service"
	CtxSourceService  = "source-service"
	CtxXSourceService = "x-source-service"
	CtxRegion         = "Region"
	CtxSource         = "Source"
)

func Pre() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		ctx = context.WithValue(ctx, CtxAction, c.Query(CtxAction))
		ctx = context.WithValue(ctx, CtxVersion, c.Query(CtxVersion))
		ctx = context.WithValue(ctx, CtxService, c.Request.Header.Get(CtxService))
		ctx = context.WithValue(ctx, CtxSourceService, c.Request.Header.Get(CtxSourceService))
		ctx = context.WithValue(ctx, CtxXSourceService, c.Request.Header.Get(CtxXSourceService))
		ctx = context.WithValue(ctx, CtxRegion, c.Request.Header.Get(CtxRegion))
		ctx = context.WithValue(ctx, CtxSource, c.Request.Header.Get(CtxSource))
		c.Next(ctx)
	}
}
