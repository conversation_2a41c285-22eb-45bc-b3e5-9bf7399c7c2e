package middleware

import (
	"context"
	"strings"

	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/errorcode"
	"code.byted.org/volcengine-support/cloud-sherlock/biz/service/permission"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	ehttp "code.byted.org/volcengine-support/gopkg/hertzext"

	"code.byted.org/middleware/hertz/pkg/app"
)

func GeneralUserRoleAdd() app.HandlerFunc {
	return func(ctx context.Context, c *app.RequestContext) {
		logger := utils.NewModuleLogger("GeneralUserRoleAdd")

		email := utils.CtxGetEmployeeEmail(ctx)
		if email != "system" {
			s := permission.GetPermissionService()
			roles, err := s.GetUserRoles(ctx, email)
			if err != nil {
				logger.CtxError(ctx, "GetUserRoles", "GetUserRoles", err)
			} else {
				signalFireRoles := slicex.Filter(roles, func(role string) bool {
					return strings.HasPrefix(role, "signal_fire_")
				})
				if len(signalFireRoles) <= 0 {
					err := permission.GetPermissionService().AddUserRole(ctx, email, "signal_fire_general_users", 4102416000000)
					if err != nil {
						logger.CtxError(ctx, "AddUserRole error:%v, email:%s", err, email)
					}
					// 通知前端重新加载页面
					ehttp.ErrorResp(ctx, c, errorcode.ReLoadingFrontEndError)
					return
				}
			}
		}
		c.Next(ctx)
	}
}
