package dal

import (
	"context"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"gorm.io/gen"
)

func DiagnoseResultGroupByDiagnoseItemIDInstanceID(ctx context.Context, diagnoseTaskRunID int64) ([]*model.DiagnoseResultV2, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	return entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID)).Group(entity.DiagnoseItemID, entity.InstanceID).Select(entity.DiagnoseItemID, entity.InstanceID).Find()
}

// DiagnoseResultsCreate 批量创建诊断结果
func DiagnoseResultsCreate(ctx context.Context, diagnoseResults []*model.DiagnoseResultV2) error {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	return entity.WithContext(ctx).CreateInBatches(diagnoseResults, 100)
}

// DiagnoseResultsGroupByLevelByTaskRunIDItemIDInstanceIDsLevels 诊断结果level分组统计
func DiagnoseResultsGroupByLevelByTaskRunIDItemIDInstanceIDsLevels(ctx context.Context, diagnoseTaskRunID, diagnoseItemID int64, instanceIDs, levels []string) ([]*DiagnoseResultLevelCountGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, entity.DiagnoseItemID.Eq(diagnoseItemID))
	conditions = append(conditions, entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID))
	if len(instanceIDs) > 0 {
		conditions = append(conditions, entity.InstanceID.In(instanceIDs...))
	}
	if len(levels) > 0 {
		conditions = append(conditions, entity.DiagnoseResultLevel.In(levels...))
	}
	result := make([]*DiagnoseResultLevelCountGroupBy, 0)
	err := entity.WithContext(ctx).Where(conditions...).Group(entity.DiagnoseResultLevel).Select(entity.DiagnoseResultLevel, entity.DiagnoseResultLevel.Count().As("count")).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

//V2版本

// DiagnoseResultsGroupByLevelByDiagnoseTaskRunID 通过诊断任务运行ID对level分组统计
func DiagnoseResultsGroupByLevelByDiagnoseTaskRunID(ctx context.Context, diagnoseTaskRunID int64) ([]*DiagnoseResultLevelCountGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	result := make([]*DiagnoseResultLevelCountGroupBy, 0)
	err := entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID)).Group(entity.DiagnoseResultLevel).Select(entity.DiagnoseResultLevel,
		entity.DiagnoseResultLevel.Count().As("count")).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func DiagnoseResultsGroupByLevelByDiagnoseTaskRunIDProductCategoryID(ctx context.Context, diagnoseTaskRunID, productCategoryID int64) ([]*DiagnoseResultLevelCountGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	result := make([]*DiagnoseResultLevelCountGroupBy, 0)
	err := entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID), entity.ProductCategoryID.Eq(productCategoryID)).Group(entity.DiagnoseResultLevel).Select(entity.DiagnoseResultLevel,
		entity.DiagnoseResultLevel.Count().As("count")).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID 对ProduceCategoryID分组
func DiagnoseResultsGroupByProductCategoryIDByDiagnoseTaskRunID(ctx context.Context, diagnoseTaskRunID int64) ([]*DiagnoseResultProductCategoryIDGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	result := make([]*DiagnoseResultProductCategoryIDGroupBy, 0)
	err := entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID)).Group(entity.ProductCategoryID).Select(entity.ProductCategoryID).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DiagnoseResultsGroupByLevelByTaskRunIDItemID 通过任务运行ID诊断项ID对level分组统计
func DiagnoseResultsGroupByLevelByTaskRunIDItemID(ctx context.Context, diagnoseTaskRunID, diagnoseItemID int64) ([]*DiagnoseResultLevelCountGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	result := make([]*DiagnoseResultLevelCountGroupBy, 0)
	err := entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID),
		entity.DiagnoseItemID.Eq(diagnoseItemID)).Group(entity.DiagnoseResultLevel).Select(entity.DiagnoseResultLevel,
		entity.DiagnoseResultLevel.Count().As("count")).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func DiagnoseResultsGroupByTaskRunIDsByLevelProductCategoryID(ctx context.Context, taskRunIDs, productCategoryIDs []int64,
	levels []string) ([]*DiagnoseResultTaskRunIDGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	result := make([]*DiagnoseResultTaskRunIDGroupBy, 0)
	conditions := make([]gen.Condition, 0)
	if len(taskRunIDs) > 0 {
		conditions = append(conditions, entity.DiagnoseTaskRunID.In(taskRunIDs...))
	}
	if len(levels) > 0 {
		conditions = append(conditions, entity.DiagnoseResultLevel.In(levels...))
	}
	if len(productCategoryIDs) > 0 {
		conditions = append(conditions, entity.ProductCategoryID.In(productCategoryIDs...))
	}
	err := entity.WithContext(ctx).Where(conditions...).Group(entity.DiagnoseTaskRunID).Select(entity.DiagnoseTaskRunID).Scan(&result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DiagnoseResultsFindByConSortByLevel 通过任务运行ID、诊断项ID、实例ID列表和诊断结果等级列表查询诊断结果
func DiagnoseResultsFindByConSortByLevel(ctx context.Context, diagnoseTaskRunID, diagnoseItemID int64, instanceIDs,
	diagnoseResultLevels []string, pageNumber, pageSize int) ([]*model.DiagnoseResultV2, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2

	conditions := make([]gen.Condition, 0)
	conditions = append(conditions, entity.DiagnoseItemID.Eq(diagnoseItemID))
	conditions = append(conditions, entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID))
	// 新增instanceIDs过滤条件，如果instanceIDs不为空，则添加到查询条件中
	if len(instanceIDs) > 0 {
		conditions = append(conditions, entity.InstanceID.In(instanceIDs...))
	}
	if len(diagnoseResultLevels) > 0 {
		conditions = append(conditions, entity.DiagnoseResultLevel.In(diagnoseResultLevels...))
	}
	return entity.WithContext(ctx).Where(conditions...).Order(entity.DiagnoseResultLevelNum.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
}

func DiagnoseResultsFindByDiagnoseTaskRunID(ctx context.Context, diagnoseTaskRunID int64) ([]*model.DiagnoseResultV2, error) {
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	return entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID)).Find()
}

func DiagnoseResultsFindByConditionsSortByLevelNum(ctx context.Context, filter *model.DiagnoseResultV2, levels []string) ([]*model.DiagnoseResultV2, error) {
	conditions := make([]gen.Condition, 0)
	if filter.DiagnoseTaskRunID != 0 {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.DiagnoseTaskRunID.Eq(filter.DiagnoseTaskRunID))
	}
	if filter.DiagnoseItemID != 0 {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.DiagnoseItemID.Eq(filter.DiagnoseItemID))
	}
	if filter.InstanceID != "" {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.InstanceID.Eq(filter.InstanceID))
	}
	if len(levels) > 0 {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.DiagnoseResultLevel.In(levels...))
	}
	if filter.Status != nil {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.Status.Eq(*filter.Status))
	}
	if filter.ID != 0 {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.ID.Eq(filter.ID))
	}
	if filter.ProductCategoryID != 0 {
		conditions = append(conditions, rds.QueryCloudSherlock.DiagnoseResultV2.ProductCategoryID.Eq(filter.ProductCategoryID))
	}
	entity := rds.QueryCloudSherlock.DiagnoseResultV2
	return entity.WithContext(ctx).Where(conditions...).Order(entity.DiagnoseResultLevelNum.Desc()).Find()
}
