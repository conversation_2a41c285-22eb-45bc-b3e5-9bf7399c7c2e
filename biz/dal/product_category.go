package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

// QueryProductCategoryList 查询产品类型列表
func QueryProductCategoryList(ctx context.Context, productCategory *model.ProductCategory) ([]*model.ProductCategory, error) {
	entity := rds.QueryCloudSherlock.ProductCategory
	conditions := make([]gen.Condition, 0)
	if productCategory.ID != 0 {
		conditions = append(conditions, entity.ID.Eq(productCategory.ID))
	}
	if productCategory.Product != "" {
		conditions = append(conditions, entity.Product.Eq(productCategory.Product))
	}
	if productCategory.SubProduct != "" {
		conditions = append(conditions, entity.SubProduct.Eq(productCategory.SubProduct))
	}
	if productCategory.ResourceType != "" {
		conditions = append(conditions, entity.ResourceType.Eq(productCategory.ResourceType))
	}
	if productCategory.ProductCn != "" {
		conditions = append(conditions, entity.ProductCn.Eq(productCategory.ProductCn))
	}
	if productCategory.SubProductCn != "" {
		conditions = append(conditions, entity.SubProductCn.Eq(productCategory.SubProductCn))
	}
	return entity.WithContext(ctx).Where(conditions...).Find()
}

// QueryProductCategoryByID 通过ID查询产品类型
func QueryProductCategoryByID(ctx context.Context, id int64) (*model.ProductCategory, error) {
	entity := rds.QueryCloudSherlock.ProductCategory
	return entity.WithContext(ctx).Where(entity.ID.Eq(id)).First()
}

// ProductCategoryFindByIDs 通过ID列表查询产品类型
func ProductCategoryFindByIDs(ctx context.Context, ids []int64) ([]*model.ProductCategory, error) {
	entity := rds.QueryCloudSherlock.ProductCategory
	return entity.WithContext(ctx).Where(entity.ID.In(ids...)).Find()
}

// QueryProductCategoryIDByResourceType 通过ResourceType查询到对应的ID
func QueryProductCategoryIDByResourceType(ctx context.Context, ResourceType string) (int64, error) {
	entity := rds.QueryCloudSherlock.ProductCategory
	productCategory, err := entity.WithContext(ctx).Where(entity.ResourceType.Eq(ResourceType)).First()
	if err != nil {
		return 0, err
	}
	return productCategory.ID, nil
}
