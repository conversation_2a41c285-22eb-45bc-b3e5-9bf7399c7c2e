package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
)

func AddItemTemplateInBatches(ctx context.Context, itemTemplate []*model.ItemTemplate) error {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).CreateInBatches(itemTemplate, len(itemTemplate))
}

func AddItemTemplate(ctx context.Context, itemTemplate *model.ItemTemplate) error {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).Create(itemTemplate)
}

func FindRelationByItemIDAndTemplateID(ctx context.Context, itemId, templateID int64) (*model.ItemTemplate, error) {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(itemId)).
		Where(entity.DiagnoseTemplateID.Eq(templateID)).First()
}

func UpdateItemTemplate(ctx context.Context, itemTemplate *model.ItemTemplate) error {
	entity := rds.QueryCloudSherlock.ItemTemplate
	_, err := entity.WithContext(ctx).Updates(itemTemplate)
	return err
}

func FindRelationByTemplateID(ctx context.Context, templateId int64) ([]*model.ItemTemplate, error) {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).Where(entity.DiagnoseTemplateID.Eq(templateId)).Find()
}

func FindRelationByItemID(ctx context.Context, itemId int64) ([]*model.ItemTemplate, error) {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(itemId)).Find()
}

func SaveItemTemplate(ctx context.Context, itemTemplate *model.ItemTemplate) error {
	entity := rds.QueryCloudSherlock.ItemTemplate
	return entity.WithContext(ctx).Save(itemTemplate)
}

//func FindActivityDiagnoseItemsByTemplateID(ctx context.Context, templateId int64) ([]*diagnoseTemplateHertz.ActivityDiagnose, error) {
//	entity := rds.QueryCloudSherlock.ItemTemplate
//	relations, err := entity.WithContext(ctx).Where(entity.DiagnoseTemplateID.Eq(templateId)).Find()
//	if err != nil {
//		return nil, err
//	}
//	itemIds := make([]int64, 0)
//	acDiagnosesMap := make(map[string][]int64)
//	activityDiagnoses := make([]*diagnoseTemplateHertz.ActivityDiagnose, 0)
//	for _, relation := range relations {
//		itemIds = append(itemIds, relation.DiagnoseItemID)
//		acDiagnosesMap[*relation.Activity] = append(acDiagnosesMap[*relation.Activity], relation.DiagnoseItemID)
//	}
//	for ac, ids := range acDiagnosesMap {
//		activityDiagnoses = append(activityDiagnoses, &diagnoseTemplateHertz.ActivityDiagnose{
//			Activity:        ac,
//			DiagnoseItemIDs: ids,
//		})
//	}
//	return activityDiagnoses, err
//
//}
