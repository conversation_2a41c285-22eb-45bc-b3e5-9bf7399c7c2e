package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
	"time"
)

// QueryDiagnoseTaskRunStatusByID 查询任务状态 link diagnose_task_run.go
func QueryDiagnoseTaskRunStatusByID(ctx context.Context, id *int64) (string, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	taskRun, err := entity.WithContext(ctx).Select(entity.Status).Where(entity.ID.Eq(*id)).First()
	if err != nil {
		return "", err
	}
	return *taskRun.Status, nil
}

func FindActiveSession(ctx context.Context, userId string) (*model.DiscourseSession, error) {
	entity := rds.QueryCloudSherlock.DiscourseSession
	return entity.WithContext(ctx).Omit(entity.Title).Where(entity.UserID.Eq(userId)).Where(entity.IsHistory.Not()).First()
}

func UpdateDiscourseHistory(ctx context.Context, userId string) error {
	entity := rds.QueryCloudSherlock.DiscourseSession
	if _, err := entity.WithContext(ctx).Where(entity.UserID.Eq(userId), entity.IsHistory.Not()).UpdateColumn(entity.IsHistory, true); err != nil {
		return err
	}
	return nil
}

func HistoryDiscourseList(ctx context.Context, userId string, pageNumber, pageSize int) ([]*model.DiscourseSession, int64, error) {
	entity := rds.QueryCloudSherlock.DiscourseSession
	page, total, err := entity.WithContext(ctx).
		Select(entity.SessionID, entity.Title, entity.StartDate).
		Where(entity.UserID.Eq(userId), entity.IsHistory.Not()).
		// 一个月内
		Where(entity.StartDate.Gt(time.Now().Add(-30*24*time.Hour))).
		Order(entity.StartDate.Desc()).
		FindByPage((pageNumber-1)*pageSize, pageSize)
	if err != nil {
		return nil, 0, err
	}
	return page, total, nil
}

// QueryDiscourseSessionByID 根据 id查询 session
func QueryDiscourseSessionByID(ctx context.Context, id int64) (*model.DiscourseSession, error) {
	entity := rds.QueryCloudSherlock.DiscourseSession
	return entity.WithContext(ctx).Where(entity.ID.Eq(id)).First()
}

func QueryLatestDiscourseSessionByUUID(ctx context.Context, userId, uuid string) (*model.DiscourseSession, error) {
	entity := rds.QueryCloudSherlock.DiscourseSession
	return entity.WithContext(ctx).Where(entity.UserID.Eq(userId), entity.SessionID.Eq(uuid)).Order(entity.StartDate.Desc()).First()
}

func CreateDiscourseSession(ctx context.Context, session *model.DiscourseSession) error {
	entity := rds.QueryCloudSherlock.DiscourseSession
	return entity.WithContext(ctx).Create(session)
}

func UpdateDiscourseSession(ctx context.Context, session *model.DiscourseSession) error {
	entity := rds.QueryCloudSherlock.DiscourseSession
	_, err := entity.WithContext(ctx).Where(entity.SessionID.Eq(session.SessionID)).Updates(session)
	return err
}

// CreateDiscourseMessage 创建 message
func CreateDiscourseMessage(ctx context.Context, message *model.DiscourseMessage) error {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Create(message)
}

// UpdateDiscourseMessage 更新 message
func UpdateDiscourseMessage(ctx context.Context, message *model.DiscourseMessage) error {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	_, err := entity.WithContext(ctx).Where(entity.TaskRunID.Eq(*message.TaskRunID)).Updates(message)
	return err
}

// UpdateAgentDiscourseMessage 更新 自建 agent
func UpdateAgentDiscourseMessage(ctx context.Context, message *model.DiscourseMessage) error {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(message.ID)).Updates(message)
	return err
}

// QueryDiscourseMessageSummary 查询ai 总结
func QueryDiscourseMessageSummary(ctx context.Context, taskRunID int64) (*model.DiscourseMessage, error) {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Where(entity.TaskRunID.Eq(taskRunID)).First()
}

// QueryDiscourseMessageBySessionID 根据 session id 查询
func QueryDiscourseMessageBySessionID(ctx context.Context, sessionID int64) ([]*model.DiscourseMessage, error) {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Where(entity.SessionID.Eq(sessionID)).Order(entity.StartDate.Desc()).Find()
}

func QueryDiscourseMessageByTaskRunID(ctx context.Context, taskID int64) (*model.DiscourseMessage, error) {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Where(entity.TaskRunID.Eq(taskID)).First()
}

func QueryDiscourseSessionByTaskRunID(ctx context.Context, taskRunID int64) (*model.DiscourseSession, error) {
	entity := rds.QueryCloudSherlock.DiscourseSession
	rEntity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Join(rEntity, entity.ID.EqCol(rEntity.SessionID)).Where(rEntity.TaskRunID.Eq(taskRunID)).First()
}

func QuerySessionRunningTask(ctx context.Context, sessionID int64) (*model.DiagnoseTaskRun, error) {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	runEntity := rds.QueryCloudSherlock.DiagnoseTaskRun
	return runEntity.WithContext(ctx).Join(entity, runEntity.ID.EqCol(entity.TaskRunID)).Where(entity.SessionID.Eq(sessionID)).Order(entity.StartDate.Desc()).First()
}

// QueryLatestDiagnoseSummary 查询当前 session下最新的总结信息
func QueryLatestDiagnoseSummary(ctx context.Context, sessionID int64) (*model.DiscourseMessage, error) {
	entity := rds.QueryCloudSherlock.DiscourseMessage
	return entity.WithContext(ctx).Where(entity.SessionID.Eq(sessionID)).Where(entity.Summary.IsNotNull()).Order(entity.StartDate.Desc()).First()
}

func CreateDiscourseFeedBack(ctx context.Context, req *model.DiscourseFeeback) error {
	entity := rds.QueryCloudSherlock.DiscourseFeeback
	return entity.WithContext(ctx).Create(req)
}

func UpdateDiscourseFeeback(ctx context.Context, req *model.DiscourseFeeback) (gen.ResultInfo, error) {
	entity := rds.QueryCloudSherlock.DiscourseFeeback
	return entity.WithContext(ctx).Where(entity.ID.Eq(req.ID), entity.Type.Eq(req.Type)).UpdateColumn(entity.Upvote, req.Upvote)
}

func QueryDiscourseFeedback(ctx context.Context, sessionId string, messageId string) (*model.DiscourseFeeback, error) {
	entity := rds.QueryCloudSherlock.DiscourseFeeback
	return entity.WithContext(ctx).Where(entity.SessionID.Eq(sessionId), entity.MessageID.Eq(messageId)).Order(entity.CreateTime.Desc()).First()
}
