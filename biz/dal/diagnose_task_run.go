package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"gorm.io/gen"
)

const (
	updateDiagnoseTaskRunLock = "updateDiagnoseTaskRunLock"
	updateLockDuration        = 10 * time.Second
)

// QueryDiagnoseTaskRunList 查询诊断任务运行列表
func QueryDiagnoseTaskRunList(ctx context.Context, diagnoseTaskRun *model.DiagnoseTaskRun, templateIds, ids, productCategoryIds []int64,
	levels []string, ascSortByEndTime bool, pageNumber, pageSize int) ([]*model.DiagnoseTaskRun, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	conditions := make([]gen.Condition, 0)
	if len(ids) == 1 {
		conditions = append(conditions, entity.ID.Eq(ids[0]))
	} else {
		if diagnoseTaskRun.DiagnoseTaskID != 0 {
			conditions = append(conditions, entity.DiagnoseTaskID.Eq(diagnoseTaskRun.DiagnoseTaskID))
		}
		if diagnoseTaskRun.Status != nil {
			conditions = append(conditions, entity.Status.Eq(*diagnoseTaskRun.Status))
		}
		if diagnoseTaskRun.Origin != 0 {
			conditions = append(conditions, entity.Origin.Eq(diagnoseTaskRun.Origin))
		}
		if len(templateIds) > 0 {
			conditions = append(conditions, entity.DiagnoseTemplateID.In(templateIds...))
		}
		if diagnoseTaskRun.TicketID != nil {
			conditions = append(conditions, entity.TicketID.Eq(*diagnoseTaskRun.TicketID))
		}
		if diagnoseTaskRun.Name != nil {
			conditions = append(conditions, entity.Name.Like("%"+*diagnoseTaskRun.Name+"%"))
		}
		if diagnoseTaskRun.StartTime != nil {
			conditions = append(conditions, entity.StartTime.Gte(*diagnoseTaskRun.StartTime))
		}
		if diagnoseTaskRun.EndTime != nil {
			conditions = append(conditions, entity.EndTime.Lte(*diagnoseTaskRun.EndTime))
		}
		if len(ids) > 0 {
			conditions = append(conditions, entity.ID.In(ids...))
		}

		//level和productCategoryId字段不在任务运行表中，需要通过结果表查询
		if len(levels) > 0 || len(productCategoryIds) > 0 {
			//通过conditions查询出taskRunIDs
			runIds := make([]int64, 0)
			if len(conditions) > 0 {
				runIDsGroupBy, err := DiagnoseTaskRunIDsGroupByIDsByCondition(ctx, conditions)
				if err != nil {
					return nil, 0, err
				}
				for _, runIDGroupBy := range runIDsGroupBy {
					runIds = append(runIds, runIDGroupBy.ID)
				}
			}
			taskRunIDGroupBys, err := DiagnoseResultsGroupByTaskRunIDsByLevelProductCategoryID(ctx, runIds, productCategoryIds, levels)
			if err != nil {
				return nil, 0, err
			}
			taskRunIDs := make([]int64, 0)
			for _, taskRunIDGroupBy := range taskRunIDGroupBys {
				taskRunIDs = append(taskRunIDs, taskRunIDGroupBy.DiagnoseTaskRunID)
			}
			conditions = make([]gen.Condition, 0)
			conditions = append(conditions, entity.ID.In(taskRunIDs...))
		}
	}

	if ascSortByEndTime {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.EndTime.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	return entity.WithContext(ctx).Where(conditions...).Order(entity.EndTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
}

func DiagnoseTaskRunIDsGroupByIDsByCondition(ctx context.Context, conditions []gen.Condition) ([]*IDGroupBy, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	Ids := make([]*IDGroupBy, 0)
	err := entity.WithContext(ctx).Where(conditions...).Group(entity.ID).Select(entity.ID).Scan(&Ids)
	if err != nil {
		return nil, err
	}
	return Ids, nil
}

// QueryDiagnoseTaskRunListByTaskID 通过任务ID查询诊断任务运行列表
func QueryDiagnoseTaskRunListByTaskID(ctx context.Context, diagnoseTaskID int64) ([]*model.DiagnoseTaskRun, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	return entity.WithContext(ctx).Where(entity.DiagnoseTaskID.Eq(diagnoseTaskID)).Find()
}

// QueryDiagnoseTaskRunListByTaskIDs 通过任务ID查询诊断任务运行列表
func QueryDiagnoseTaskRunListByTaskIDs(ctx context.Context, diagnoseTaskIDs []int64) ([]*model.DiagnoseTaskRun, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	return entity.WithContext(ctx).Where(entity.DiagnoseTaskID.In(diagnoseTaskIDs...)).Find()
}

// QueryDiagnoseTaskRunListByTaskIDsSortByEndTime 通过任务ID查询诊断任务运行列表
func QueryDiagnoseTaskRunListByTaskIDsSortByEndTime(ctx context.Context, diagnoseTaskIDs []int64) ([]*model.DiagnoseTaskRun, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	return entity.WithContext(ctx).Where(entity.DiagnoseTaskID.In(diagnoseTaskIDs...)).Order(entity.EndTime.Desc()).Find()
}

// DeleteDiagnoseTaskRunByID 根据 ID 删除诊断任务运行记录
func DeleteDiagnoseTaskRunByID(ctx context.Context, id int64) error {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	// 执行删除操作，根据 ID 找到对应的记录并删除
	result, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	// 检查是否有记录被删除
	if result.RowsAffected == 0 {
		return fmt.Errorf("no diagnose task run record deleted for ID: %d", id)
	}
	return nil
}

// QueryDiagnoseTaskRunByID 根据 ID 查询诊断任务运行记录
func QueryDiagnoseTaskRunByID(ctx context.Context, id int64) (*model.DiagnoseTaskRun, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	// 执行查询操作，根据 ID 找到对应的记录
	diagnoseTaskRun, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).First()
	if err != nil {
		return nil, err
	}
	return diagnoseTaskRun, nil
}

// CreateDiagnoseTaskRun 创建诊断任务运行记录
func CreateDiagnoseTaskRun(ctx context.Context, diagnoseTaskRun *model.DiagnoseTaskRun) error {
	entity := rds.QueryCloudSherlock.DiagnoseTaskRun
	// 执行插入操作
	err := entity.WithContext(ctx).Create(diagnoseTaskRun)
	if err != nil {
		return err
	}
	return nil
}

// UpdateDiagnoseTaskRunStatus 更新诊断任务运行状态
func UpdateDiagnoseTaskRunStatus(ctx context.Context, taskRunID int64, taskRunStatus *string) error {
	if err := getUpdateLock(taskRunID); err != nil {
		return err
	}
	defer deleteUpdateLock(taskRunID)
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskRun
		// 执行更新操作，根据 taskRunID 找到对应的记录并更新巡检项状态
		_, err := entity.WithContext(ctx).Where(entity.ID.Eq(taskRunID)).UpdateColumn(entity.Status, taskRunStatus)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
func UpdateDiagnoseTaskRunEndTimeStatus(ctx context.Context, taskRunID int64, endTime time.Time, taskRunStatus *string) error {
	if err := getUpdateLock(taskRunID); err != nil {
		return err
	}
	defer deleteUpdateLock(taskRunID)
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskRun
		// 执行更新操作，根据 taskRunID 找到对应的记录并更新巡检项状态
		_, err := entity.WithContext(ctx).Where(entity.ID.Eq(taskRunID)).UpdateColumn(entity.Status, taskRunStatus)
		if err != nil {
			return err
		}
		_, err = entity.WithContext(ctx).Where(entity.ID.Eq(taskRunID)).UpdateColumn(entity.EndTime, endTime)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func UpdateDiagnoseTaskRunItemStatus(ctx context.Context, taskRunID int64, diagnoseItemStatus *diagnose_task_run.DiagnoseItemStatus) error {
	if err := getUpdateLock(taskRunID); err != nil {
		return err
	}
	defer deleteUpdateLock(taskRunID)
	//通过taskRunID查询诊断任务运行记录
	taskRun, err := QueryDiagnoseTaskRunByID(ctx, taskRunID)
	if err != nil {
		return err
	}
	if taskRun == nil {
		return fmt.Errorf("taskRun is nil")
	}
	oldItemStatusString := taskRun.DiagnoseItemStatus
	// 若旧的状态字符串为空，创建一个新的状态映射
	if oldItemStatusString == nil {
		return fmt.Errorf("oldItemStatusString is nil")
	}
	if diagnoseItemStatus == nil || diagnoseItemStatus.DiagnoseItem == nil || diagnoseItemStatus.DiagnoseItem.Id == 0 {
		return fmt.Errorf("diagnoseItemStatus.DiagnoseItem.Id is nil")
	}
	// 若旧的状态字符串不为空，将其解析为状态映射
	statusMap := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)
	err = json.Unmarshal([]byte(*oldItemStatusString), &statusMap)
	if err != nil {
		return err
	}
	if diagnoseItemStatus.Status != "" {
		statusMap[diagnoseItemStatus.DiagnoseItem.Id].Status = diagnoseItemStatus.Status
	}
	if diagnoseItemStatus.DiagnoseResultLevel != nil {
		statusMap[diagnoseItemStatus.DiagnoseItem.Id].DiagnoseResultLevel = diagnoseItemStatus.DiagnoseResultLevel
	}
	if diagnoseItemStatus.StartTime != nil {
		statusMap[diagnoseItemStatus.DiagnoseItem.Id].StartTime = diagnoseItemStatus.StartTime
	}
	if diagnoseItemStatus.EndTime != nil {
		statusMap[diagnoseItemStatus.DiagnoseItem.Id].EndTime = diagnoseItemStatus.EndTime
	}
	// 将更新后的状态映射转换为 JSON 字符串
	jsonBytes, err := json.Marshal(statusMap)
	if err != nil {
		return err
	}
	err = rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskRun
		// 执行更新操作，根据 taskRunID 找到对应的记录并更新巡检项状态
		_, err = entity.WithContext(ctx).Where(entity.ID.Eq(taskRunID)).UpdateColumn(entity.DiagnoseItemStatus, string(jsonBytes))
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil

}

// UpdateDiagnoseTaskRunItemStatusInBatches 批量更新不同行的同一个字段的数据
func UpdateDiagnoseTaskRunItemStatusInBatches(ctx context.Context, taskRunID int64, diagnoseItemStatuses []*diagnose_task_run.DiagnoseItemStatus) error {
	if err := getUpdateLock(taskRunID); err != nil {
		return err
	}
	defer deleteUpdateLock(taskRunID)
	//通过taskRunID查询诊断任务运行记录
	taskRun, err := QueryDiagnoseTaskRunByID(ctx, taskRunID)
	if err != nil {
		return err
	}
	if taskRun == nil {
		return fmt.Errorf("taskRun is nil")
	}
	oldItemStatusString := taskRun.DiagnoseItemStatus
	// 若旧的状态字符串为空，创建一个新的状态映射
	if oldItemStatusString == nil {
		return fmt.Errorf("oldItemStatusString is nil")
	}
	if len(diagnoseItemStatuses) == 0 {
		return fmt.Errorf("diagnoseItemStatus is nil")
	}
	// 若旧的状态字符串不为空，将其解析为状态映射
	statusMap := make(map[int64]*diagnose_task_run.DiagnoseItemStatus)
	err = json.Unmarshal([]byte(*oldItemStatusString), &statusMap)
	if err != nil {
		return err
	}
	for _, diagnoseItemStatus := range diagnoseItemStatuses {
		if diagnoseItemStatus == nil || diagnoseItemStatus.DiagnoseItem == nil || diagnoseItemStatus.DiagnoseItem.Id == 0 {
			return fmt.Errorf("diagnoseItemStatus.DiagnoseItem.Id is nil")
		}
		if diagnoseItemStatus.Status != "" {
			statusMap[diagnoseItemStatus.DiagnoseItem.Id].Status = diagnoseItemStatus.Status
		}
		if diagnoseItemStatus.StartTime != nil {
			statusMap[diagnoseItemStatus.DiagnoseItem.Id].StartTime = diagnoseItemStatus.StartTime
		}
		if diagnoseItemStatus.EndTime != nil {
			statusMap[diagnoseItemStatus.DiagnoseItem.Id].EndTime = diagnoseItemStatus.EndTime
		}
		if diagnoseItemStatus.DiagnoseResultLevel != nil {
			statusMap[diagnoseItemStatus.DiagnoseItem.Id].DiagnoseResultLevel = diagnoseItemStatus.DiagnoseResultLevel
		}
	}
	// 将更新后的状态映射转换为 JSON 字符串
	jsonBytes, err := json.Marshal(statusMap)
	if err != nil {
		return err
	}
	err = rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskRun
		// 执行更新操作，根据 taskRunID 找到对应的记录并更新巡检项状态
		_, err = entity.WithContext(ctx).Where(entity.ID.Eq(taskRunID)).UpdateColumn(entity.DiagnoseItemStatus, string(jsonBytes))
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil

}

// DiagnoseTaskRunUpdateFeedbackIDByID
func DiagnoseTaskRunUpdateFeedbackIDByID(ctx context.Context, id int64, feedbackID *int64) error {
	if err := getUpdateLock(id); err != nil {
		return err
	}
	defer deleteUpdateLock(id)
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskRun
		// 执行更新操作，根据 taskRunID 找到对应的记录并更新巡检项状态
		_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).UpdateColumn(entity.FeedbackID, feedbackID)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func getKey(id int64) string {
	return fmt.Sprintf("%s:%v", updateDiagnoseTaskRunLock, id)
}

func getUpdateLock(id int64) error {
	t1 := time.Now().Unix()
	for {
		l, e := rds_redis.GetLock(getKey(id), updateLockDuration)
		if e != nil {
			return e
		}
		if l {
			break
		}
		time.Sleep(time.Millisecond * 10)
		if time.Now().Unix()-t1 > 180 {
			return errors.New("获取更新diagnoseTaskRun表的锁超过3分钟")
		}
	}
	return nil
}

func deleteUpdateLock(id int64) {
	rds_redis.DeleteLock(getKey(id))
}
