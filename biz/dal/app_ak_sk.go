package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

// FindAppAkSkByAppKey 通过app_key查询app_ak_sk
func FindAppAkSkByAppKey(ctx context.Context, appKey string) (*model.AppAkSk, error) {
	entity := rds.QueryCloudSherlock.AppAkSk
	return entity.WithContext(ctx).Where(entity.AppKey.Eq(appKey)).First()
}

// CreateAppAkSk 创建app_ak_sk
func CreateAppAkSk(ctx context.Context, appAkSk *model.AppAkSk) error {
	entity := rds.QueryCloudSherlock.AppAkSk
	return entity.WithContext(ctx).Create(appAkSk)
}

func FindAppAkSkByAppID(ctx context.Context, appID string) (*model.AppAkSk, error) {
	entity := rds.QueryCloudSherlock.AppAkSk
	return entity.WithContext(ctx).Where(entity.AppID.Eq(appID)).First()
}

func UpdateAppAkSk(ctx context.Context, appAkSk *model.AppAkSk) error {
	entity := rds.QueryCloudSherlock.AppAkSk
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(appAkSk.ID)).Updates(appAkSk)
	if err != nil {
		return err
	}
	return nil
}

func QueryAppAkSkList(ctx context.Context, appAkSk *model.AppAkSk, pageNumber, pageSize int) ([]*model.AppAkSk, int64, error) {
	entity := rds.QueryCloudSherlock.AppAkSk
	conditions := make([]gen.Condition, 0)
	if appAkSk.AppID != "" {
		conditions = append(conditions, entity.AppID.Eq(appAkSk.AppID))
	}
	if appAkSk.AppName != nil {
		conditions = append(conditions, entity.AppName.Eq(*appAkSk.AppName))
	}
	if appAkSk.AppKey != "" {
		conditions = append(conditions, entity.AppKey.Eq(appAkSk.AppKey))
	}
	if appAkSk.OutDateTime != nil {
		conditions = append(conditions, entity.OutDateTime.Lte(*appAkSk.OutDateTime))
	}
	return entity.WithContext(ctx).Where(conditions...).FindByPage((pageNumber-1)*pageSize, pageSize)
}

func DeleteAppAkSk(ctx context.Context, appAkSk *model.AppAkSk) error {
	entity := rds.QueryCloudSherlock.AppAkSk
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(appAkSk.ID)).Delete()
	if err != nil {
		return err
	}
	return nil
}
