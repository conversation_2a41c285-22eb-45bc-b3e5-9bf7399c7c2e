package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	diagnoseItemHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_item"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
)

func AddDiagnoseItem(ctx context.Context, item *model.DiagnoseItem) error {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Create(item)
}

func ListDiagnoseItem(ctx context.Context, pageNumber, pageSize int, req *diagnoseItemHertz.ListDiagnoseItemRequest) ([]*model.DiagnoseItem, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	if len(req.DiagnoseItemIDs) > 0 {
		return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull()).
			Where(entity.DiagnoseItemID.In(req.DiagnoseItemIDs...)).
			FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	conditions := make([]gen.Condition, 0)
	if req.DiagnoseItemName != nil && *req.DiagnoseItemName != "" {
		conditions = append(conditions, entity.DiagnoseItemName.Like("%"+*req.DiagnoseItemName+"%"))
	}
	if req.Status != nil && *req.Status != "" {
		conditions = append(conditions, entity.Status.Eq(*req.Status))
	}
	if req.ProductCategoryIDs != nil && len(req.ProductCategoryIDs) != 0 {
		conditions = append(conditions, entity.ProductCategoryID.In(req.ProductCategoryIDs...))
	}
	if req.AccessResponsiblePerson != nil && *req.AccessResponsiblePerson != "" {
		conditions = append(conditions, entity.AccessResponsiblePerson.Eq("%"+*req.AccessResponsiblePerson+"%"))
	}
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull()).Where(conditions...).
		FindByPage((pageNumber-1)*pageSize, pageSize)
}
func UpdateDiagnoseItem(ctx context.Context, item *model.DiagnoseItem) error {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	_, err := entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(item.DiagnoseItemID)).Updates(item)
	if err != nil {
		return err
	}
	return nil
}

func UpdateDiagnoseItemName(ctx context.Context, item *model.DiagnoseItem, templates []*model.DiagnoseTemplate) error {
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		entity := rds.QueryCloudSherlock.DiagnoseItem
		_, err := entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(item.DiagnoseItemID)).Updates(item)
		if err != nil {
			return err
		}
		err = BatchUpdateDifferentDiagnoseTemplates(ctx, templates)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func FindDiagnoseItemByID(ctx context.Context, diagnoseItemID int64) (*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).
		Where(entity.DiagnoseItemID.Eq(diagnoseItemID)).First()
}
func FindDiagnoseItemByCode(ctx context.Context, diagnoseItemCode string) (*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).
		Where(entity.DiagnoseItemCode.Eq(diagnoseItemCode)).First()
}

func FindDiagnoseItemByName(ctx context.Context, diagnoseItemName string) (*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).
		Where(entity.DiagnoseItemName.Eq(diagnoseItemName)).First()
}

func DeleteDiagnoseItem(ctx context.Context, diagnoseItemID int64) error {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	_, err := entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(diagnoseItemID)).Delete()
	if err != nil {
		return err
	}
	return nil
}

func DiagnoseItemFindByIDs(ctx context.Context, diagnoseItemIds []int64) ([]*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull(), entity.DiagnoseItemID.In(diagnoseItemIds...)).Find()
}

func DiagnoseItemFindByID(ctx context.Context, diagnoseItemId int64) (*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull(), entity.DiagnoseItemID.Eq(diagnoseItemId)).First()
}

func SaveDiagnoseItem(ctx context.Context, item *model.DiagnoseItem) error {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Where(entity.DiagnoseItemID.Eq(item.DiagnoseItemID)).Save(item)
}

func GetAllDiagnoseItem(ctx context.Context) ([]*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull()).
		Find()
}

// FilterDiagnoseItemsByProductCategoryID 通过产品Id过滤出对应的诊断项
func FilterDiagnoseItemsByProductCategoryID(ctx context.Context, productCategoryID int64, DiagnoseItemIDs []int64) ([]*model.DiagnoseItem, error) {
	entity := rds.QueryCloudSherlock.DiagnoseItem
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull(), entity.DiagnoseItemID.In(DiagnoseItemIDs...), entity.ProductCategoryID.Eq(productCategoryID)).
		Find()
}
