package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
)

func FeedbackFindByDiagnoseTaskID(ctx context.Context, diagnoseTaskID int64) (*model.Feedback, error) {
	entity := rds.QueryCloudSherlock.Feedback
	feedback, err := entity.WithContext(ctx).Where(entity.DiagnoseTaskID.Eq(diagnoseTaskID)).First()
	if err != nil {
		return nil, err
	}
	return feedback, nil
}
func FeedbackFindByDiagnoseTaskRunID(ctx context.Context, diagnoseTaskRunID int64) (*model.Feedback, error) {
	entity := rds.QueryCloudSherlock.Feedback
	feedback, err := entity.WithContext(ctx).Where(entity.DiagnoseTaskRunID.Eq(diagnoseTaskRunID)).First()
	if err != nil {
		return nil, err
	}
	return feedback, nil
}

func CreateFeedback(ctx context.Context, feedback *model.Feedback) error {
	entity := rds.QueryCloudSherlock.Feedback
	err := entity.WithContext(ctx).Create(feedback)
	if err != nil {
		return err
	}
	return nil
}

func SaveFeedback(ctx context.Context, feedback *model.Feedback) error {
	entity := rds.QueryCloudSherlock.Feedback
	err := entity.WithContext(ctx).Save(feedback)
	if err != nil {
		return err
	}
	return nil
}
