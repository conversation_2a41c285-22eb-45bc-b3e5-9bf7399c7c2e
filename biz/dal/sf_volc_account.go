package dal

// func FindSfVolcAccountByConditions(ctx context.Context, conditions []gen.Condition) ([]*model.SfVolcanoAccount, error) {
// 	entity := rds.QueryCloudSherlock.SfVolcanoAccount
// 	result, err := entity.WithContext(ctx).Where(conditions...).Find()
// 	if err != nil {
// 		logs.CtxError(ctx, "[dal/FindSfVolcAccountByConditions] err:%v", err)
// 		return nil, err
// 	}
// 	return result, nil
// }

// func FindSfVolcAccountRelatedCustomer(ctx context.Context, volcAccountId string) (*model.Customer, error) {
// 	volcanoAccountList, err := FindSfVolcAccountByConditions(ctx, []gen.Condition{
// 		rds.QueryCloudSherlock.Customer.CustomerNumber.Eq(volcAccountId),
// 	})
// 	if err != nil {
// 		logs.CtxError(ctx, "[dal/FindSfVolcAccountRelatedCustomer] FindSfVolcAccountByConditions err:%v", err)
// 		return nil, errorcode.ErrDatabaseError.WithArgs(constants.DBName_SignalFire)
// 	}
// 	if len(volcanoAccountList) == 0 || volcanoAccountList[0] == nil || len(volcanoAccountList[0].AccountC) == 0 {
// 		logs.CtxWarn(ctx, "[dal/FindSfVolcAccountRelatedCustomer] volcanoAccountList is invalid, volcAccountId:%s", volcAccountId)
// 		return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("VolcanoAccount[%s]", volcAccountId))
// 	}
// 	customerList, err := FindCustomerByConditions(ctx,
// 		[]gen.Condition{
// 			rds.QuerySignalFire.Customer.SfID.Eq(volcanoAccountList[0].AccountC),
// 		},
// 	)
// 	if err != nil {
// 		logs.CtxError(ctx, "[dal/FindSfVolcAccountRelatedCustomer] FindCustomer err:%v", err)
// 		return nil, errorcode.ErrDatabaseError.WithArgs(constants.DBName_SignalFire)
// 	}
// 	if len(customerList) == 0 || customerList[0] == nil {
// 		logs.CtxWarn(ctx, "[dal/FindSfVolcAccountRelatedCustomer] customerList is invalid, volcAccountId:%s", volcAccountId)
// 		return nil, errorcode.ErrDataNotFound.WithArgs(fmt.Sprintf("VolcanoAccount[%s] related Customer", volcAccountId))
// 	}

// 	return customerList[0], nil
// }

// func FindCustomerRelatedSfVolcAccount(ctx context.Context, cutomerNumber string) ([]*model.SfVolcanoAccount, error) {
// 	customerEntity := rds.QuerySignalFire.Customer
// 	sfVolcanoAccountentity := rds.QuerySignalFire.SfVolcanoAccount

// 	volcanoAccountList, err := sfVolcanoAccountentity.WithContext(ctx).
// 		Join(customerEntity, sfVolcanoAccountentity.AccountC.EqCol(customerEntity.SfID)).
// 		Where(customerEntity.CustomerNumber.Eq(cutomerNumber)).Find()

// 	if err != nil {
// 		logs.CtxError(ctx, "[dal/FindCustomerRelatedSfVolcAccount] FindSfVolcAccount err:%v", err)
// 		return nil, errorcode.ErrDatabaseError.WithArgs(constants.DBName_SignalFire)
// 	}
// 	return volcanoAccountList, nil
// }

// func FindSfVolcAccountRelatedCustomerList(ctx context.Context, volcAccountIds []string) ([]*model.Customer, error) {
// 	customerEntity := rds.QuerySignalFire.Customer
// 	sfVolcanoAccountentity := rds.QuerySignalFire.SfVolcanoAccount

// 	customerList, err := customerEntity.WithContext(ctx).
// 		Join(sfVolcanoAccountentity, sfVolcanoAccountentity.AccountC.EqCol(customerEntity.SfID)).
// 		Where(sfVolcanoAccountentity.VolcanoAccountIDC.In(volcAccountIds...)).Find()

// 	if err != nil {
// 		logs.CtxError(ctx, "[dal/FindCustomerRelatedSfVolcAccount] FindSfVolcAccount err:%v", err)
// 		return nil, errorcode.ErrDatabaseError.WithArgs(constants.DBName_SignalFire)
// 	}
// 	return customerList, nil
// }
