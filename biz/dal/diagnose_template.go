package dal

import (
	diagnoseTemplateHertz "code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_template"
	"context"
	"gorm.io/gen"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
)

func AddDiagnoseTemplate(ctx context.Context, template *model.DiagnoseTemplate) error {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	return entity.WithContext(ctx).Create(template)
}

func DeleteDiagnoseTemplate(ctx context.Context, template *model.DiagnoseTemplate) error {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	_, err := entity.WithContext(ctx).Delete(template)
	return err
}

func GetStatusMachineInfoByTemplateID(ctx context.Context, templateID int64) (*string, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	template, err := entity.WithContext(ctx).Select(entity.StateMachineInfo).Where(entity.DiagnoseTemplateID.Eq(templateID)).First()
	if err != nil {
		return nil, err
	} else {
		return template.StateMachineInfo, nil
	}
}

func DiagnoseTemplateFindByID(ctx context.Context, id int64) (*model.DiagnoseTemplate, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	return entity.WithContext(ctx).Where(entity.DiagnoseTemplateID.Eq(id)).First()
}
func FindDiagnoseTemplateByName(ctx context.Context, templateName string) (*model.DiagnoseTemplate, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	template, err := entity.WithContext(ctx).Where(entity.DiagnoseTemplateName.Eq(templateName)).First()
	if err != nil {
		return nil, err
	} else {
		return template, nil
	}
}
func UpdateDiagnoseTemplate(ctx context.Context, template *model.DiagnoseTemplate) error {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	_, err := entity.WithContext(ctx).Where(entity.DiagnoseTemplateID.Eq(template.DiagnoseTemplateID)).Updates(template)
	return err
}

func SaveDiagnoseTemplate(ctx context.Context, template *model.DiagnoseTemplate) error {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	err := entity.WithContext(ctx).Where(entity.DiagnoseTemplateID.Eq(template.DiagnoseTemplateID)).Save(template)
	return err
}
func ListDiagnoseTemplate(ctx context.Context, pageNumber, pageSize int, req *diagnoseTemplateHertz.ListDiagnoseTemplateRequest) ([]*model.DiagnoseTemplate, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	if len(req.DiagnoseTemplateIDs) > 0 {
		return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull()).
			Where(entity.DiagnoseTemplateID.In(req.DiagnoseTemplateIDs...)).
			FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	conditions := make([]gen.Condition, 0)
	if req.DiagnoseTemplateName != nil && *req.DiagnoseTemplateName != "" {
		conditions = append(conditions, entity.DiagnoseTemplateName.Like("%"+*req.DiagnoseTemplateName+"%"))
	}
	if req.Status != nil && *req.Status != "" {
		conditions = append(conditions, entity.Status.Eq(*req.Status))
	}
	if req.ResponsiblePerson != nil && *req.ResponsiblePerson != "" {
		conditions = append(conditions, entity.ResponsiblePerson.Like("%"+*req.ResponsiblePerson+"%"))
	}
	if req.ProductCategoryIDs != nil && len(req.ProductCategoryIDs) != 0 {
		conditions = append(conditions, entity.ProductCategoryID.In(req.ProductCategoryIDs...))
	}
	return entity.WithContext(ctx).Where(entity.DeleteAt.IsNull()).Where(conditions...).Order(entity.ShowLevel).
		FindByPage((pageNumber-1)*pageSize, pageSize)
}

func FindDiagnoseTemplateByID(ctx context.Context, diagnoseTemplateID int64) (*model.DiagnoseTemplate, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTemplate
	return entity.WithContext(ctx).
		Where(entity.DiagnoseTemplateID.Eq(diagnoseTemplateID)).First()
}

func BatchUpdateDifferentDiagnoseTemplates(ctx context.Context, templates []*model.DiagnoseTemplate) error {
	return rds.Transaction(ctx, func(ctx context.Context) error {
		entity := rds.QueryCloudSherlock.DiagnoseTemplate
		for _, template := range templates {
			_, err := entity.WithContext(ctx).
				Where(entity.DiagnoseTemplateID.Eq(template.DiagnoseTemplateID)).
				Updates(template)
			if err != nil {
				return err
			}
		}
		return nil
	})
}
