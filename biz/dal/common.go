package dal

const (
	DiagnoseResultStatusSucceed = "succeed"
	DiagnoseResultStatusFailed  = "failed"
)

type DiagnoseResultLevelCountGroupBy struct {
	DiagnoseResultLevel string `json:"diagnose_result_level"`
	Count               int64  `json:"count"`
}

type DiagnoseResultItemGroupBy struct {
	DiagnoseItemID int64 `json:"diagnose_item_id"`
	Count          int64 `json:"count"`
}

type DiagnoseResultProductCategoryIDGroupBy struct {
	ProductCategoryID int64 `json:"product_category_id"`
}

type DiagnoseResultTaskRunIDGroupBy struct {
	DiagnoseTaskRunID int64 `json:"diagnose_task_run_id"`
}

type IDGroupBy struct {
	ID int64 `json:"id"`
}
