package dal

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds"
	"context"
	"gorm.io/gen"
	"time"
)

func DiagnoseTaskV2sFindByConditionSortByLastRunTime(ctx context.Context, diagnoseTaskV2 *model.DiagnoseTaskV2, pageNumber, pageSize int, asc bool) ([]*model.DiagnoseTaskV2, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	conditions := make([]gen.Condition, 0)
	if diagnoseTaskV2.Name != nil {
		conditions = append(conditions, entity.Name.Like("%"+*diagnoseTaskV2.Name+"%"))
	}
	if diagnoseTaskV2.DiagnoseTemplateID != nil {
		conditions = append(conditions, entity.DiagnoseTemplateID.Eq(*diagnoseTaskV2.DiagnoseTemplateID))
	}
	if diagnoseTaskV2.Origin != nil {
		conditions = append(conditions, entity.Origin.Eq(*diagnoseTaskV2.Origin))
	}
	if asc {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.LastRunTime.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	return entity.WithContext(ctx).Where(conditions...).Order(entity.LastRunTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
}

func DiagnoseTaskV2sFindByConditionSortByCreateTime(ctx context.Context, diagnoseTaskV2 *model.DiagnoseTaskV2, pageNumber, pageSize int, asc bool) ([]*model.DiagnoseTaskV2, int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	conditions := make([]gen.Condition, 0)
	if diagnoseTaskV2.Name != nil {
		conditions = append(conditions, entity.Name.Like("%"+*diagnoseTaskV2.Name+"%"))
	}
	if diagnoseTaskV2.DiagnoseTemplateID != nil {
		conditions = append(conditions, entity.DiagnoseTemplateID.Eq(*diagnoseTaskV2.DiagnoseTemplateID))
	}
	if diagnoseTaskV2.Origin != nil {
		conditions = append(conditions, entity.Origin.Eq(*diagnoseTaskV2.Origin))
	}
	if asc {
		return entity.WithContext(ctx).Where(conditions...).Order(entity.CreateTime.Asc()).FindByPage((pageNumber-1)*pageSize, pageSize)
	}
	return entity.WithContext(ctx).Where(conditions...).Order(entity.CreateTime.Desc()).FindByPage((pageNumber-1)*pageSize, pageSize)
}

// DeleteDiagnoseTaskV2ByID 通过ID删除诊断任务
func DeleteDiagnoseTaskV2ByID(ctx context.Context, id int64) error {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).Delete()
	if err != nil {
		return err
	}
	return nil
}

// DiagnoseTaskV2FindByID 通过ID查询诊断任务
func DiagnoseTaskV2FindByID(ctx context.Context, id int64) (*model.DiagnoseTaskV2, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	return entity.WithContext(ctx).Where(entity.ID.Eq(id)).First()
}

// CreateDiagnoseTaskV2 创建诊断任务
func CreateDiagnoseTaskV2(ctx context.Context, diagnoseTaskV2 *model.DiagnoseTaskV2) error {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	err := entity.WithContext(ctx).Create(diagnoseTaskV2)
	if err != nil {
		return err
	}
	return nil
}

// UpdateDiagnoseTaskV2 通过传入的诊断任务对象更新数据库中的记录
func UpdateDiagnoseTaskV2(ctx context.Context, diagnoseTaskV2 *model.DiagnoseTaskV2) error {
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskV2
		// 执行更新操作，将传入对象的所有字段更新到数据库中
		_, err := entity.WithContext(ctx).Where(entity.ID.Eq(diagnoseTaskV2.ID)).Updates(diagnoseTaskV2)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// QueryDiagnoseTaskV2ListByTicketID 通过TicketID查询诊断任务
func QueryDiagnoseTaskV2ListByTicketID(ctx context.Context, ticketID string) ([]*model.DiagnoseTaskV2, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	return entity.WithContext(ctx).Where(entity.TicketID.Eq(ticketID)).Find()
}

// UpdateDiagnoseTaskV2LastRunTimeByID 通过ID更新最近一次运行时间
func UpdateDiagnoseTaskV2LastRunTimeByID(ctx context.Context, id int64, lastRunTime *time.Time) error {
	err := rds.Transaction(ctx, func(ctx context.Context) error {
		// 更新任务运行item状态字段
		entity := rds.QueryCloudSherlock.DiagnoseTaskV2
		// 执行更新操作，将传入对象的所有字段更新到数据库中
		_, err := entity.WithContext(ctx).Where(entity.ID.Eq(id)).Update(entity.LastRunTime, lastRunTime)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func QueryDiagTaskV2ByDiagnoseTemplateID(ctx context.Context, diagnoseTemplateID int64) ([]int64, error) {
	entity := rds.QueryCloudSherlock.DiagnoseTaskV2
	diagnoseTasks, err := entity.WithContext(ctx).Select(entity.ID).Where(entity.DiagnoseTemplateID.Eq(diagnoseTemplateID)).Find()
	if err != nil {
		return nil, err
	}
	diagnoseTemplateIDs := make([]int64, 0)
	for _, diagnoseTask := range diagnoseTasks {
		if diagnoseTask.DiagnoseItemIds == nil {
			continue
		}
		diagnoseTemplateIDs = append(diagnoseTemplateIDs, *diagnoseTask.DiagnoseTemplateID)
	}
	return diagnoseTemplateIDs, nil
}
