package test

import (
	"encoding/json"
	"io"
	"net/http"
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
	"github.com/stretchr/testify/assert"
)

// 测试直接使用前端传过来的token，利用 oauth2 第三方库获取用户信息
func TestOauth2GetUserInfo(t *testing.T) {
	// 这里使用的是测试环境的token，由postman 生成
	token := "445dc37d88aff767aa046fce5c1ceb08cda8ed11"
	// 创建 http 客户端
	client := &http.Client{}
	req, err := http.NewRequest("GET", sso.Conf.UserInfoURL, nil)
	if err != nil {
		t.Errorf("new request failed, err: %v", err)
		return
	}
	req.Header.Set("Authorization", "Bearer "+token)
	q := req.URL.Query()
	// q.Add("username", "conans.wang")
	req.URL.RawQuery = q.Encode()
	resp, err := client.Do(req)
	if err != nil {
		t.Errorf("get userinfo failed, err: %v", err)
		return
	}
	defer resp.Body.Close()
	bs, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Errorf("read userinfo failed, err: %v", err)
		return
	}

	// 定义部门结构体
	type department struct {
		Name   string `json:"name,omitempty"`
		EnName string `json:"en_name,omitempty"`
	}

	// 定义用户信息结构体
	type UserInfo struct {
		Name           string     `json:"name,omitempty"`
		Email          string     `json:"email,omitempty"`
		Username       string     `json:"username,omitempty"`
		EmployeeID     string     `json:"employee_id,omitempty"`
		NickName       string     `json:"nickname,omitempty"`
		Picture        string     `json:"picture,omitempty"`
		EmployeeNumber int64      `json:"employee_number,omitempty"`
		TenantAlias    string     `json:"tenant_alias,omitempty"`
		Department     department `json:"department,omitempty"`
	}
	// 反序列
	var userInfo UserInfo
	if err := json.Unmarshal(bs, &userInfo); err != nil {
		t.Errorf("unmarshal userinfo failed, err: %v", err)
		return
	}

	assert := assert.New(t)
	assert.Equal(userInfo.Name, "conans.wang", "they should be equal")
	assert.Equal(userInfo.Email, "<EMAIL>", "they should be equal")
	assert.Equal(userInfo.EmployeeID, "8502739", "they should be equal")
}

// 测试能否从tcc 获取 sso env 的内容
func TestGetSSOEnv(t *testing.T) {
	ssoEnv := tcc.GetServiceConfig().SSOEnv.Env
	assert := assert.New(t)
	assert.NotEmpty(ssoEnv, "sso env should not be empty")
	if ssoEnv == "prod" || ssoEnv == "boe" {
		return
	} else {
		t.Errorf("sso env should be prod or boe, but got %s", ssoEnv)
	}
}
