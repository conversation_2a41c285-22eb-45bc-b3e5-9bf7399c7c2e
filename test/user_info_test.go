package test

import (
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/oauth2"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/utils"

	"github.com/stretchr/testify/assert"
)

func TestStoreToRedis(t *testing.T) {
	// 构造测试数据
	example_token := "2ecc40003d3aeed4cf8f826b81907d7d0e8f0561"
	u := sso.NewUserInfo()
	u.Name = "conans.wang"
	u.Email = "<EMAIL>"
	u.Username = "conans.wang"
	u.NickName = "王殿"
	u.Picture = "https://s3-imfile.feishucdn.com/static-resource/v1/v3_00cc_a08b617f-fec9-4bcd-80dc-7ad9578e591g~?image_size=240x240\u0026cut_type=\u0026quality=\u0026format=png\u0026sticker_format=.webp"
	u.EmployeeNumber = 8502739
	u.TenantAlias = "bytedance"
	u.Department = &oauth2.Department{
		Name:   "企业合作与解决方案-技术服务-客户技术支持",
		EnName: "Enterprise Partnership and Solution-Technical Service-Customer Technical Support",
	}
	// 创建redis客户端
	client, err := NewRedisClient(t)
	if err != nil {
		t.Errorf("NewRedisClient() error = %v", err)
		return
	}
	// 将redis客户端赋值给u.RedisClient
	u.RedisClient = client

	// 调用 StoreToRedis 方法, 这里使用固定
	expireNanoSecond := int64(6e10)
	err = u.StoreToRedis("token_"+utils.Sha256(example_token), expireNanoSecond)
	if err != nil {
		t.Errorf("u.StoreToRedis() error = %v", err)
		return
	}
	// 从 redis中获取用户信息
	u_copy, err := sso.GetUserInfoByTokenFromRedis(example_token)
	if err != nil {
		t.Errorf("middleWare.GetUserInfoByTokenFromRedis() error = %v", err)
		return
	}

	// 检查用户信息是否一致
	assert := assert.New(t)
	assert.Equal(u.Name, u_copy.Name, "Name should be equal")
	assert.Equal(u.Email, u_copy.Email, "Email should be equal")
	assert.Equal(u.Username, u_copy.Username, "Username should be equal")
	assert.Equal(u.NickName, u_copy.NickName, "NickName should be equal")
	assert.Equal(u.Picture, u_copy.Picture, "Picture should be equal")
	assert.Equal(u.EmployeeNumber, u_copy.EmployeeNumber, "EmployeeNumber should be equal")
	assert.Equal(u.TenantAlias, u_copy.TenantAlias, "TenantAlias should be equal")
	assert.Equal(u.Department, u_copy.Department, "Department should be equal")

}
