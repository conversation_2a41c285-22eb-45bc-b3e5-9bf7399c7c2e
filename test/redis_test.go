package test

import (
	"fmt"
	"testing"
)

// 本文件用于测试使用内部 sdk 连接 redis

func TestNewRedisClient(t *testing.T) {
	client, err := NewRedisClient(t)
	if err != nil {
		t.Errorf("new redis client error: %v", err)
		return
	}

	// Notice: Please do not call client.Client directly, this can cause undefined error, cause we have many wrapper over the v6.Client strct
	// 请注意： 使用 goredis New 出来的 client，不要试图访问 client 内嵌的匿名 Client 对象，由于上层有大量 wrapper 函数，非预期的函数调用可能导致设计之外的情况出现
	ret, err := client.Del("key").Result()
	fmt.Printf("result = %d, error = %v\n", ret, err)
	if err != nil {
		t.Errorf("del key error: %v", err)
		return
	}

	setRet, err := client.Set("key", "value", 0).Result()
	fmt.Printf("result = %s, error = %v\n", setRet, err)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("set key error: %v", err)
		return
	}

	getRet, err := client.Get("key").Result() // 得到的ret是一个 *StringCmd 可以进一步操作
	fmt.Printf("result = %s, error = %v\n", getRet, err)
	if err != nil {
		t.Errorf("get key error: %v", err)
		return
	}
}
