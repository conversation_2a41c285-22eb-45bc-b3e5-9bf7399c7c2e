package test

import (
	"testing"

	"code.byted.org/volcengine-support/cloud-sherlock/utils"
	"github.com/stretchr/testify/assert"
)

// 测试AES加密和解密
func TestEncryption(t *testing.T) {
	assert := assert.New(t)

	plainText := "wangdian"
	cipherText, err := utils.AesCrypto(plainText)
	if err != nil {
		t.Error(err)
	}

	plainTextCopy, err := utils.AesDecrypto(cipherText)
	if err != nil {
		t.Error(err)
	}
	assert.Equal(plainText, string(plainTextCopy), "they should be equal")
}

// 测试base64编码和解码
func TestBase64(t *testing.T) {
	assert := assert.New(t)
	src := "wangdian"
	encoded := utils.Base64Encode([]byte(src))

	decoded, err := utils.Base64Decode(encoded)
	if err != nil {
		t.Error(err)
	}

	assert.Equal(src, string(decoded), "they should be equal")
}
