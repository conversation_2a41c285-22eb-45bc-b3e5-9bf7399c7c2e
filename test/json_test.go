package test

import (
	"encoding/json"
	"testing"

	"code.byted.org/kv/goredis"
)

func TestJson(t *testing.T) {
	type Department struct {
		Name   string
		EnName string
	}

	type UserInfo struct {
		Name           string      `json:"name,omitempty"`
		Email          string      `json:"email,omitempty"`
		Username       string      `json:"username,omitempty"`
		EmployeeID     string      `json:"employee_id,omitempty"`
		NickName       string      `json:"nickname,omitempty"`
		Picture        string      `json:"picture,omitempty"`
		EmployeeNumber int64       `json:"employee_number,omitempty"`
		TenantAlias    string      `json:"tenant_alias,omitempty"`
		Department     *Department `json:"department,omitempty"`

		// redis 客户端连接对象
		RedisClient *goredis.Client
	}
	string1 := "{\"code\":401,\"message\":\"token 过期\",\"error\":\"expired_token\"}"
	u := UserInfo{}
	err := json.Unmarshal([]byte(string1), &u)
	t.Log("err is:", err)

}
