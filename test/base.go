package test

// 本文件包含一些供其他测试函数使用的基本函数

import (
	"testing"

	"code.byted.org/kv/goredis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/rds_redis"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/sso"
	"code.byted.org/volcengine-support/cloud-sherlock/pkg/tcc"
)

func init() {
	// 初始化 tcc
	tcc.InitServiceConfig()
	// 初始化 redis
	rds_redis.InitRedis()
	// 初始化 sso 配置
	sso.InitSSOconfig()
}

// 创建一个新的redis客户端
func NewRedisClient(t *testing.T) (*goredis.Client, error) {
	// 从 tcc 中获取redis 的 PSM
	cluster := tcc.GetServiceConfig().CloudSherlockRedis.PSM

	if cluster == "" {
		t.Errorf("REDIS_CLUSTER is empty")
		return nil, nil
	}
	// init option
	options := goredis.NewOption()

	// init connect
	client, err := goredis.NewClientWithOption(cluster, options)
	// client, err := goredis.NewClientWithServers(cluster, []string{"127.0.0.1"}, options)
	if err != nil {
		t.Errorf("goredis.NewClientWithOption error = %v", err)
		return nil, err
	}

	// 测试 redis 连接
	_, err = client.Ping().Result()
	if err != nil {
		t.Errorf("redisClient.Ping() error = %v", err)
		return nil, err
	}
	return client, nil
}
