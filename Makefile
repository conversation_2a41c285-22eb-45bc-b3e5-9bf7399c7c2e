PKG := "code.byted.org/volcengine-support/cloud-sherlock"
PSM := "volcengine.support.cloud_sherlock"
IDL := "idl/service.thrift"
TESTFLAG=-p=1 -gcflags=all=-l
.PHONY:gen
.PHONY:build

gen:
	go install code.byted.org/middleware/hertztool/v3@latest
	rm -rf gen/gorm

	go run gen/tools/gorm_generate.go
	rm -rf gen/kitex

	hertztool model --idl $(IDL) --model_dir ./gen/hertz

gen_hertz:
	go install code.byted.org/middleware/hertztool/v3@latest
	hertztool --version
	hertztool model --idl $(IDL) --model_dir ./gen/hertz

build:
	go mod tidy
	./build.sh

run:
	go mod tidy
	./build.sh
	./output/bootstrap.sh

clean:
	@rm -rf log output conf/kitex_remote_config.json

push:
	go mod tidy
	./build.sh
	@rm -rf log output conf/kitex_remote_config.json
	git push
