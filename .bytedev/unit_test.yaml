unit_test:
  coverage_threshold: #覆盖率卡口配置(local+远端)
    include_files: [ "biz/service/**", "utils/**" ]
    exclude_files: [ "utils/log.go", "utils/transaction.go"]
    line_percent: 0
    diff_percent: 70
    diff:
      default:
        line_limit: 10 # 增量行数少于多少行时，默认置成功
        minimum_coverage: "0%"
  go:
    commands:
      - chmod +x .bytedev/caseanalysis_download.sh
      - .bytedev/caseanalysis_download.sh
      - go test ./... -run $(./codeanalysis -testcase-only -assertable-only -all -output-way console) -gcflags=all=-l -coverpkg=./... -coverprofile=coverage.out
  runner:
    envs:
      CI_RUNNER_ROOT_PATH: "/home/<USER>"



