#!/bin/bash
# caseanalysis_download.sh：根据系统类型下载分析工具并授权执行，兼容本地与CI执行
if [[ ! -f "codeanalysis" ]]; then
  if [[ `uname` == 'Linux' ]]; then
    wget http://tosv.byted.org/obj/bytecheck-plugin-tos/code-analysis/codeanalysis_linux -O codeanalysis
  elif [[ `uname` == 'Darwin' ]]; then
    wget http://tosv.byted.org/obj/bytecheck-plugin-tos/code-analysis/codeanalysis_mac -O codeanalysis
  fi
  chmod +x codeanalysis
fi