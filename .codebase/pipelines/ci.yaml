name: ci pipelines
trigger: [ change ]
jobs:
  job_1:
    name: unit_test
    image: hub.byted.org/codebase/ci_go_1_22
    envs:
      init_db_client: "docker"
    runs-on:
      env: boe
    steps:
      - id: consul
        uses: actions/setup-consul
      - id: doas
        uses: actions/doas
        inputs:
          psm: "eps.platform.signal_fire"
      - commands:
          - echo $SEC_TOKEN_STRING # 通过环境变量获取 GDPR token
          - echo ${{Steps["doas"].Outputs["token"]}} # 通过表达式获取 GDPR token
          #- go test $(go list ./... | grep -v /pkg/... | grep -v /biz/service/approval) -gcflags=all=-l -coverprofile=coverage.out
          # 获取单测覆盖率
      - id: codecov
        uses: actions/codecov
        inputs:
          files: "coverage.out"
          fail_ci_if_error: false