package main

import (
	"os"
	"strings"

	"code.byted.org/gopkg/lang/v2/stringx"

	"code.byted.org/gorm/bytedgorm"
	"gorm.io/gen"
	"gorm.io/gorm"

	"code.byted.org/gorm/bytedgen"
)

func main() {
	//genCloudSherlock()
	genFeedback()
	genDiagnoseResult()
	genDiagnoseTask()
	// genLdi()
	// genPdp()
}

func genCloudSherlock() {
	// 指定生成代码的具体(相对)目录，默认为：./query
	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

	currentPath, err := os.Getwd()
	print(currentPath)

	conf := gen.Config{
		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
		OutPath:      currentPath + "/gen/gorm/query",
		ModelPkgPath: currentPath + "/gen/gorm/model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

		FieldNullable:     true,  // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
	}

	conf.WithImportPkgPath(
		// 产生的model自动import soft_delete
		"gorm.io/plugin/soft_delete",
		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model",
	)

	g := bytedgen.NewGenerator(conf)
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
	})

	DB, err := gorm.Open(
		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
		bytedgorm.MySQL("toutiao.mysql.cloud_sherlock" /*数据库PSM*/, "cloud_sherlock" /*数据库名*/).WithReadReplicas(),
		bytedgorm.WithDefaults(),
	)

	if err != nil {
		panic(err)
	}

	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
	g.UseDB(DB)

	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
		if strings.Contains(tableName, "_del") {
			return ""
		}
		return tableName
	})

	// 为指定的结构体或表格生成基础CRUD查询方法
	models := g.GenerateAllTable(
		// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
		gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
		// 忽略 deleted_at 字段
		gen.FieldIgnore("deleted_at"),
	)
	for _, i := range models {
		g.ApplyBasic(i)
	}

	// 执行并生成代码
	g.Execute()
}

// func genLdi() {
// 	// 指定生成代码的具体(相对)目录，默认为：./query
// 	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

// 	currentPath, err := os.Getwd()
// 	print(currentPath)

// 	conf := gen.Config{
// 		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
// 		OutPath:      currentPath + "/gen/gorm/ldi_query",
// 		ModelPkgPath: currentPath + "/gen/gorm/ldi_model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

// 		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

// 		FieldNullable:     false, // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
// 		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
// 		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
// 	}

// 	conf.WithImportPkgPath(
// 		// 产生的model自动import soft_delete
// 		"gorm.io/plugin/soft_delete",
// 		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/ldi_model",
// 	)

// 	g := bytedgen.NewGenerator(conf)
// 	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
// 		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
// 	})

// 	DB, err := gorm.Open(
// 		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
// 		bytedgorm.MySQL("toutiao.mysql.eps_ldi_data" /*数据库PSM*/, "eps_ldi_data" /*数据库名*/).WithReadReplicas(),
// 		bytedgorm.WithDefaults(),
// 	)

// 	if err != nil {
// 		panic(err)
// 	}

// 	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
// 	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
// 	g.UseDB(DB)
// 	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
// 		if strings.Contains(tableName, "_del") {
// 			return ""
// 		}
// 		return tableName
// 	})

// 	// 为指定的结构体或表格生成基础CRUD查询方法
// 	//models := g.GenerateAllTable(
// 	//	// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
// 	//	gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
// 	//	// 忽略 deleted_at 字段
// 	//	gen.FieldIgnore("deleted_at"),
// 	//)
// 	//for _, i := range models {
// 	//	g.ApplyBasic(i)
// 	//}
// 	g.ApplyBasic(g.GenerateModel("account_service_team"))
// 	// 执行并生成代码
// 	g.Execute()
// }

// func genPdp() {
// 	// 指定生成代码的具体(相对)目录，默认为：./query
// 	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

// 	currentPath, err := os.Getwd()
// 	print(currentPath)

// 	conf := gen.Config{
// 		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
// 		OutPath:      currentPath + "/gen/gorm/pdp_query",
// 		ModelPkgPath: currentPath + "/gen/gorm/pdp_model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

// 		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

// 		FieldNullable:     false, // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
// 		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
// 		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
// 	}

// 	conf.WithImportPkgPath(
// 		// 产生的model自动import soft_delete
// 		"gorm.io/plugin/soft_delete",
// 		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/pdp_model",
// 	)

// 	g := bytedgen.NewGenerator(conf)
// 	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
// 		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
// 	})

// 	DB, err := gorm.Open(
// 		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
// 		bytedgorm.MySQL("toutiao.mysql.cloud_pdp_volc" /*数据库PSM*/, "cloud_pdp_volc" /*数据库名*/).WithReadReplicas(),
// 		bytedgorm.WithDefaults(),
// 	)

// 	if err != nil {
// 		panic(err)
// 	}

// 	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
// 	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
// 	g.UseDB(DB)
// 	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
// 		if strings.Contains(tableName, "_del") {
// 			return ""
// 		}
// 		return tableName
// 	})

// 	// 为指定的结构体或表格生成基础CRUD查询方法
// 	//models := g.GenerateAllTable(
// 	//	// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
// 	//	gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
// 	//	// 忽略 deleted_at 字段
// 	//	gen.FieldIgnore("deleted_at"),
// 	//)
// 	//for _, i := range models {
// 	//	g.ApplyBasic(i)
// 	//}
// 	g.ApplyBasic(g.GenerateModel("project_opportunity_customer"))
// 	g.ApplyBasic(g.GenerateModel("public_cloud_resource"))
// 	g.ApplyBasic(g.GenerateModel("oa_application"))
// 	// 执行并生成代码
// 	g.Execute()
// }

func genFeedback() {
	// 指定生成代码的具体(相对)目录，默认为：./query
	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

	currentPath, err := os.Getwd()
	print(currentPath)

	conf := gen.Config{
		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
		OutPath:      currentPath + "/gen/gorm/query",
		ModelPkgPath: currentPath + "/gen/gorm/model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

		FieldNullable:     true,  // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
	}

	conf.WithImportPkgPath(
		// 产生的model自动import soft_delete
		"gorm.io/plugin/soft_delete",
		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model",
	)

	g := bytedgen.NewGenerator(conf)
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
	})

	DB, err := gorm.Open(
		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
		bytedgorm.MySQL("toutiao.mysql.cloud_sherlock" /*数据库PSM*/, "feedback" /*数据库名*/).WithReadReplicas(),
		bytedgorm.WithDefaults(),
	)

	if err != nil {
		panic(err)
	}

	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
	g.UseDB(DB)

	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
		if strings.Contains(tableName, "_del") {
			return ""
		}
		return tableName
	})

	// 为指定的结构体或表格生成基础CRUD查询方法
	models := g.GenerateAllTable(
		// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
		gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
		// 忽略 deleted_at 字段
		gen.FieldIgnore("deleted_at"),
	)
	for _, i := range models {
		g.ApplyBasic(i)
	}

	// 执行并生成代码
	g.Execute()
}

func genDiagnoseResult() {
	// 指定生成代码的具体(相对)目录，默认为：./query
	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

	currentPath, err := os.Getwd()
	print(currentPath)

	conf := gen.Config{
		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
		OutPath:      currentPath + "/gen/gorm/query",
		ModelPkgPath: currentPath + "/gen/gorm/model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

		FieldNullable:     true,  // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
	}

	conf.WithImportPkgPath(
		// 产生的model自动import soft_delete
		"gorm.io/plugin/soft_delete",
		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model",
	)

	g := bytedgen.NewGenerator(conf)
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
	})

	DB, err := gorm.Open(
		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
		bytedgorm.MySQL("toutiao.mysql.cloud_sherlock" /*数据库PSM*/, "diagnose_result" /*数据库名*/).WithReadReplicas(),
		bytedgorm.WithDefaults(),
	)

	if err != nil {
		panic(err)
	}

	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
	g.UseDB(DB)

	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
		if strings.Contains(tableName, "_del") {
			return ""
		}
		return tableName
	})

	// 为指定的结构体或表格生成基础CRUD查询方法
	models := g.GenerateAllTable(
		// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
		gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
		// 忽略 deleted_at 字段
		gen.FieldIgnore("deleted_at"),
	)
	for _, i := range models {
		g.ApplyBasic(i)
	}

	// 执行并生成代码
	g.Execute()
}

func genDiagnoseTask() {
	// 指定生成代码的具体(相对)目录，默认为：./query
	// 默认情况下需要先调用WithContext后才可接具体查询，但可以设置gen.WithoutContext解除限制

	currentPath, err := os.Getwd()
	print(currentPath)

	conf := gen.Config{
		// 最终package不能设置为model，在有数据库表同步的情况下会产生冲突，若一定要使用可以单独指定model package的新名字
		OutPath:      currentPath + "/gen/gorm/query",
		ModelPkgPath: currentPath + "/gen/gorm/model", // 默认情况下会跟随OutPath参数，在同目录下生成model目录

		// Mode: gen.WithoutContext｜gen.WithDefaultQuery, // 解除WithContext限制，生成代码中包含全局变量Q

		FieldNullable:     true,  // 对于数据库表中nullable的数据，在生成代码中自动对应为指针类型
		FieldWithIndexTag: false, // 从数据库同步的表结构代码包含gorm的index tag
		FieldWithTypeTag:  false, // 同步的表结构代码包含gorm的type tag(数据库中对应数据类型)
	}

	conf.WithImportPkgPath(
		// 产生的model自动import soft_delete
		"gorm.io/plugin/soft_delete",
		"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model",
	)

	g := bytedgen.NewGenerator(conf)
	g.WithJSONTagNameStrategy(func(columnName string) (tagContent string) {
		return strings.ReplaceAll(stringx.ToUpperCamelCase(columnName, '_'), "Id", "ID")
	})

	DB, err := gorm.Open(
		// psm 的格式为 p.s.m 无需 _write, _read 等后缀，dbname 为数据库名
		bytedgorm.MySQL("toutiao.mysql.cloud_sherlock" /*数据库PSM*/, "diagnose_task_v2" /*数据库名*/).WithReadReplicas(),
		bytedgorm.WithDefaults(),
	)

	if err != nil {
		panic(err)
	}

	// 复用工程原本使用的SQL连接配置db(*gorm.DB)
	// 非必需，但如果需要连接数据库同步表信息，或需要复用连接时的gorm.Config时必须设置
	g.UseDB(DB)

	g.WithTableNameStrategy(func(tableName string) (targetTableName string) {
		if strings.Contains(tableName, "_del") {
			return ""
		}
		return tableName
	})

	// 为指定的结构体或表格生成基础CRUD查询方法
	models := g.GenerateAllTable(
		// 如果模型字段为deleted_time的话，字段类型修改为 soft_delete.DeleteAt
		gen.FieldType("deleted_time", "soft_delete.DeletedAt"),
		// 忽略 deleted_at 字段
		gen.FieldIgnore("deleted_at"),
	)
	for _, i := range models {
		g.ApplyBasic(i)
	}

	// 执行并生成代码
	g.Execute()
}
