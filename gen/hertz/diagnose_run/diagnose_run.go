// This file is automatically generated. Do not modify.

package diagnose_run

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/feedback"
)

var _ = fmt.Sprintf

type DeleteDiagnoseTaskRunReq struct {
	Id      int64   `form:"ID,required" json:"ID,required" query:"ID,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type DeleteDiagnoseTaskRunResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DiagnoseItem struct {
	Id         int64  `form:"ID,required" json:"ID,required" query:"ID,required"`
	Name       string `form:"Name,required" json:"Name,required" query:"Name,required"`
	Product    string `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct string `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
}

type DiagnoseResource struct {
	Product     string   `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct  string   `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
	InstanceIDs []string `form:"InstanceIDs,required" json:"InstanceIDs,required" query:"InstanceIDs,required"`
}

type DiagnoseResultLevel struct {
	Failed   int64 `form:"Failed,required" json:"Failed,required" query:"Failed,required"`
	Critical int64 `form:"Critical,required" json:"Critical,required" query:"Critical,required"`
	Error    int64 `form:"Error,required" json:"Error,required" query:"Error,required"`
	Warning  int64 `form:"Warning,required" json:"Warning,required" query:"Warning,required"`
	Info     int64 `form:"Info,required" json:"Info,required" query:"Info,required"`
	Unknown  int64 `form:"Unknown,required" json:"Unknown,required" query:"Unknown,required"`
}

type DiagnoseTask struct {
	Id            int64           `form:"ID,required" json:"ID,required" query:"ID,required"`
	Name          string          `form:"Name,required" json:"Name,required" query:"Name,required"`
	DiagnoseType  int32           `form:"DiagnoseType,required" json:"DiagnoseType,required" query:"DiagnoseType,required"`
	UpdateUserID  string          `form:"UpdateUserID,required" json:"UpdateUserID,required" query:"UpdateUserID,required"`
	UpdateTime    int64           `form:"UpdateTime,required" json:"UpdateTime,required" query:"UpdateTime,required"`
	LastRunTime   *int64          `form:"LastRunTime" json:"LastRunTime,omitempty" query:"LastRunTime"`
	CreateTime    int64           `form:"CreateTime,required" json:"CreateTime,required" query:"CreateTime,required"`
	CreateUserID  string          `form:"CreateUserID,required" json:"CreateUserID,required" query:"CreateUserID,required"`
	Origin        string          `form:"Origin,required" json:"Origin,required" query:"Origin,required"`
	DiagnoseItems []*DiagnoseItem `form:"DiagnoseItems" json:"DiagnoseItems,omitempty" query:"DiagnoseItems"`
	ResourceTypes []*ResourceType `form:"ResourceTypes,required" json:"ResourceTypes,required" query:"ResourceTypes,required"`
	Dimension     *string         `form:"Dimension" json:"Dimension,omitempty" query:"Dimension"`
	TicketID      *string         `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
}

type DiagnoseTaskRun struct {
	Id                  int64                `form:"ID,required" json:"ID,required" query:"ID,required"`
	DiagnoseTaskID      int64                `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	DiagnoseTaskName    string               `form:"DiagnoseTaskName,required" json:"DiagnoseTaskName,required" query:"DiagnoseTaskName,required"`
	DiagnoseType        int32                `form:"DiagnoseType,required" json:"DiagnoseType,required" query:"DiagnoseType,required"`
	Status              string               `form:"Status,required" json:"Status,required" query:"Status,required"`
	RunStartTime        string               `form:"RunStartTime,required" json:"RunStartTime,required" query:"RunStartTime,required"`
	RunEndTime          string               `form:"RunEndTime,required" json:"RunEndTime,required" query:"RunEndTime,required"`
	Origin              string               `form:"Origin,required" json:"Origin,required" query:"Origin,required"`
	RunUserID           string               `form:"RunUserID,required" json:"RunUserID,required" query:"RunUserID,required"`
	DiagnoseResultLevel *DiagnoseResultLevel `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
}

type QueryDiagnoseTaskListReq struct {
	Name               *string `form:"Name" json:"Name,omitempty" query:"Name"`
	Origin             *string `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	DiagnoseTemplateID *int64  `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	PageNumber         *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize           *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskListResp struct {
	Result           *QueryDiagnoseTaskListResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata       `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskListResult struct {
	DiagnoseTaskList []*DiagnoseTask    `form:"DiagnoseTaskList,required" json:"DiagnoseTaskList,required" query:"DiagnoseTaskList,required"`
	Pagination       *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type QueryDiagnoseTaskRunListReq struct {
	DiagnoseTaskID      *int64  `form:"DiagnoseTaskID" json:"DiagnoseTaskID,omitempty" query:"DiagnoseTaskID"`
	DiagnoseTaskRunIDs  []int64 `form:"DiagnoseTaskRunIDs" json:"DiagnoseTaskRunIDs,omitempty" query:"DiagnoseTaskRunIDs"`
	Status              *string `form:"Status" json:"Status,omitempty" query:"Status"`
	RunStartTime        *int64  `form:"RunStartTime" json:"RunStartTime,omitempty" query:"RunStartTime"`
	RunEndTime          *int64  `form:"RunEndTime" json:"RunEndTime,omitempty" query:"RunEndTime"`
	AscSortByRunEndTime *bool   `form:"AscSortByRunEndTime" json:"AscSortByRunEndTime,omitempty" query:"AscSortByRunEndTime"`
	PageNumber          int32   `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize            int32   `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	Action              string  `json:"Action,required" query:"Action,required"`
	Version             *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunListResp struct {
	Result           *QueryDiagnoseTaskRunListResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunListResult struct {
	DiagnoseTaskRunList []*DiagnoseTaskRun `form:"DiagnoseTaskRunList,required" json:"DiagnoseTaskRunList,required" query:"DiagnoseTaskRunList,required"`
	Pagination          *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type ResourceType struct {
	Product    string `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct string `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
}

type RunDiagnoseTaskReq struct {
	DiagnoseTaskID    int64               `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	RunUserID         string              `form:"RunUserID,required" json:"RunUserID,required" query:"RunUserID,required"`
	DiagnoseResources []*DiagnoseResource `form:"DiagnoseResources,required" json:"DiagnoseResources,required" query:"DiagnoseResources,required"`
	DiagnoseStartTime int64               `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime   int64               `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
	Action            string              `json:"Action,required" query:"Action,required"`
	Version           *string             `json:"Version,omitempty" query:"Version"`
}

type RunDiagnoseTaskResp struct {
	Id               int64                  `form:"ID,required" json:"ID,required" query:"ID,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SubmitDiagnoseTaskRunFeedbackReq struct {
	DiagnoseTaskRunID int64                   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Resolved          bool                    `form:"Resolved,required" json:"Resolved,required" query:"Resolved,required"`
	EvaluateUserID    *string                 `form:"EvaluateUserID" json:"EvaluateUserID,omitempty" query:"EvaluateUserID"`
	ProblemItems      []*feedback.ProblemItem `form:"ProblemItems" json:"ProblemItems,omitempty" query:"ProblemItems"`
	Description       *string                 `form:"Description" json:"Description,omitempty" query:"Description"`
	Action            string                  `json:"Action,required" query:"Action,required"`
	Version           *string                 `json:"Version,omitempty" query:"Version"`
}

type SubmitDiagnoseTaskRunFeedbackResp struct {
	Id               int64                  `form:"ID,required" json:"ID,required" query:"ID,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}
