// This file is automatically generated. Do not modify.

package diagnose_item

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type DeleteDiagnoseItemRequest struct {
	UserInfo       *UserInfo `form:"UserInfo" json:"UserInfo,omitempty" query:"UserInfo"`
	DiagnoseItemID *int64    `form:"DiagnoseItemID" json:"DiagnoseItemID,omitempty" query:"DiagnoseItemID"`
	Action         string    `json:"Action,required" query:"Action,required"`
	Version        *string   `json:"Version,omitempty" query:"Version"`
}

type DeleteDiagnoseItemResponse struct {
	ResponseMetadata *base.ResponseMetadata    `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	Result           *DeleteDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
}

type DeleteDiagnoseItemResult struct {
	IsSuccess *string `form:"IsSuccess" json:"IsSuccess,omitempty" query:"IsSuccess"`
}

type DiagnoseItem struct {
	DiagnoseItemID          *int64                            `form:"DiagnoseItemID" json:"DiagnoseItemID,omitempty" query:"DiagnoseItemID"`
	DiagnoseItemName        *string                           `form:"DiagnoseItemName" json:"DiagnoseItemName,omitempty" query:"DiagnoseItemName"`
	CreateTime              *string                           `form:"CreateTime" json:"CreateTime,omitempty" query:"CreateTime"`
	Creator                 *UserInfo                         `form:"Creator" json:"Creator,omitempty" query:"Creator"`
	UpdateTime              *string                           `form:"UpdateTime" json:"UpdateTime,omitempty" query:"UpdateTime"`
	Updater                 *UserInfo                         `form:"Updater" json:"Updater,omitempty" query:"Updater"`
	DiagnoseItemCode        *string                           `form:"DiagnoseItemCode" json:"DiagnoseItemCode,omitempty" query:"DiagnoseItemCode"`
	Status                  *string                           `form:"Status" json:"Status,omitempty" query:"Status"`
	Product                 *string                           `form:"Product" json:"Product,omitempty" query:"Product"`
	Subproduct              *string                           `form:"Subproduct" json:"Subproduct,omitempty" query:"Subproduct"`
	AccessType              *string                           `form:"AccessType" json:"AccessType,omitempty" query:"AccessType"`
	AccessResponsiblePerson *string                           `form:"AccessResponsiblePerson" json:"AccessResponsiblePerson,omitempty" query:"AccessResponsiblePerson"`
	AccessPath              *string                           `form:"AccessPath" json:"AccessPath,omitempty" query:"AccessPath"`
	InterAction             *string                           `form:"InterAction" json:"InterAction,omitempty" query:"InterAction"`
	InterVersion            *string                           `form:"InterVersion" json:"InterVersion,omitempty" query:"InterVersion"`
	Suggestion              *string                           `form:"Suggestion" json:"Suggestion,omitempty" query:"Suggestion"`
	SuggestionLink          *string                           `form:"SuggestionLink" json:"SuggestionLink,omitempty" query:"SuggestionLink"`
	Timeout                 *int32                            `form:"Timeout" json:"Timeout,omitempty" query:"Timeout"`
	DiagnoseTemplateIDs     []int64                           `form:"DiagnoseTemplateIDs" json:"DiagnoseTemplateIDs,omitempty" query:"DiagnoseTemplateIDs"`
	InterParams             map[string]string                 `form:"InterParams" json:"InterParams,omitempty" query:"InterParams"`
	DiagnoseTaskIDs         []int64                           `form:"DiagnoseTaskIDs" json:"DiagnoseTaskIDs,omitempty" query:"DiagnoseTaskIDs"`
	ResourceType            *string                           `form:"ResourceType" json:"ResourceType,omitempty" query:"ResourceType"`
	Activity                *string                           `form:"Activity" json:"Activity,omitempty" query:"Activity"`
	ProductCategoryID       *int64                            `form:"ProductCategoryID" json:"ProductCategoryID,omitempty" query:"ProductCategoryID"`
	DiagnoseItemDescription *string                           `form:"DiagnoseItemDescription" json:"DiagnoseItemDescription,omitempty" query:"DiagnoseItemDescription"`
	ProductCategory         *product_category.ProductCategory `form:"ProductCategory,required" json:"ProductCategory,required" query:"ProductCategory,required"`
}

type GetAllDiagnoseItemWithProductRequest struct {
	UserInfo *UserInfo `form:"UserInfo" json:"UserInfo,omitempty" query:"UserInfo"`
	Action   string    `json:"Action,required" query:"Action,required"`
	Version  *string   `json:"Version,omitempty" query:"Version"`
}

type GetAllDiagnoseItemWithProductResponse struct {
	ResponseMetadata *base.ResponseMetadata               `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	Result           *GetAllDiagnoseItemWithProductResult `form:"Result" json:"Result,omitempty" query:"Result"`
}

type GetAllDiagnoseItemWithProductResult struct {
	Product map[string]*SubProduct `form:"Product" json:"Product,omitempty" query:"Product"`
}

type ListDiagnoseItemRequest struct {
	UserInfo                *UserInfo `form:"UserInfo,required" json:"UserInfo,required" query:"UserInfo,required"`
	PageNumber              int64     `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize                int64     `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	DiagnoseItemIDs         []int64   `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	DiagnoseItemName        *string   `form:"DiagnoseItemName" json:"DiagnoseItemName,omitempty" query:"DiagnoseItemName"`
	Status                  *string   `form:"Status" json:"Status,omitempty" query:"Status"`
	ProductCategoryIDs      []int64   `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	AccessResponsiblePerson *string   `form:"AccessResponsiblePerson" json:"AccessResponsiblePerson,omitempty" query:"AccessResponsiblePerson"`
	Action                  string    `json:"Action,required" query:"Action,required"`
	Version                 *string   `json:"Version,omitempty" query:"Version"`
}

type ListDiagnoseItemResponse struct {
	ResponseMetadata *base.ResponseMetadata  `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	Result           *ListDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
}

type ListDiagnoseItemResult struct {
	Pagination    *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
	DiagnoseItems []*DiagnoseItem    `form:"DiagnoseItems" json:"DiagnoseItems,omitempty" query:"DiagnoseItems"`
}

type RegisterDiagnoseItemRequest struct {
	UserInfo                *UserInfo         `form:"UserInfo,required" json:"UserInfo,required" query:"UserInfo,required"`
	DiagnoseItemName        string            `form:"DiagnoseItemName,required" json:"DiagnoseItemName,required" query:"DiagnoseItemName,required"`
	DiagnoseItemCode        string            `form:"DiagnoseItemCode,required" json:"DiagnoseItemCode,required" query:"DiagnoseItemCode,required"`
	Status                  string            `form:"Status,required" json:"Status,required" query:"Status,required"`
	Product                 string            `form:"Product,required" json:"Product,required" query:"Product,required"`
	Subproduct              string            `form:"Subproduct,required" json:"Subproduct,required" query:"Subproduct,required"`
	AccessType              string            `form:"AccessType,required" json:"AccessType,required" query:"AccessType,required"`
	AccessResponsiblePerson string            `form:"AccessResponsiblePerson,required" json:"AccessResponsiblePerson,required" query:"AccessResponsiblePerson,required"`
	AccessPath              string            `form:"AccessPath,required" json:"AccessPath,required" query:"AccessPath,required"`
	InterAction             string            `form:"InterAction,required" json:"InterAction,required" query:"InterAction,required"`
	InterVersion            string            `form:"InterVersion,required" json:"InterVersion,required" query:"InterVersion,required"`
	Suggestion              string            `form:"Suggestion,required" json:"Suggestion,required" query:"Suggestion,required"`
	SuggestionLink          string            `form:"SuggestionLink,required" json:"SuggestionLink,required" query:"SuggestionLink,required"`
	Timeout                 int32             `form:"Timeout,required" json:"Timeout,required" query:"Timeout,required"`
	InterParams             map[string]string `form:"InterParams,required" json:"InterParams,required" query:"InterParams,required"`
	Activity                string            `form:"Activity,required" json:"Activity,required" query:"Activity,required"`
	ResourceType            string            `form:"ResourceType,required" json:"ResourceType,required" query:"ResourceType,required"`
	ProductCategoryID       int64             `form:"ProductCategoryID,required" json:"ProductCategoryID,required" query:"ProductCategoryID,required"`
	DiagnoseItemDescription *string           `form:"DiagnoseItemDescription" json:"DiagnoseItemDescription,omitempty" query:"DiagnoseItemDescription"`
	Action                  string            `json:"Action,required" query:"Action,required"`
	Version                 string            `json:"Version,required" query:"Version,required"`
}

type RegisterDiagnoseItemResponse struct {
	ResponseMetadata *base.ResponseMetadata      `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	Result           *RegisterDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
}

type RegisterDiagnoseItemResult struct {
	DiagnoseItemID *int64 `form:"DiagnoseItemID" json:"DiagnoseItemID,omitempty" query:"DiagnoseItemID"`
}

type SubProduct struct {
	SubProduct map[string][]*DiagnoseItem `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
}

type UpdateDiagnoseItemRequest struct {
	UserInfo                *UserInfo `form:"UserInfo" json:"UserInfo,omitempty" query:"UserInfo"`
	DiagnoseItemID          *int64    `form:"DiagnoseItemID" json:"DiagnoseItemID,omitempty" query:"DiagnoseItemID"`
	DiagnoseItemName        *string   `form:"DiagnoseItemName" json:"DiagnoseItemName,omitempty" query:"DiagnoseItemName"`
	DiagnoseItemCode        *string   `form:"DiagnoseItemCode" json:"DiagnoseItemCode,omitempty" query:"DiagnoseItemCode"`
	Status                  *string   `form:"Status" json:"Status,omitempty" query:"Status"`
	Product                 *string   `form:"Product" json:"Product,omitempty" query:"Product"`
	Subproduct              *string   `form:"Subproduct" json:"Subproduct,omitempty" query:"Subproduct"`
	AccessType              *string   `form:"AccessType" json:"AccessType,omitempty" query:"AccessType"`
	AccessResponsiblePerson *string   `form:"AccessResponsiblePerson" json:"AccessResponsiblePerson,omitempty" query:"AccessResponsiblePerson"`
	AccessPath              *string   `form:"AccessPath" json:"AccessPath,omitempty" query:"AccessPath"`
	InterAction             *string   `form:"InterAction" json:"InterAction,omitempty" query:"InterAction"`
	InterVersion            *string   `form:"InterVersion" json:"InterVersion,omitempty" query:"InterVersion"`
	Suggestion              *string   `form:"Suggestion" json:"Suggestion,omitempty" query:"Suggestion"`
	SuggestionLink          *string   `form:"SuggestionLink" json:"SuggestionLink,omitempty" query:"SuggestionLink"`
	Timeout                 *int32    `form:"Timeout" json:"Timeout,omitempty" query:"Timeout"`
	DiagnoseItemDescription *string   `form:"DiagnoseItemDescription" json:"DiagnoseItemDescription,omitempty" query:"DiagnoseItemDescription"`
	Activity                *string   `form:"Activity" json:"Activity,omitempty" query:"Activity"`
	Action                  string    `json:"Action,required" query:"Action,required"`
	Version                 *string   `json:"Version,omitempty" query:"Version"`
}

type UpdateDiagnoseItemResponse struct {
	ResponseMetadata *base.ResponseMetadata    `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	Result           *UpdateDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
}

type UpdateDiagnoseItemResult struct {
	IsSuccess *string `form:"IsSuccess" json:"IsSuccess,omitempty" query:"IsSuccess"`
}

type UserInfo struct {
	UserID   string `form:"UserID,required" json:"UserID,required" query:"UserID,required"`
	UserName string `form:"UserName,required" json:"UserName,required" query:"UserName,required"`
}
