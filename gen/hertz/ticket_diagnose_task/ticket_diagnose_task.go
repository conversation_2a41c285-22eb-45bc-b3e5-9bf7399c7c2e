// This file is automatically generated. Do not modify.

package ticket_diagnose_task

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
)

var _ = fmt.Sprintf

type CreateDiagnoseTaskReq struct {
	TicketID           string                              `form:"TicketID,required" json:"TicketID,required" query:"TicketID,required"`
	DiagnoseTemplateID *int64                              `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	Dimension          string                              `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	DiagnoseItemIDs    []int64                             `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	DiagnoseResources  []*diagnose_result.DiagnoseResource `form:"DiagnoseResources,required" json:"DiagnoseResources,required" query:"DiagnoseResources,required"`
	CreateUserID       string                              `form:"CreateUserID,required" json:"CreateUserID,required" query:"CreateUserID,required"`
	CreateUserName     *string                             `form:"CreateUserName" json:"CreateUserName,omitempty" query:"CreateUserName"`
	AccountID          *string                             `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	DiagnoseStartTime  int64                               `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime    int64                               `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
	DiagnoseType       int32                               `form:"DiagnoseType,required" json:"DiagnoseType,required" query:"DiagnoseType,required"`
	ProductCategoryIDs []int64                             `form:"ProductCategoryIDs,required" json:"ProductCategoryIDs,required" query:"ProductCategoryIDs,required"`
	Action             string                              `json:"Action,required" query:"Action,required"`
	Version            *string                             `json:"Version,omitempty" query:"Version"`
}

type CreateDiagnoseTaskResp struct {
	Result           *CreateDiagnoseTaskResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata    `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type CreateDiagnoseTaskResult struct {
	DiagnoseTaskID int64 `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
}

type DescribeTaskDiagnoseItemReq struct {
	DiagnoseTaskID      int64    `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	DiagnoseItemID      int64    `form:"DiagnoseItemID,required" json:"DiagnoseItemID,required" query:"DiagnoseItemID,required"`
	DiagnoseResultLevel []string `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	InstanceID          []string `form:"InstanceID" json:"InstanceID,omitempty" query:"InstanceID"`
	PageNumber          *int32   `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize            *int32   `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action              string   `json:"Action,required" query:"Action,required"`
	Version             *string  `json:"Version,omitempty" query:"Version"`
}

type DescribeTaskDiagnoseItemResp struct {
	Result           *DescribeTaskDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DescribeTaskDiagnoseItemResult struct {
	DiagnoseResultLevel *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	DiagnoseResults     []*diagnose_result.DiagnoseResult    `form:"DiagnoseResults,required" json:"DiagnoseResults,required" query:"DiagnoseResults,required"`
	Pagination          *common.Pagination                   `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type DiagnoseTask struct {
	DiagnoseTaskID       int64   `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	DiagnoseTemplateName *string `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Status               *string `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime            *string `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	CreateUserID         *string `form:"CreateUserID" json:"CreateUserID,omitempty" query:"CreateUserID"`
	CreateUserName       *string `form:"CreateUserName" json:"CreateUserName,omitempty" query:"CreateUserName"`
	TicketID             *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	Feedback             *bool   `form:"Feedback" json:"Feedback,omitempty" query:"Feedback"`
	DiagnoseStartTime    *int64  `form:"DiagnoseStartTime" json:"DiagnoseStartTime,omitempty" query:"DiagnoseStartTime"`
	DiagnoseEndTime      *int64  `form:"DiagnoseEndTime" json:"DiagnoseEndTime,omitempty" query:"DiagnoseEndTime"`
}

type ListDiagnoseTaskReq struct {
	TicketID            *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTemplateIDs []int64 `form:"DiagnoseTemplateIDs" json:"DiagnoseTemplateIDs,omitempty" query:"DiagnoseTemplateIDs"`
	Status              *string `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime           *string `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime             *string `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	PageNumber          *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize            *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	AscSortByEndTime    *bool   `form:"AscSortByEndTime" json:"AscSortByEndTime,omitempty" query:"AscSortByEndTime"`
	Action              string  `json:"Action,required" query:"Action,required"`
	Version             *string `json:"Version,omitempty" query:"Version"`
}

type ListDiagnoseTaskResp struct {
	Result           *ListDiagnoseTaskResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata  `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListDiagnoseTaskResult struct {
	DiagnoseTaskList []*DiagnoseTask    `form:"DiagnoseTaskList,required" json:"DiagnoseTaskList,required" query:"DiagnoseTaskList,required"`
	Pagination       *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type ListTicketDiagnoseTaskReq struct {
	TicketID         string  `form:"TicketID,required" json:"TicketID,required" query:"TicketID,required"`
	PageNumber       *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize         *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	AscSortByEndTime *bool   `form:"AscSortByEndTime" json:"AscSortByEndTime,omitempty" query:"AscSortByEndTime"`
	Status           *string `form:"Status" json:"Status,omitempty" query:"Status"`
	Action           string  `json:"Action,required" query:"Action,required"`
	Version          *string `json:"Version,omitempty" query:"Version"`
}

type ListTicketDiagnoseTaskResp struct {
	Result           *ListTicketDiagnoseTaskResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata        `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListTicketDiagnoseTaskResult struct {
	DiagnoseTaskList []*DiagnoseTask    `form:"DiagnoseTaskList,required" json:"DiagnoseTaskList,required" query:"DiagnoseTaskList,required"`
	Pagination       *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type QueryDiagnoseTaskCategoryReq struct {
	DiagnoseTaskID int64   `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskCategoryResp struct {
	Result           *QueryDiagnoseTaskCategoryResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata           `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskCategoryResult struct {
	DiagnoseTaskCategoryList []*diagnose_result.DiagnoseTaskCategory `form:"DiagnoseTaskCategoryList,required" json:"DiagnoseTaskCategoryList,required" query:"DiagnoseTaskCategoryList,required"`
}

type QueryDiagnoseTaskItemsReq struct {
	DiagnoseTaskID     int64   `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskItemsResp struct {
	Result           *QueryDiagnoseTaskItemsResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata        `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskItemsResult struct {
	DiagnoseItems []*diagnose_task_v2.DiagnoseItem `form:"DiagnoseItems,required" json:"DiagnoseItems,required" query:"DiagnoseItems,required"`
}

type QueryDiagnoseTaskResourcesReq struct {
	DiagnoseTaskID     int64   `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	DiagnoseItemIDs    []int64 `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskResourcesResp struct {
	Result           *QueryDiagnoseTaskResourcesResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata            `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskResourcesResult struct {
	DiagnoseResources []*diagnose_result.DiagnoseResource `form:"DiagnoseResources,required" json:"DiagnoseResources,required" query:"DiagnoseResources,required"`
}

type QueryDiagnoseTaskSummaryReq struct {
	DiagnoseTaskID int64   `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskSummaryResp struct {
	Result           *QueryDiagnoseTaskSummaryResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskSummaryResult struct {
	DiagnoseResultLevel  *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	DiagnoseTemplateName *string                              `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	StartTime            *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	CreateUserID         *string                              `form:"CreateUserID" json:"CreateUserID,omitempty" query:"CreateUserID"`
	CreateUserName       *string                              `form:"CreateUserName" json:"CreateUserName,omitempty" query:"CreateUserName"`
	Progress             int32                                `form:"Progress,required" json:"Progress,required" query:"Progress,required"`
	Feedback             bool                                 `form:"Feedback,required" json:"Feedback,required" query:"Feedback,required"`
	DiagnoseStartTime    int64                                `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime      int64                                `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
}
