// This file is automatically generated. Do not modify.

package permission

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type CheckUserPermissionItem struct {
	ResourceKey     string          `form:"ResourceKey,required" json:"ResourceKey,required" query:"<PERSON>Key,required"`
	ActionAccessMap map[string]bool `form:"ActionAccessMap,required" json:"ActionAccessMap,required" query:"ActionAccessMap,required"`
}

type CheckUserPermissionReq struct {
	Rules   []*Rule `form:"Rules,required" json:"Rules,required" query:"Rules,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type CheckUserPermissionResp struct {
	Result           *CheckUserPermissionResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata     `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type CheckUserPermissionResult struct {
	List []*CheckUserPermissionItem `form:"List,required" json:"List,required" query:"List,required"`
}

type GetMenuPermissionConfigReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type GetMenuPermissionConfigResp struct {
	Result           *GetMenuPermissionConfigResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata         `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type GetMenuPermissionConfigResult struct {
	List []*MenuItem `form:"List" json:"List,omitempty" query:"List"`
}

type GetResourcesReq struct {
	Resources []string `form:"Resources,required" json:"Resources,required" query:"Resources,required"`
	Action    string   `json:"Action,required" query:"Action,required"`
	Version   *string  `json:"Version,omitempty" query:"Version"`
}

type GetResourcesResp struct {
	Key         string   `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name        string   `form:"Name,required" json:"Name,required" query:"Name,required"`
	Description string   `form:"Description,required" json:"Description,required" query:"Description,required"`
	ActionKeys  []string `form:"ActionKeys,required" json:"ActionKeys,required" query:"ActionKeys,required"`
}

type GetUserPermissionReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type GetUserPermissionResp struct {
	Result           []*UserPermission      `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListPermissionRolesReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type ListPermissionRolesResp struct {
	Result           *PermissionRolesResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type MenuItem struct {
	MenuKey                *string       `form:"MenuKey" json:"MenuKey,omitempty" query:"MenuKey"`
	MenuName               *string       `form:"MenuName" json:"MenuName,omitempty" query:"MenuName"`
	IsHidden               *bool         `form:"IsHidden" json:"IsHidden,omitempty" query:"IsHidden"`
	OperablePermissionList []*Permission `form:"OperablePermissionList" json:"OperablePermissionList,omitempty" query:"OperablePermissionList"`
	ViewablePermissionList []*Permission `form:"ViewablePermissionList" json:"ViewablePermissionList,omitempty" query:"ViewablePermissionList"`
	Children               []*MenuItem   `form:"Children" json:"Children,omitempty" query:"Children"`
}

type Permission struct {
	ResourceKey *string `form:"ResourceKey" json:"ResourceKey,omitempty" query:"ResourceKey"`
	ActionKey   *string `form:"ActionKey" json:"ActionKey,omitempty" query:"ActionKey"`
}

type PermissionRolesResult struct {
	RoleList []string `form:"RoleList,required" json:"RoleList,required" query:"RoleList,required"`
}

type Rule struct {
	ResourceKey string `form:"ResourceKey,required" json:"ResourceKey,required" query:"ResourceKey,required"`
	ActionKey   string `form:"ActionKey,required" json:"ActionKey,required" query:"ActionKey,required"`
}

type UserPermission struct {
	Resource   string   `form:"Resource,required" json:"Resource,required" query:"Resource,required"`
	ActionList []string `form:"ActionList,required" json:"ActionList,required" query:"ActionList,required"`
}
