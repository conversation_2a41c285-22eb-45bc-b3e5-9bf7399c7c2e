// This file is automatically generated. Do not modify.

package base

import (
	"fmt"
)

var _ = fmt.Sprintf

type ErrorObj struct {
	CodeN   *int32  `form:"CodeN" json:"CodeN,omitempty" query:"CodeN"`
	Code    *string `form:"Code" json:"Code,omitempty" query:"Code"`
	Message *string `form:"Message" json:"Message,omitempty" query:"Message"`
}

type ResponseMetadata struct {
	RequestId *string   `form:"RequestId" json:"RequestId,omitempty" query:"RequestId"`
	Action    *string   `form:"Action" json:"Action,omitempty" query:"Action"`
	Version   *string   `form:"Version" json:"Version,omitempty" query:"Version"`
	Service   *string   `form:"Service" json:"Service,omitempty" query:"Service"`
	Region    *string   `form:"Region" json:"Region,omitempty" query:"Region"`
	Error     *ErrorObj `form:"Error" json:"Error,omitempty" query:"Error"`
}
