// This file is automatically generated. Do not modify.

package diagnose_template

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type DeleteDiagnoseTemplateRequest struct {
	DiagnoseTemplateID *int64    `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	UserInfo           *UserInfo `form:"UserInfo" json:"UserInfo,omitempty" query:"UserInfo"`
	Action             string    `json:"Action,required" query:"Action,required"`
	Version            *string   `json:"Version,omitempty" query:"Version"`
}

type DeleteDiagnoseTemplateResponse struct {
	DeleteDiagnoseTemplateResult *DeleteDiagnoseTemplateResult `form:"DeleteDiagnoseTemplateResult" json:"DeleteDiagnoseTemplateResult,omitempty" query:"DeleteDiagnoseTemplateResult"`
	ResponseMetadata             *base.ResponseMetadata        `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DeleteDiagnoseTemplateResult struct {
	DiagnoseTemplateID *int64 `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
}

type DiagnoseTemplate struct {
	DiagnoseTemplateID   *int64                            `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	DiagnoseTemplateName *string                           `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	DiagnosesItemIDs     []int64                           `form:"DiagnosesItemIDs" json:"DiagnosesItemIDs,omitempty" query:"DiagnosesItemIDs"`
	DiagnoseTaskIDs      []int64                           `form:"DiagnoseTaskIDs" json:"DiagnoseTaskIDs,omitempty" query:"DiagnoseTaskIDs"`
	UpdateTime           *string                           `form:"UpdateTime" json:"UpdateTime,omitempty" query:"UpdateTime"`
	Updater              *UserInfo                         `form:"Updater" json:"Updater,omitempty" query:"Updater"`
	CreateTime           *string                           `form:"CreateTime" json:"CreateTime,omitempty" query:"CreateTime"`
	Creator              *UserInfo                         `form:"Creator" json:"Creator,omitempty" query:"Creator"`
	TemplateDescription  *string                           `form:"TemplateDescription" json:"TemplateDescription,omitempty" query:"TemplateDescription"`
	Suggestion           *string                           `form:"Suggestion" json:"Suggestion,omitempty" query:"Suggestion"`
	DiagnoseItemIDs      []int64                           `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	StateMachineInfo     *string                           `form:"StateMachineInfo" json:"StateMachineInfo,omitempty" query:"StateMachineInfo"`
	Status               *string                           `form:"Status" json:"Status,omitempty" query:"Status"`
	ResponsiblePerson    *string                           `form:"ResponsiblePerson" json:"ResponsiblePerson,omitempty" query:"ResponsiblePerson"`
	Product              *string                           `form:"Product" json:"Product,omitempty" query:"Product"`
	SubProduct           *string                           `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	ResourceType         *string                           `form:"ResourceType" json:"ResourceType,omitempty" query:"ResourceType"`
	ShowLevel            *int32                            `form:"ShowLevel" json:"ShowLevel,omitempty" query:"ShowLevel"`
	Inputs               []*Input                          `form:"Inputs" json:"Inputs,omitempty" query:"Inputs"`
	ProductCategory      *product_category.ProductCategory `form:"ProductCategory,required" json:"ProductCategory,required" query:"ProductCategory,required"`
}

type Input struct {
	Key              *string `form:"Key" json:"Key,omitempty" query:"Key"`
	Type             *string `form:"Type" json:"Type,omitempty" query:"Type"`
	DefaultValue     *string `form:"DefaultValue" json:"DefaultValue,omitempty" query:"DefaultValue"`
	IsRequired       *bool   `form:"IsRequired" json:"IsRequired,omitempty" query:"IsRequired"`
	InputDescription *string `form:"InputDescription" json:"InputDescription,omitempty" query:"InputDescription"`
}

type ListDiagnoseTemplateRequest struct {
	UserInfo             *UserInfo `form:"UserInfo" json:"UserInfo,omitempty" query:"UserInfo"`
	DiagnoseTemplateIDs  []int64   `form:"DiagnoseTemplateIDs" json:"DiagnoseTemplateIDs,omitempty" query:"DiagnoseTemplateIDs"`
	PageNumber           *int64    `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize             *int64    `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	DiagnoseTemplateName *string   `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Status               *string   `form:"Status" json:"Status,omitempty" query:"Status"`
	ResponsiblePerson    *string   `form:"ResponsiblePerson" json:"ResponsiblePerson,omitempty" query:"ResponsiblePerson"`
	ProductCategoryIDs   []int32   `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action               string    `json:"Action,required" query:"Action,required"`
	Version              string    `json:"Version,required" query:"Version,required"`
}

type ListDiagnoseTemplateResponse struct {
	ListDiagnoseTemplateResult *ListDiagnoseTemplateResult `form:"ListDiagnoseTemplateResult" json:"ListDiagnoseTemplateResult,omitempty" query:"ListDiagnoseTemplateResult"`
	ResponseMetadata           *base.ResponseMetadata      `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListDiagnoseTemplateResult struct {
	Pagination        *common.Pagination  `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
	DiagnoseTemplates []*DiagnoseTemplate `form:"DiagnoseTemplates" json:"DiagnoseTemplates,omitempty" query:"DiagnoseTemplates"`
}

type RegisterDiagnoseTemplateRequest struct {
	DiagnoseTemplateName string    `form:"DiagnoseTemplateName,required" json:"DiagnoseTemplateName,required" query:"DiagnoseTemplateName,required"`
	UserInfo             *UserInfo `form:"UserInfo,required" json:"UserInfo,required" query:"UserInfo,required"`
	TemplateDescription  string    `form:"TemplateDescription,required" json:"TemplateDescription,required" query:"TemplateDescription,required"`
	Suggestion           string    `form:"Suggestion,required" json:"Suggestion,required" query:"Suggestion,required"`
	Inputs               []*Input  `form:"Inputs" json:"Inputs,omitempty" query:"Inputs"`
	StateMachineInfo     string    `form:"StateMachineInfo,required" json:"StateMachineInfo,required" query:"StateMachineInfo,required"`
	Status               string    `form:"Status,required" json:"Status,required" query:"Status,required"`
	ResponsiblePerson    string    `form:"ResponsiblePerson,required" json:"ResponsiblePerson,required" query:"ResponsiblePerson,required"`
	DiagnosesItemIDs     []int64   `form:"DiagnosesItemIDs,required" json:"DiagnosesItemIDs,required" query:"DiagnosesItemIDs,required"`
	Product              *string   `form:"Product" json:"Product,omitempty" query:"Product"`
	SubProduct           *string   `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	ResourceType         *string   `form:"ResourceType" json:"ResourceType,omitempty" query:"ResourceType"`
	ShowLevel            int64     `form:"ShowLevel,required" json:"ShowLevel,required" query:"ShowLevel,required"`
	ProductCategoryID    int64     `form:"ProductCategoryID,required" json:"ProductCategoryID,required" query:"ProductCategoryID,required"`
	Action               string    `json:"Action,required" query:"Action,required"`
	Version              string    `json:"Version,required" query:"Version,required"`
}

type RegisterDiagnoseTemplateResponse struct {
	RegisterDiagnoseTemplateResult *RegisterDiagnoseTemplateResult `form:"RegisterDiagnoseTemplateResult" json:"RegisterDiagnoseTemplateResult,omitempty" query:"RegisterDiagnoseTemplateResult"`
	ResponseMetadata               *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type RegisterDiagnoseTemplateResult struct {
	DiagnoseTemplateID *int64 `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
}

type UpdateDiagnoseTemplateRequest struct {
	UserInfo             *UserInfo `form:"UserInfo,required" json:"UserInfo,required" query:"UserInfo,required"`
	DiagnoseTemplateID   *int64    `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	DiagnoseTemplateName *string   `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	TemplateDescription  *string   `form:"TemplateDescription" json:"TemplateDescription,omitempty" query:"TemplateDescription"`
	Suggestion           *string   `form:"suggestion" json:"suggestion,omitempty" query:"suggestion"`
	DiagnosesItemIDs     []int64   `form:"DiagnosesItemIDs" json:"DiagnosesItemIDs,omitempty" query:"DiagnosesItemIDs"`
	StateMachineInfo     *string   `form:"StateMachineInfo" json:"StateMachineInfo,omitempty" query:"StateMachineInfo"`
	Status               *string   `form:"Status" json:"Status,omitempty" query:"Status"`
	Action               string    `json:"Action,required" query:"Action,required"`
	Version              *string   `json:"Version,omitempty" query:"Version"`
}

type UpdateDiagnoseTemplateResponse struct {
	UpdateDiagnoseTemplateResult *UpdateDiagnoseTemplateResult `form:"UpdateDiagnoseTemplateResult" json:"UpdateDiagnoseTemplateResult,omitempty" query:"UpdateDiagnoseTemplateResult"`
	ResponseMetadata             *base.ResponseMetadata        `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type UpdateDiagnoseTemplateResult struct {
	DiagnoseTemplateID *int64 `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
}

type UserInfo struct {
	UserID   string `form:"UserID,required" json:"UserID,required" query:"UserID,required"`
	UserName string `form:"UserName,required" json:"UserName,required" query:"UserName,required"`
}
