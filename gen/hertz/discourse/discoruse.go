package discourse

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_run"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/ticket_diagnose_task"
	"fmt"
	"time"
)

var _ = fmt.Sprintf

type CreateReq struct {
	Token string `form:"Token" json:"Token" query:"Token"`
	Query string `form:"Query" json:"Query" query:"Query"`
}

type CreateResp struct {
	SessionID string `from:"SessionID" json:"SessionID,required" query:"SessionID"`
}

type HistoryRequest struct {
	Token      string `form:"Token,required" json:"Token,required" query:"Token,required"`
	PageNumber int    `from:"PageNumber" json:"PageNumber,required" query:"PageNumber"`
	PageSize   int    `from:"PageSize" json:"PageSize,required" query:"PageSize"`
}

type HistoryResp struct {
	TotalCount int64                      `from:"TotalCount" json:"TotalCount,required" query:"TotalCount"`
	PageNumber int                        `from:"PageNumber" json:"PageNumber,required" query:"PageNumber"`
	PageSize   int                        `from:"PageSize" json:"PageSize,required" query:"PageSize"`
	Items      []*QueryDiscourseSessionDo `from:"Items" json:"Items,required" query:"Items"`
}

type QueryDiscourseSessionDo struct {
	SessionID string     `from:"SessionID" json:"SessionID,required" query:"SessionID"`
	Title     string     `from:"Title" json:"Title,required" query:"Title"`
	Date      *time.Time `from:"Date" json:"Date,required" query:"Date"`
}

type QueryDiagnoseTaskRunDetailReq struct {
	DiagnoseTaskRunID int64 `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
}

type QueryDiagnoseTaskRunDetailResp struct {
	Data      *[]*SSEMessage `json:"data"`
	Status    string         `json:"status"`
	Progress  int32          `json:"progress"`
	TaskRunID int64          `json:"task_run_id"`
}

type DetailRequest struct {
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	PageSize  int64  `form:"PageSize" json:"PageSize" query:"PageSize"`
	PageNum   int64  `form:"PageNum" json:"PageNum" query:"PageNum"`
}

type HandleDiscourseRequest struct {
	Query     string `form:"Query" json:"Query" query:"Query"`
	ReQuery   interface{}
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type SSEDetail struct {
	Messages []DetailMessage `form:"Messages,required" json:"Messages,required" query:"Messages,required"`
}

type CreateAgentDiagnoseTaskReq struct {
	ReQuery   interface{} `form:"ReQuery,required" json:"ReQuery,required" query:"ReQuery,required"`
	Token     string      `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string      `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type SessionRedisObj struct {
	ID               int64   `json:"ID"`
	SessionID        string  `json:"SessionID"`
	IntentionSession *string `json:"IntentionSession"`
	DiagnoseSession  *string `json:"DiagnoseSession"`
	SummarySession   *string `json:"SummarySession"`
}

type RunRequest struct {
	Update            bool     `json:"Update,omitempty" form:"Update" query:"Update"`
	Product           string   `json:"Product,omitempty" form:"Product" query:"Product"`
	ID                int64    `json:"ID,omitempty" form:"ID" query:"ID"`
	Function          string   `json:"Function,omitempty" form:"Function" query:"Function"`
	AccountID         string   `json:"AccountID,omitempty" form:"AccountID" query:"AccountID"`
	TicketID          string   `json:"TicketID,omitempty" form:"TicketID" query:"TicketID"`
	Arguments         string   `json:"Arguments,omitempty" form:"Arguments" query:"Arguments"`
	Query             string   `json:"Query,omitempty" form:"Query" query:"Query"`
	Instances         []string `json:"Instances,omitempty" form:"Instances" query:"Instances"`
	Token             string   `json:"Token" form:"Token" query:"Token"`
	SessionID         string   `json:"SessionID" form:"SessionID" query:"SessionID"`
	DiagnoseStartTime int64    `form:"DiagnoseStartTime,omitempty" json:"DiagnoseStartTime" query:"DiagnoseStartTime"`
	DiagnoseEndTime   int64    `form:"DiagnoseEndTime,omitempty" json:"DiagnoseEndTime" query:"DiagnoseEndTime"`
}

type FeedBackRequest struct {
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
	MessageID string `form:"MessageID,required" json:"MessageID" query:"MessageID,required"`
	Upvote    *bool  `form:"Upvote" json:"Upvote" query:"Upvote"`
}

type CreateDiagnoseTaskReq struct {
	ticket_diagnose_task.CreateDiagnoseTaskReq
	Token     string `form:"Token,required" json:"Token,required" query:"Token,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type DiagnoseTaskRunBot struct {
	Id                   int64                                `form:"ID,required" json:"ID,required" query:"ID,required"`
	DiagnoseTaskID       int64                                `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	Name                 *string                              `form:"Name" json:"Name,omitempty" query:"Name"`
	DiagnoseType         *int32                               `form:"DiagnoseType" json:"DiagnoseType,omitempty" query:"DiagnoseType"`
	Status               *string                              `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime            *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	RunUserID            *string                              `form:"RunUserID" json:"RunUserID,omitempty" query:"RunUserID"`
	TicketID             *string                              `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseResultLevel  *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	Feedback             *bool                                `form:"Feedback" json:"Feedback,omitempty" query:"Feedback"`
	DiagnoseStartTime    *int64                               `form:"DiagnoseStartTime" json:"DiagnoseStartTime,omitempty" query:"DiagnoseStartTime"`
	DiagnoseEndTime      *int64                               `form:"DiagnoseEndTime" json:"DiagnoseEndTime,omitempty" query:"DiagnoseEndTime"`
	DiagnoseTemplateName *string                              `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Origin               int32                                `form:"Origin,required" json:"Origin,required" query:"Origin,required"`
	ProductCategory      *[]*product_category.ProductCategory `form:"ProductCategory" json:"ProductCategory,omitempty" query:"ProductCategory"`
	Instances            *[]string                            `form:"Instances" json:"Instances,omitempty" query:"Instances"`
}

type QueryDiagnoseTaskRunDetailResultBot struct {
	Information             *DiagnoseTaskRunBot                        `form:"Information,required" json:"Information,required" query:"Information,required"`
	DiagnoseTaskRunByteFlow *diagnose_task_run.DiagnoseTaskRunByteFlow `form:"DiagnoseTaskRunByteFlow,required" json:"DiagnoseTaskRunByteFlow,required" query:"DiagnoseTaskRunByteFlow,required"`
}
