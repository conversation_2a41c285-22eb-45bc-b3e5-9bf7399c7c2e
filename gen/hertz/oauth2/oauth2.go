// This file is automatically generated. Do not modify.

package oauth2

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type AuthUrlReq struct {
	OriginalPath string  `form:"OriginalPath,required" json:"OriginalPath,required" query:"OriginalPath,required"`
	Action       string  `json:"Action,required" query:"Action,required"`
	Version      *string `json:"Version,omitempty" query:"Version"`
}

type AuthUrlResp struct {
	Result           *AuthUrlResult         `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type AuthUrlResult struct {
	AuthUrl string `form:"AuthUrl,required" json:"AuthUrl,required" query:"AuthUrl,required"`
}

type Department struct {
	Name   string `form:"Name,required" json:"Name,required" query:"Name,required"`
	EnName string `form:"en_name,required" json:"en_name,required" query:"en_name,required"`
}

type TokenReq struct {
	Code    string  `form:"Code,required" json:"Code,required" query:"Code,required"`
	State   string  `form:"State,required" json:"State,required" query:"State,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type TokenResp struct {
	Result           *TokenResult           `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type TokenResult struct {
	OriginalPath string      `form:"OriginalPath,required" json:"OriginalPath,required" query:"OriginalPath,required"`
	Token        string      `form:"Token,required" json:"Token,required" query:"Token,required"`
	Username     string      `form:"Username,required" json:"Username,required" query:"Username,required"`
	Email        string      `form:"Email,required" json:"Email,required" query:"Email,required"`
	Department   *Department `form:"Department,required" json:"Department,required" query:"Department,required"`
}

type UserInfoReq struct {
	Token   string  `form:"Token,required" json:"Token,required" query:"Token,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type UserInfoResp struct {
	Result           *UserInfoResult        `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type UserInfoResult struct {
	Username   string      `form:"Username,required" json:"Username,required" query:"Username,required"`
	Email      string      `form:"Email,required" json:"Email,required" query:"Email,required"`
	Department *Department `form:"Department,required" json:"Department,required" query:"Department,required"`
}
