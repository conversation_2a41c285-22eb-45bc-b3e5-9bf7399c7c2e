// This file is automatically generated. Do not modify.

package openapi

import (
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/discourse"
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_item"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_template"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type CreateDiagnoseTaskReq struct {
	TicketID           *string                             `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTemplateID *int64                              `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	Dimension          string                              `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	DiagnoseItemIDs    []int64                             `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	DiagnoseResources  []*diagnose_result.DiagnoseResource `form:"DiagnoseResources,required" json:"DiagnoseResources,required" query:"DiagnoseResources,required"`
	CreateUserID       string                              `form:"CreateUserID,required" json:"CreateUserID,required" query:"CreateUserID,required"`
	CreateUserName     *string                             `form:"CreateUserName" json:"CreateUserName,omitempty" query:"CreateUserName"`
	AccountID          *string                             `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	DiagnoseStartTime  int64                               `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime    int64                               `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
	DiagnoseType       int32                               `form:"DiagnoseType,required" json:"DiagnoseType,required" query:"DiagnoseType,required"`
	ProductCategoryIDs []int64                             `form:"ProductCategoryIDs,required" json:"ProductCategoryIDs,required" query:"ProductCategoryIDs,required"`
	Origin             *int32                              `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	OriginName         *string                             `form:"OriginName" json:"OriginName,omitempty" query:"OriginName"`
	Name               string                              `form:"Name,required" json:"Name,required" query:"Name,required"`
	Action             string                              `json:"Action,required" query:"Action,required"`
	Version            *string                             `json:"Version,omitempty" query:"Version"`
}

type CreateDiagnoseTaskResp struct {
	Result           *CreateDiagnoseTaskResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata    `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type CreateDiagnoseTaskResult struct {
	DiagnoseTaskID int64 `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
}

type ListDiagnoseItemRequest struct {
	PageNumber              int64   `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize                int64   `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	DiagnoseItemIDs         []int64 `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	DiagnoseItemName        *string `form:"DiagnoseItemName" json:"DiagnoseItemName,omitempty" query:"DiagnoseItemName"`
	Status                  *string `form:"Status" json:"Status,omitempty" query:"Status"`
	ProductCategoryIDs      []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	AccessResponsiblePerson *string `form:"AccessResponsiblePerson" json:"AccessResponsiblePerson,omitempty" query:"AccessResponsiblePerson"`
	Action                  string  `json:"Action,required" query:"Action,required"`
	Version                 *string `json:"Version,omitempty" query:"Version"`
}

type ListDiagnoseItemResponse struct {
	Result           *ListDiagnoseItemResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata  `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListDiagnoseItemResult struct {
	DiagnoseItems []*diagnose_item.DiagnoseItem `form:"DiagnoseItems" json:"DiagnoseItems,omitempty" query:"DiagnoseItems"`
	Pagination    *common.Pagination            `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type ListDiagnoseTemplateRequest struct {
	DiagnoseTemplateIDs  []int64 `form:"DiagnoseTemplateIDs" json:"DiagnoseTemplateIDs,omitempty" query:"DiagnoseTemplateIDs"`
	PageNumber           int64   `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize             int64   `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	DiagnoseTemplateName *string `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Status               *string `form:"Status" json:"Status,omitempty" query:"Status"`
	ResponsiblePerson    *string `form:"ResponsiblePerson" json:"ResponsiblePerson,omitempty" query:"ResponsiblePerson"`
	ProductCategoryIDs   []int32 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action               string  `json:"Action,required" query:"Action,required"`
	Version              string  `json:"Version,required" query:"Version,required"`
}

type ListDiagnoseTemplateResponse struct {
	ListDiagnoseTemplateResult *ListDiagnoseTemplateResult `form:"ListDiagnoseTemplateResult" json:"ListDiagnoseTemplateResult,omitempty" query:"ListDiagnoseTemplateResult"`
	ResponseMetadata           *base.ResponseMetadata      `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListDiagnoseTemplateResult struct {
	DiagnoseTemplates []*diagnose_template.DiagnoseTemplate `form:"DiagnoseTemplates" json:"DiagnoseTemplates,omitempty" query:"DiagnoseTemplates"`
	Pagination        *common.Pagination                    `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type ListProductCategoryRequest struct {
	Id             *int64  `form:"ID" json:"ID,omitempty" query:"ID"`
	Product        *string `form:"Product" json:"Product,omitempty" query:"Product"`
	SubProduct     *string `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	ResourceType   *string `form:"ResourceType" json:"ResourceType,omitempty" query:"ResourceType"`
	ProductCn      *string `form:"ProductCn" json:"ProductCn,omitempty" query:"ProductCn"`
	SubProductCn   *string `form:"SubProductCn" json:"SubProductCn,omitempty" query:"SubProductCn"`
	ResourceTypeCn *string `form:"ResourceTypeCn" json:"ResourceTypeCn,omitempty" query:"ResourceTypeCn"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type ListProductCategoryResponse struct {
	ListProductCategoryResult *ListProductCategoryResult `form:"ListProductCategoryResult" json:"ListProductCategoryResult,omitempty" query:"ListProductCategoryResult"`
	ResponseMetadata          *base.ResponseMetadata     `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListProductCategoryResult struct {
	ProductCategoryList []*product_category.ProductCategory `form:"ProductCategoryList" json:"ProductCategoryList,omitempty" query:"ProductCategoryList"`
}

type QueryDiagnoseTaskResultRequest struct {
	DiagnoseTaskRunID int64    `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Levels            []string `form:"Levels" json:"Levels,omitempty" query:"Levels"`
	DiagnoseItemID    *int64   `form:"DiagnoseItemID" json:"DiagnoseItemID,omitempty" query:"DiagnoseItemID"`
	Action            string   `json:"Action,required" query:"Action,required"`
	Version           *string  `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskResultResponse struct {
	Result           *QueryDiagnoseTaskResultResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata         `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskResultResult struct {
	DiagnoseResults []*diagnose_result.DiagnoseResult `form:"DiagnoseResults,required" json:"DiagnoseResults,required" query:"DiagnoseResults,required"`
}

type UpdateDiagnoseItemNameRequest struct {
	UserInfo         *UserInfo `form:"UserInfo,required" json:"UserInfo,required" query:"UserInfo,required"`
	DiagnoseItemID   int64     `form:"DiagnoseItemID,required" json:"DiagnoseItemID,required" query:"DiagnoseItemID,required"`
	DiagnoseItemName string    `form:"DiagnoseItemName,required" json:"DiagnoseItemName,required" query:"DiagnoseItemName,required"`
	Action           string    `json:"Action,required" query:"Action,required"`
	Version          *string   `json:"Version,omitempty" query:"Version"`
}

type UpdateDiagnoseItemNameResponse struct {
	ResponseMetadata             *base.ResponseMetadata        `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
	UpdateDiagnoseItemNameResult *UpdateDiagnoseItemNameResult `form:"UpdateDiagnoseItemNameResult" json:"UpdateDiagnoseItemNameResult,omitempty" query:"UpdateDiagnoseItemNameResult"`
}

type UpdateDiagnoseItemNameResult struct {
	Result *string `form:"Result" json:"Result,omitempty" query:"Result"`
}

type UserInfo struct {
	UserID   string `form:"UserID,required" json:"UserID,required" query:"UserID,required"`
	UserName string `form:"UserName,required" json:"UserName,required" query:"UserName,required"`
}

type TicketSummaryReq struct {
	TicketID     string    `form:"TicketID,required" json:"TicketID,required" query:"TicketID,required"`
	SubProductID string    `form:"SubProductID,required" json:"SubProductID,required" query:"SubProductID,required"`
	Data         *[]DoData `form:"Data,required" json:"Data,required" query:"Data,required"`
}

type DoData struct {
	Content     string `json:"Content"`
	ChatID      string `json:"ChatID"`
	Channel     string `json:"Channel"`
	CreateTime  int64  `json:"CreateTime"`
	MessageID   string `json:"MessageID"`
	ContentType string `json:"ContentType"`
	Sender      struct {
		SenderType string `json:"SenderType"`
		Email      string `json:"Email"`
	} `json:"Sender"`
}

type TicketReportReq struct {
	ID        string `form:"ID,required" json:"ID,required" query:"ID,required"`
	SessionID string `form:"SessionID,required" json:"SessionID,required" query:"SessionID,required"`
}

type TicketResp struct {
	Status    string       `json:"Status"`
	Choices   *[]DoChoices `json:"Choices"`
	Created   int64        `json:"Created"`
	ID        string       `json:"ID"`
	SessionID string       `json:"SessionID"`
}

type DoChoices struct {
	FinishReason string       `json:"FinishReason"`
	Index        int          `json:"Index"`
	Message      DoSubMessage `json:"Message"`
}

type DoSubMessage struct {
	Content          string      `json:"Content"`
	Role             string      `json:"Role"`
	ReasoningContent interface{} `json:"ReasoningContent"`
}

type TicketRedisObj struct {
	Status     string                `json:"Status"`
	TaskRunID  int64                 `json:"TaskRunID"`
	AnswerInfo *discourse.AnswerInfo `json:"AnswerInfo"`
}
