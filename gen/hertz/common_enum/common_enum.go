// This file is automatically generated. Do not modify.

package common_enum

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type GetCommonEnumConfigReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type GetCommonEnumConfigResp struct {
	Result           map[string]map[string][]*common.EnumItem `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata                   `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type GetUploadTokenReq struct {
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type GetUploadTokenResp struct {
	Result           *UploadToken           `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type UploadToken struct {
	AccessKeyId     string `form:"AccessKeyId" json:"AccessKeyId" query:"AccessKeyId"`
	SecretAccessKey string `form:"SecretAccessKey" json:"SecretAccessKey" query:"SecretAccessKey"`
	SessionToken    string `form:"SessionToken" json:"SessionToken" query:"SessionToken"`
	ExpiredTime     string `form:"ExpiredTime" json:"ExpiredTime" query:"ExpiredTime"`
	CurrentTime     string `form:"CurrentTime" json:"CurrentTime" query:"CurrentTime"`
}
