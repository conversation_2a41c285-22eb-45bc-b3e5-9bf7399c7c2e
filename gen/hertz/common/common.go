// This file is automatically generated. Do not modify.

package common

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type EnumItem struct {
	Label    *string     `form:"Label" json:"Label,omitempty" query:"Label"`
	Value    *string     `form:"Value" json:"Value,omitempty" query:"Value"`
	IsHidden *bool       `form:"IsHidden" json:"IsHidden,omitempty" query:"IsHidden"`
	Extra    *Extra      `form:"Extra" json:"Extra,omitempty" query:"Extra"`
	Children []*EnumItem `form:"Children" json:"Children,omitempty" query:"Children"`
}

type Extra struct {
	Type  *string `form:"Type" json:"Type,omitempty" query:"Type"`
	Color *string `form:"Color" json:"Color,omitempty" query:"Color"`
}

type GetCommonMapConfigReq struct {
	Key     string  `form:"Key,required" json:"Key,required" query:"Key,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type GetCommonMapConfigResp struct {
	Result           map[string]string      `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type Order struct {
	FieldName string  `form:"FieldName,required" json:"FieldName,required" query:"FieldName,required"`
	Order     *string `form:"Order" json:"Order,omitempty" query:"Order"`
}

type Pagination struct {
	PageNumber int32 `form:"PageNumber,required" json:"PageNumber,required" query:"PageNumber,required"`
	PageSize   int32 `form:"PageSize,required" json:"PageSize,required" query:"PageSize,required"`
	TotalCount int64 `form:"TotalCount,required" json:"TotalCount,required" query:"TotalCount,required"`
}

type StatItem struct {
	StatName *string `form:"StatName" json:"StatName,omitempty" query:"StatName"`
	Count    *int64  `form:"Count" json:"Count,omitempty" query:"Count"`
}

type StatResult struct {
	TotalCount                *int64      `form:"TotalCount" json:"TotalCount,omitempty" query:"TotalCount"`
	MonthOnMonthRatio         *float64    `form:"MonthOnMonthRatio" json:"MonthOnMonthRatio,omitempty" query:"MonthOnMonthRatio"`
	MonthOnMonthIncreaseCount *int64      `form:"MonthOnMonthIncreaseCount" json:"MonthOnMonthIncreaseCount,omitempty" query:"MonthOnMonthIncreaseCount"`
	StatList                  []*StatItem `form:"StatList" json:"StatList,omitempty" query:"StatList"`
}
