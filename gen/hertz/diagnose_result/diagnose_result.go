// This file is automatically generated. Do not modify.

package diagnose_result

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type DiagnoseItemInfo struct {
	Message             *string `form:"Message" json:"Message,omitempty" query:"Message"`
	DiagnoseResultLevel string  `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	Suggestion          *string `form:"Suggestion" json:"Suggestion,omitempty" query:"Suggestion"`
	Operate             *string `form:"Operate" json:"Operate,omitempty" query:"Operate"`
}

type DiagnoseResource struct {
	Instances         []string `form:"Instances,required" json:"Instances,required" query:"Instances,required"`
	ProductCategoryID int64    `form:"ProductCategoryID,required" json:"ProductCategoryID,required" query:"ProductCategoryID,required"`
}

type DiagnoseResult struct {
	InstanceID          string  `form:"InstanceID,required" json:"InstanceID,required" query:"InstanceID,required"`
	InstanceName        *string `form:"InstanceName" json:"InstanceName,omitempty" query:"InstanceName"`
	Region              *string `form:"Region" json:"Region,omitempty" query:"Region"`
	Message             *string `form:"Message" json:"Message,omitempty" query:"Message"`
	DiagnoseResultLevel string  `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	Suggestion          *string `form:"Suggestion" json:"Suggestion,omitempty" query:"Suggestion"`
	Operate             *string `form:"Operate" json:"Operate,omitempty" query:"Operate"`
	DiagnoseItemID      int64   `form:"DiagnoseItemID,required" json:"DiagnoseItemID,required" query:"DiagnoseItemID,required"`
}

type DiagnoseResultLevel struct {
	Failed   int64 `form:"Failed,required" json:"Failed,required" query:"Failed,required"`
	Critical int64 `form:"Critical,required" json:"Critical,required" query:"Critical,required"`
	Error    int64 `form:"Error,required" json:"Error,required" query:"Error,required"`
	Warning  int64 `form:"Warning,required" json:"Warning,required" query:"Warning,required"`
	Info     int64 `form:"Info,required" json:"Info,required" query:"Info,required"`
	Unknown  int64 `form:"Unknown,required" json:"Unknown,required" query:"Unknown,required"`
}

type DiagnoseTaskCategory struct {
	ProductCategory *product_category.ProductCategory `form:"ProductCategory,required" json:"ProductCategory,required" query:"ProductCategory,required"`
	AbnormalNum     int32                             `form:"AbnormalNum,required" json:"AbnormalNum,required" query:"AbnormalNum,required"`
}
