// This file is automatically generated. Do not modify.

package feedback

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type ProblemItem struct {
	ProblemCode string `form:"ProblemCode,required" json:"ProblemCode,required" query:"ProblemCode,required"`
	ProblemName string `form:"ProblemName,required" json:"ProblemName,required" query:"ProblemName,required"`
}

type SubmitDiagnoseTaskFeedbackReq struct {
	DiagnoseTaskID    *int64         `form:"DiagnoseTaskID" json:"DiagnoseTaskID,omitempty" query:"DiagnoseTaskID"`
	DiagnoseTaskRunID int64          `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Resolved          bool           `form:"Resolved,required" json:"Resolved,required" query:"Resolved,required"`
	ProblemItems      []*ProblemItem `form:"ProblemItems" json:"ProblemItems,omitempty" query:"ProblemItems"`
	Description       *string        `form:"Description" json:"Description,omitempty" query:"Description"`
	FeedbackUserID    *string        `form:"FeedbackUserID" json:"FeedbackUserID,omitempty" query:"FeedbackUserID"`
	Action            string         `json:"Action,required" query:"Action,required"`
	Version           *string        `json:"Version,omitempty" query:"Version"`
}

type SubmitDiagnoseTaskFeedbackResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}
