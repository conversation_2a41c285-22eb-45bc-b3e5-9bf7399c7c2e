// This file is automatically generated. Do not modify.

package describe_resource

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type CustomerInfo struct {
	AccountId    *string `form:"AccountId" json:"AccountId,omitempty" query:"AccountId"`
	CustomerName *string `form:"CustomerName" json:"CustomerName,omitempty" query:"CustomerName"`
}

type DescribeResourceDependencyRequest struct {
	Dimension  string     `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	AccountId  *string    `form:"AccountId" json:"AccountId,omitempty" query:"AccountId"`
	Resources  *Resources `form:"Resources,required" json:"Resources,required" query:"Resources,required"`
	Region     *string    `form:"Region" json:"Region,omitempty" query:"Region"`
	PageSize   *int32     `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	PageNumber *int32     `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
}

type DescribeResourceRequest struct {
	Dimension  string     `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	AccountId  *string    `form:"AccountId" json:"AccountId,omitempty" query:"AccountId"`
	Resources  *Resources `form:"Resources,required" json:"Resources,required" query:"Resources,required"`
	Region     *string    `form:"Region" json:"Region,omitempty" query:"Region"`
	PageSize   *int32     `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	PageNumber *int32     `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
}

type DescribeResourceResponse struct {
	ResourceType        string                   `form:"ResourceType,required" json:"ResourceType,required" query:"ResourceType,required"`
	ResourceMetadata    []*ResourceMetadataEntry `form:"ResourceMetadata,required" json:"ResourceMetadata,required" query:"ResourceMetadata,required"`
	ResourceNetworkInfo []*ResourceNetworkEntry  `form:"ResourceNetworkInfo" json:"ResourceNetworkInfo,omitempty" query:"ResourceNetworkInfo"`
	CustomerInfo        *CustomerInfo            `form:"CustomerInfo" json:"CustomerInfo" query:"CustomerInfo"`
	OriginMsg           string                   `form:"OriginMsg,required" json:"OriginMsg,required" query:"OriginMsg,required"`
	GrafanaLink         *string                  `form:"GrafanaLink" json:"GrafanaLink,omitempty" query:"GrafanaLink"`
	ResponseMetadata    *base.ResponseMetadata   `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ResourceMetadataEntry struct {
	Key         string  `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name        string  `form:"Name,required" json:"Name,required" query:"Name,required"`
	GrafanaLink *string `form:"GrafanaLink" json:"GrafanaLink,omitempty" query:"GrafanaLink"`
}

type ResourceNetworkEntry struct {
	Key  string `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name string `form:"Name,required" json:"Name,required" query:"Name,required"`
}

type Resources struct {
	ResourceType string   `form:"ResourceType,required" json:"ResourceType,required" query:"ResourceType,required"`
	Product      string   `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct   string   `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
	Instances    []string `form:"Instances,required" json:"Instances,required" query:"Instances,required"`
}
