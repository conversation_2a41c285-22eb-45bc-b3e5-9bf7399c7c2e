// This file is automatically generated. Do not modify.

package describe_ecs

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type DescribeInstanceInput struct {
	Dimension  string     `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	AccountId  int64      `form:"AccountId,required" json:"AccountId,required" query:"AccountId,required"`
	Resources  *Resources `form:"Resources" json:"Resources,omitempty" query:"Resources"`
	Region     string     `form:"Region,required" json:"Region,required" query:"Region,required"`
	PageSize   *int32     `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	PageNumber *int32     `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
}

type DescribeInstanceResponse struct {
	DescribeInstanceResult *DescribeInstanceResult `form:"DescribeInstanceResult" json:"DescribeInstanceResult,omitempty" query:"DescribeInstanceResult"`
	ResponseMetadata       *base.ResponseMetadata  `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DescribeInstanceResult struct {
	Type       *string            `form:"Type" json:"Type,omitempty" query:"Type"`
	Pagination *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
	Instances  []*Instance        `form:"Instances" json:"Instances,omitempty" query:"Instances"`
}

type Instance struct {
	InstanceName *string `form:"InstanceName" json:"InstanceName,omitempty" query:"InstanceName"`
	Id           *string `form:"Id" json:"Id,omitempty" query:"Id"`
	Status       *string `form:"Status" json:"Status,omitempty" query:"Status"`
	ImageId      *string `form:"ImageId" json:"ImageId,omitempty" query:"ImageId"`
	InstanceType *string `form:"InstanceType" json:"InstanceType,omitempty" query:"InstanceType"`
	Region       *string `form:"Region" json:"Region,omitempty" query:"Region"`
}

type Resources struct {
	Type       string   `form:"Type,required" json:"Type,required" query:"Type,required"`
	Product    string   `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct string   `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
	Instances  []string `form:"Instances" json:"Instances,omitempty" query:"Instances"`
}
