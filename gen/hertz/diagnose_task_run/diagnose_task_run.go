// This file is automatically generated. Do not modify.

package diagnose_task_run

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_result"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/diagnose_task_v2"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type DeleteDiagnoseTaskRunReq struct {
	Id      int64   `form:"ID,required" json:"ID,required" query:"ID,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type DeleteDiagnoseTaskRunResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DiagnoseItemStatus struct {
	DiagnoseItem        *diagnose_task_v2.DiagnoseItem       `form:"DiagnoseItem,required" json:"DiagnoseItem,required" query:"DiagnoseItem,required"`
	Status              string                               `form:"Status,required" json:"Status,required" query:"Status,required"`
	StartTime           *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime             *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	DiagnoseResultLevel *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	DiagnoseResources   []*diagnose_result.DiagnoseResource  `form:"DiagnoseResources" json:"DiagnoseResources,omitempty" query:"DiagnoseResources"`
}

type DiagnoseTaskRun struct {
	Id                   int64                                `form:"ID,required" json:"ID,required" query:"ID,required"`
	DiagnoseTaskID       int64                                `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	Name                 *string                              `form:"Name" json:"Name,omitempty" query:"Name"`
	DiagnoseType         *int32                               `form:"DiagnoseType" json:"DiagnoseType,omitempty" query:"DiagnoseType"`
	Status               *string                              `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime            *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	RunUserID            *string                              `form:"RunUserID" json:"RunUserID,omitempty" query:"RunUserID"`
	TicketID             *string                              `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseResultLevel  *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	Feedback             *bool                                `form:"Feedback" json:"Feedback,omitempty" query:"Feedback"`
	DiagnoseStartTime    *int64                               `form:"DiagnoseStartTime" json:"DiagnoseStartTime,omitempty" query:"DiagnoseStartTime"`
	DiagnoseEndTime      *int64                               `form:"DiagnoseEndTime" json:"DiagnoseEndTime,omitempty" query:"DiagnoseEndTime"`
	DiagnoseTemplateName *string                              `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	Origin               int32                                `form:"Origin,required" json:"Origin,required" query:"Origin,required"`
	ProductCategory      []*product_category.ProductCategory  `form:"ProductCategory" json:"ProductCategory,omitempty" query:"ProductCategory"`
}

type DiagnoseTaskRunByteFlow struct {
	StateMachineInfo       *string                       `form:"StateMachineInfo" json:"StateMachineInfo,omitempty" query:"StateMachineInfo"`
	DiagnoseItemStatusList map[int64]*DiagnoseItemStatus `form:"DiagnoseItemStatusList,required" json:"DiagnoseItemStatusList,required" query:"DiagnoseItemStatusList,required"`
}

type DiagnoseTaskRunItemDetailResult struct {
	DiagnoseItem        *diagnose_task_v2.DiagnoseItem       `form:"DiagnoseItem,required" json:"DiagnoseItem,required" query:"DiagnoseItem,required"`
	DiagnoseResultLevel *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	DiagnoseResults     []*diagnose_result.DiagnoseResult    `form:"DiagnoseResults,required" json:"DiagnoseResults,required" query:"DiagnoseResults,required"`
	Pagination          *common.Pagination                   `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type ProductCategoryLevel struct {
	ProductCategory     *product_category.ProductCategory    `form:"ProductCategory,required" json:"ProductCategory,required" query:"ProductCategory,required"`
	DiagnoseResultLevel *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel" query:"DiagnoseResultLevel"`
}

type QueryDiagnoseTaskRunCategoryReq struct {
	DiagnoseTaskRunID int64   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Action            string  `json:"Action,required" query:"Action,required"`
	Version           *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunCategoryResp struct {
	Result           *QueryDiagnoseTaskRunCategoryResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata              `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunCategoryResult struct {
	DiagnoseTaskRunCategoryList []*diagnose_result.DiagnoseTaskCategory `form:"DiagnoseTaskRunCategoryList,required" json:"DiagnoseTaskRunCategoryList,required" query:"DiagnoseTaskRunCategoryList,required"`
}

type QueryDiagnoseTaskRunDetailReq struct {
	DiagnoseTaskRunID int64   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Action            string  `json:"Action,required" query:"Action,required"`
	Version           *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunDetailResp struct {
	Result           *QueryDiagnoseTaskRunDetailResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata            `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunDetailResult struct {
	Information             *DiagnoseTaskRun         `form:"Information,required" json:"Information,required" query:"Information,required"`
	DiagnoseTaskRunByteFlow *DiagnoseTaskRunByteFlow `form:"DiagnoseTaskRunByteFlow,required" json:"DiagnoseTaskRunByteFlow,required" query:"DiagnoseTaskRunByteFlow,required"`
}

type QueryDiagnoseTaskRunItemDetailReq struct {
	DiagnoseTaskRunID   int64    `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	DiagnoseItemID      int64    `form:"DiagnoseItemID,required" json:"DiagnoseItemID,required" query:"DiagnoseItemID,required"`
	DiagnoseResultLevel []string `form:"DiagnoseResultLevel" json:"DiagnoseResultLevel,omitempty" query:"DiagnoseResultLevel"`
	InstanceID          []string `form:"InstanceID" json:"InstanceID,omitempty" query:"InstanceID"`
	PageNumber          *int32   `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize            *int32   `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action              string   `json:"Action,required" query:"Action,required"`
	Version             *string  `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunItemDetailResp struct {
	Result           *DiagnoseTaskRunItemDetailResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata           `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunItemsReq struct {
	DiagnoseTaskRunID  int64   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunItemsResp struct {
	Result           *QueryDiagnoseTaskRunItemsResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata           `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunItemsResult struct {
	DiagnoseItems []*diagnose_task_v2.DiagnoseItem `form:"DiagnoseItems,required" json:"DiagnoseItems,required" query:"DiagnoseItems,required"`
}

type QueryDiagnoseTaskRunListReq struct {
	DiagnoseTaskID       *int64   `form:"DiagnoseTaskID" json:"DiagnoseTaskID,omitempty" query:"DiagnoseTaskID"`
	DiagnoseTaskRunIDs   []int64  `form:"DiagnoseTaskRunIDs" json:"DiagnoseTaskRunIDs,omitempty" query:"DiagnoseTaskRunIDs"`
	Status               *string  `form:"Status" json:"Status,omitempty" query:"Status"`
	StartTime            *string  `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime              *string  `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	AscSortByEndTime     *bool    `form:"AscSortByEndTime" json:"AscSortByEndTime,omitempty" query:"AscSortByEndTime"`
	DiagnoseTaskRunName  *string  `form:"DiagnoseTaskRunName" json:"DiagnoseTaskRunName,omitempty" query:"DiagnoseTaskRunName"`
	PageNumber           *int32   `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize             *int32   `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	TicketID             *string  `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTemplateIDs  []int64  `form:"DiagnoseTemplateIDs" json:"DiagnoseTemplateIDs,omitempty" query:"DiagnoseTemplateIDs"`
	DiagnoseResultLevels []string `form:"DiagnoseResultLevels" json:"DiagnoseResultLevels,omitempty" query:"DiagnoseResultLevels"`
	ProductCategoryIDs   []int64  `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Origin               *int32   `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	Action               string   `json:"Action,required" query:"Action,required"`
	Version              *string  `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunListResp struct {
	Result           *QueryDiagnoseTaskRunListResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunListResult struct {
	DiagnoseTaskRunList []*DiagnoseTaskRun `form:"DiagnoseTaskRunList,required" json:"DiagnoseTaskRunList,required" query:"DiagnoseTaskRunList,required"`
	Pagination          *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type QueryDiagnoseTaskRunResourceReq struct {
	DiagnoseTaskRunID  int64   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	DiagnoseItemIDs    []int64 `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunResourceResp struct {
	Result           *QueryDiagnoseTaskRunResourceResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata              `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunResourceResult struct {
	DiagnoseResources []*diagnose_result.DiagnoseResource `form:"DiagnoseResources,required" json:"DiagnoseResources,required" query:"DiagnoseResources,required"`
}

type QueryDiagnoseTaskRunSummaryReq struct {
	DiagnoseTaskRunID int64   `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
	Action            string  `json:"Action,required" query:"Action,required"`
	Version           *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskRunSummaryResp struct {
	Result           *QueryDiagnoseTaskRunSummaryResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata             `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskRunSummaryResult struct {
	DiagnoseResultLevel   *diagnose_result.DiagnoseResultLevel `form:"DiagnoseResultLevel,required" json:"DiagnoseResultLevel,required" query:"DiagnoseResultLevel,required"`
	DiagnoseTemplateName  *string                              `form:"DiagnoseTemplateName" json:"DiagnoseTemplateName,omitempty" query:"DiagnoseTemplateName"`
	StartTime             *string                              `form:"StartTime" json:"StartTime,omitempty" query:"StartTime"`
	EndTime               *string                              `form:"EndTime" json:"EndTime,omitempty" query:"EndTime"`
	CreateUserID          *string                              `form:"CreateUserID" json:"CreateUserID,omitempty" query:"CreateUserID"`
	CreateUserName        *string                              `form:"CreateUserName" json:"CreateUserName,omitempty" query:"CreateUserName"`
	Progress              int32                                `form:"Progress,required" json:"Progress,required" query:"Progress,required"`
	Feedback              bool                                 `form:"Feedback,required" json:"Feedback,required" query:"Feedback,required"`
	DiagnoseStartTime     int64                                `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime       int64                                `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
	ProductCategoryLevels []*ProductCategoryLevel              `form:"ProductCategoryLevels,required" json:"ProductCategoryLevels,required" query:"ProductCategoryLevels,required"`
}

type RunDiagnoseTaskReq struct {
	DiagnoseTaskID    int64                               `form:"DiagnoseTaskID,required" json:"DiagnoseTaskID,required" query:"DiagnoseTaskID,required"`
	RunUserID         string                              `form:"RunUserID,required" json:"RunUserID,required" query:"RunUserID,required"`
	DiagnoseResources []*diagnose_result.DiagnoseResource `form:"DiagnoseResources" json:"DiagnoseResources,omitempty" query:"DiagnoseResources"`
	DiagnoseStartTime int64                               `form:"DiagnoseStartTime,required" json:"DiagnoseStartTime,required" query:"DiagnoseStartTime,required"`
	DiagnoseEndTime   int64                               `form:"DiagnoseEndTime,required" json:"DiagnoseEndTime,required" query:"DiagnoseEndTime,required"`
	Name              *string                             `form:"Name" json:"Name,omitempty" query:"Name"`
	AccountID         *string                             `form:"AccountID" json:"AccountID,omitempty" query:"AccountID"`
	Action            string                              `json:"Action,required" query:"Action,required"`
	Version           *string                             `json:"Version,omitempty" query:"Version"`
}

type RunDiagnoseTaskResp struct {
	Result           *RunDiagnoseTaskResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type RunDiagnoseTaskResult struct {
	DiagnoseTaskRunID int64 `form:"DiagnoseTaskRunID,required" json:"DiagnoseTaskRunID,required" query:"DiagnoseTaskRunID,required"`
}
