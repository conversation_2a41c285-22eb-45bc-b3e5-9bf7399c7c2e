// This file is automatically generated. Do not modify.

package customer

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type CustomerServiceInfo struct {
	RoleName      *string  `form:"RoleName" json:"RoleName,omitempty" query:"RoleName"`
	Role          *int32   `form:"Role" json:"Role,omitempty" query:"Role"`
	UserEmailList []string `form:"UserEmailList" json:"UserEmailList,omitempty" query:"UserEmailList"`
}

type CustomerSimpleInfo struct {
	CustomerName   string `form:"CustomerName,required" json:"CustomerName,required" query:"CustomerName,required"`
	CustomerNumber string `form:"CustomerNumber,required" json:"CustomerNumber,required" query:"CustomerNumber,required"`
	ShortName      string `form:"ShortName,required" json:"ShortName,required" query:"ShortName,required"`
	CustomerTier   string `form:"CustomerTier,required" json:"CustomerTier,required" query:"CustomerTier,required"`
}

type CustomerVolcAccount struct {
	VolcAccountID   *string `form:"VolcAccountID" json:"VolcAccountID,omitempty" query:"VolcAccountID"`
	VolcAccountName *string `form:"VolcAccountName" json:"VolcAccountName,omitempty" query:"VolcAccountName"`
	CustomerName    *string `form:"CustomerName" json:"CustomerName,omitempty" query:"CustomerName"`
	CustomerNumber  *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
}

type GetCustomerInfoReq struct {
	CustomerNumber *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	VolcAccountID  *string `form:"VolcAccountID" json:"VolcAccountID,omitempty" query:"VolcAccountID"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type GetCustomerInfoResp struct {
	Result           *GetCustomerInfoResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type GetCustomerInfoResult struct {
	CustomerName         *string  `form:"CustomerName" json:"CustomerName,omitempty" query:"CustomerName"`
	CustomerNumber       *string  `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	ShortName            *string  `form:"ShortName" json:"ShortName,omitempty" query:"ShortName"`
	Province             *string  `form:"Province" json:"Province,omitempty" query:"Province"`
	ProvinceName         *string  `form:"ProvinceName" json:"ProvinceName,omitempty" query:"ProvinceName"`
	Industry             *string  `form:"Industry" json:"Industry,omitempty" query:"Industry"`
	IndustryName         *string  `form:"IndustryName" json:"IndustryName,omitempty" query:"IndustryName"`
	SubIndustry          *string  `form:"SubIndustry" json:"SubIndustry,omitempty" query:"SubIndustry"`
	SubIndustryName      *string  `form:"SubIndustryName" json:"SubIndustryName,omitempty" query:"SubIndustryName"`
	CustomerLevel        *string  `form:"CustomerLevel" json:"CustomerLevel,omitempty" query:"CustomerLevel"`
	CustomerLevelName    *string  `form:"CustomerLevelName" json:"CustomerLevelName,omitempty" query:"CustomerLevelName"`
	CustomerPLevel       *string  `form:"CustomerPLevel" json:"CustomerPLevel,omitempty" query:"CustomerPLevel"`
	CustomerPLevelName   *string  `form:"CustomerPLevelName" json:"CustomerPLevelName,omitempty" query:"CustomerPLevelName"`
	CustomerTier         *string  `form:"CustomerTier" json:"CustomerTier,omitempty" query:"CustomerTier"`
	CustomerTierName     *string  `form:"CustomerTierName" json:"CustomerTierName,omitempty" query:"CustomerTierName"`
	OwnerEmail           *string  `form:"OwnerEmail" json:"OwnerEmail,omitempty" query:"OwnerEmail"`
	OwnerName            *string  `form:"OwnerName" json:"OwnerName,omitempty" query:"OwnerName"`
	Region               *string  `form:"Region" json:"Region,omitempty" query:"Region"`
	RegionName           *string  `form:"RegionName" json:"RegionName,omitempty" query:"RegionName"`
	ProjectTotalCount    *int64   `form:"ProjectTotalCount" json:"ProjectTotalCount,omitempty" query:"ProjectTotalCount"`
	CSMList              []string `form:"CSMList" json:"CSMList,omitempty" query:"CSMList"`
	VolcanoAccountIDList []string `form:"VolcanoAccountIDList" json:"VolcanoAccountIDList,omitempty" query:"VolcanoAccountIDList"`
	GuaranteeStatus      *string  `form:"GuaranteeStatus" json:"GuaranteeStatus,omitempty" query:"GuaranteeStatus"`
	CustomerSfID         *string  `form:"CustomerSfID" json:"CustomerSfID,omitempty" query:"CustomerSfID"`
}

type GetCustomerServiceTeamReq struct {
	CustomerNumber *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type GetCustomerServiceTeamResp struct {
	Result           []*CustomerServiceInfo `form:"result" json:"result,omitempty" query:"result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListCustomerSimpleInfoReq struct {
	SearchKeyword  *string  `form:"SearchKeyword" json:"SearchKeyword,omitempty" query:"SearchKeyword"`
	CustomerNumber []string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	PageNumber     *int32   `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize       *int32   `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action         string   `json:"Action,required" query:"Action,required"`
	Version        *string  `json:"Version,omitempty" query:"Version"`
}

type ListCustomerSimpleInfoResp struct {
	Result           []*CustomerSimpleInfo  `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListCustomerVolcAccountReq struct {
	VolcAccountIDFuzz *string `form:"VolcAccountIDFuzz" json:"VolcAccountIDFuzz,omitempty" query:"VolcAccountIDFuzz"`
	CustomerNumber    *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	Action            string  `json:"Action,required" query:"Action,required"`
	Version           *string `json:"Version,omitempty" query:"Version"`
}

type ListCustomerVolcAccountResp struct {
	Result           []*CustomerVolcAccount `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SyncCustomerReq struct {
	CustomerNumberList []string `form:"CustomerNumberList" json:"CustomerNumberList,omitempty" query:"CustomerNumberList"`
	Action             string   `json:"Action,required" query:"Action,required"`
	Version            *string  `json:"Version,omitempty" query:"Version"`
}

type SyncCustomerResp struct {
	Result           *SyncCustomerResult    `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type SyncCustomerResult struct {
	SyncedCustomerNumberList []string `form:"SyncedCustomerNumberList" json:"SyncedCustomerNumberList,omitempty" query:"SyncedCustomerNumberList"`
}
