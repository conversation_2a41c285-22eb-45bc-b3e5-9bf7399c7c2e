// This file is automatically generated. Do not modify.

package customer_failure

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type ListCustomerFailureItem struct {
	FailureID             *string           `form:"FailureID" json:"FailureID,omitempty" query:"FailureID"`
	VolcAccountIDList     []int64           `form:"VolcAccountIDList" json:"VolcAccountIDList,omitempty" query:"VolcAccountIDList"`
	ProductIDList         []string          `form:"ProductIDList" json:"ProductIDList,omitempty" query:"ProductIDList"`
	ProductNameList       []string          `form:"ProductNameList" json:"ProductNameList,omitempty" query:"ProductNameList"`
	FailureName           *string           `form:"FailureName" json:"FailureName,omitempty" query:"FailureName"`
	FailureStatus         *int32            `form:"FailureStatus" json:"FailureStatus,omitempty" query:"FailureStatus"`
	FailureStatusName     *string           `form:"FailureStatusName" json:"FailureStatusName,omitempty" query:"FailureStatusName"`
	FailureEntity         *string           `form:"FailureEntity" json:"FailureEntity,omitempty" query:"FailureEntity"`
	AccountTeamMemberList []string          `form:"AccountTeamMemberList" json:"AccountTeamMemberList,omitempty" query:"AccountTeamMemberList"`
	ReleatedTicketIDList  []string          `form:"ReleatedTicketIDList" json:"ReleatedTicketIDList,omitempty" query:"ReleatedTicketIDList"`
	TicketSystemURLMap    map[string]string `form:"TicketSystemURLMap" json:"TicketSystemURLMap,omitempty" query:"TicketSystemURLMap"`
	FailureTime           *int64            `form:"FailureTime" json:"FailureTime,omitempty" query:"FailureTime"`
}

type ListCustomerFailureReq struct {
	PageSize       *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	PageNumber     *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	CustomerNumber *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type ListCustomerFailureResp struct {
	Result           *ListCustomerFailureResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata     `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListCustomerFailureResult struct {
	List       []*ListCustomerFailureItem `form:"List" json:"List,omitempty" query:"List"`
	Pagination *common.Pagination         `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}
