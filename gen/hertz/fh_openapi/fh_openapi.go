// This file is automatically generated. Do not modify.

package fh_openapi

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type ListCustomerVolcAccountRequest struct {
	VolcAccountIDFuzz *string  `form:"VolcAccountIDFuzz" json:"VolcAccountIDFuzz,omitempty" query:"VolcAccountIDFuzz"`
	CustomerNumber    []string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
	VolcAccountID     []string `form:"VolcAccountID" json:"VolcAccountID,omitempty" query:"VolcAccountID"`
	PageNumber        *int32   `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize          *int32   `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action            string   `json:"Action,required" query:"Action,required"`
	Version           *string  `json:"Version,omitempty" query:"Version"`
}

type ListCustomerVolcAccountResponse struct {
	Result           []*ListCustomerVolcAccountResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata           `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type ListCustomerVolcAccountResult struct {
	VolcAccountID   *string `form:"VolcAccountID" json:"VolcAccountID,omitempty" query:"VolcAccountID"`
	VolcAccountName *string `form:"VolcAccountName" json:"VolcAccountName,omitempty" query:"VolcAccountName"`
	CustomerName    *string `form:"CustomerName" json:"CustomerName,omitempty" query:"CustomerName"`
	CustomerNumber  *string `form:"CustomerNumber" json:"CustomerNumber,omitempty" query:"CustomerNumber"`
}
