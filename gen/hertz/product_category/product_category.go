// This file is automatically generated. Do not modify.

package product_category

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
)

var _ = fmt.Sprintf

type Argument struct {
	Key  string `form:"Key,required" json:"Key,required" query:"Key,required"`
	Name string `form:"Name,required" json:"Name,required" query:"Name,required"`
}

type ProductCategory struct {
	Product                   string      `form:"Product,required" json:"Product,required" query:"Product,required"`
	SubProduct                string      `form:"SubProduct,required" json:"SubProduct,required" query:"SubProduct,required"`
	ResourceType              string      `form:"ResourceType,required" json:"ResourceType,required" query:"ResourceType,required"`
	ProductCn                 string      `form:"ProductCn,required" json:"ProductCn,required" query:"ProductCn,required"`
	SubProductCn              string      `form:"SubProductCn,required" json:"SubProductCn,required" query:"SubProductCn,required"`
	ResourceTypeCn            string      `form:"ResourceTypeCn,required" json:"ResourceTypeCn,required" query:"ResourceTypeCn,required"`
	Id                        int64       `form:"ID,required" json:"ID,required" query:"ID,required"`
	Arguments                 []*Argument `form:"Arguments,required" json:"Arguments,required" query:"Arguments,required"`
	GrafanaTemplate           *string     `form:"GrafanaTemplate" json:"GrafanaTemplate,omitempty" query:"GrafanaTemplate"`
	MaxDurationHour           *int64      `form:"MaxDurationHour" json:"MaxDurationHour,omitempty" query:"MaxDurationHour"`
	MaxStartTimeBeforeNowHour *int64      `form:"MaxStartTimeBeforeNowHour" json:"MaxStartTimeBeforeNowHour,omitempty" query:"MaxStartTimeBeforeNowHour"`
}

type QueryProductCategoryListByIDsReq struct {
	IDs     []int64 `form:"IDs,required" json:"IDs,required" query:"IDs,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type QueryProductCategoryListReq struct {
	Id             *int64  `form:"ID" json:"ID,omitempty" query:"ID"`
	Product        *string `form:"Product" json:"Product,omitempty" query:"Product"`
	SubProduct     *string `form:"SubProduct" json:"SubProduct,omitempty" query:"SubProduct"`
	ResourceType   *string `form:"ResourceType" json:"ResourceType,omitempty" query:"ResourceType"`
	ProductCn      *string `form:"ProductCn" json:"ProductCn,omitempty" query:"ProductCn"`
	SubProductCn   *string `form:"SubProductCn" json:"SubProductCn,omitempty" query:"SubProductCn"`
	ResourceTypeCn *string `form:"ResourceTypeCn" json:"ResourceTypeCn,omitempty" query:"ResourceTypeCn"`
	Action         string  `json:"Action,required" query:"Action,required"`
	Version        *string `json:"Version,omitempty" query:"Version"`
}

type QueryProductCategoryListResp struct {
	Result           *QueryProductCategoryListResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata          `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryProductCategoryListResult struct {
	ProductCategoryList []*ProductCategory `form:"ProductCategoryList,required" json:"ProductCategoryList,required" query:"ProductCategoryList,required"`
}
