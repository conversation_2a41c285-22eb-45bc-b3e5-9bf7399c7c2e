// This file is automatically generated. Do not modify.

package diagnose_task_v2

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/product_category"
)

var _ = fmt.Sprintf

type CreateDiagnoseTaskV2Req struct {
	Name               string  `form:"Name,required" json:"Name,required" query:"Name,required"`
	DiagnoseType       int32   `form:"DiagnoseType,required" json:"DiagnoseType,required" query:"DiagnoseType,required"`
	DiagnoseTemplateID *int64  `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	DiagnoseItemIDs    []int64 `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	Dimension          string  `form:"Dimension,required" json:"Dimension,required" query:"Dimension,required"`
	CreateUserID       string  `form:"CreateUserID,required" json:"CreateUserID,required" query:"CreateUserID,required"`
	TicketID           *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	Origin             *int32  `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type CreateDiagnoseTaskV2Resp struct {
	Id               int64                  `form:"ID,required" json:"ID,required" query:"ID,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DeleteDiagnoseTaskReq struct {
	Id      int64   `form:"ID,required" json:"ID,required" query:"ID,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type DeleteDiagnoseTaskResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type DiagnoseItem struct {
	Id              int64                             `form:"ID,required" json:"ID,required" query:"ID,required"`
	Name            *string                           `form:"Name" json:"Name,omitempty" query:"Name"`
	ProductCategory *product_category.ProductCategory `form:"ProductCategory,required" json:"ProductCategory,required" query:"ProductCategory,required"`
}

type DiagnoseTask struct {
	Id                 int64                               `form:"ID,required" json:"ID,required" query:"ID,required"`
	Name               *string                             `form:"Name" json:"Name,omitempty" query:"Name"`
	DiagnoseType       *int32                              `form:"DiagnoseType" json:"DiagnoseType,omitempty" query:"DiagnoseType"`
	UpdateUserID       *string                             `form:"UpdateUserID" json:"UpdateUserID,omitempty" query:"UpdateUserID"`
	UpdateTime         *string                             `form:"UpdateTime" json:"UpdateTime,omitempty" query:"UpdateTime"`
	LastRunTime        *string                             `form:"LastRunTime" json:"LastRunTime,omitempty" query:"LastRunTime"`
	CreateTime         *string                             `form:"CreateTime" json:"CreateTime,omitempty" query:"CreateTime"`
	CreateUserID       *string                             `form:"CreateUserID" json:"CreateUserID,omitempty" query:"CreateUserID"`
	Origin             *int32                              `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	DiagnoseItems      []*DiagnoseItem                     `form:"DiagnoseItems" json:"DiagnoseItems,omitempty" query:"DiagnoseItems"`
	ProductCategorys   []*product_category.ProductCategory `form:"ProductCategorys" json:"ProductCategorys,omitempty" query:"ProductCategorys"`
	Dimension          *string                             `form:"Dimension" json:"Dimension,omitempty" query:"Dimension"`
	TicketID           *string                             `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	DiagnoseTemplateID *int64                              `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
}

type QueryDiagnoseTaskDetailReq struct {
	Id      int64   `form:"ID,required" json:"ID,required" query:"ID,required"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskDetailResp struct {
	Result           *QueryDiagnoseTaskDetailResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata         `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskDetailResult struct {
	DiagnoseTaskInfo *DiagnoseTask `form:"DiagnoseTaskInfo,required" json:"DiagnoseTaskInfo,required" query:"DiagnoseTaskInfo,required"`
}

type QueryDiagnoseTaskListReq struct {
	Name                 *string `form:"Name" json:"Name,omitempty" query:"Name"`
	Origin               *int32  `form:"Origin" json:"Origin,omitempty" query:"Origin"`
	DiagnoseTemplateID   *int64  `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	PageNumber           *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize             *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	AscSortByLastRunTime *bool   `form:"AscSortByLastRunTime" json:"AscSortByLastRunTime,omitempty" query:"AscSortByLastRunTime"`
	Action               string  `json:"Action,required" query:"Action,required"`
	Version              *string `json:"Version,omitempty" query:"Version"`
}

type QueryDiagnoseTaskListResp struct {
	Result           *QueryDiagnoseTaskListResult `form:"Result" json:"Result,omitempty" query:"Result"`
	ResponseMetadata *base.ResponseMetadata       `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryDiagnoseTaskListResult struct {
	DiagnoseTaskList []*DiagnoseTask    `form:"DiagnoseTaskList,required" json:"DiagnoseTaskList,required" query:"DiagnoseTaskList,required"`
	Pagination       *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type UpdateDiagnoseTaskReq struct {
	Id                 int64   `form:"ID,required" json:"ID,required" query:"ID,required"`
	Name               *string `form:"Name" json:"Name,omitempty" query:"Name"`
	DiagnoseType       *int32  `form:"DiagnoseType" json:"DiagnoseType,omitempty" query:"DiagnoseType"`
	DiagnoseTemplateID *int64  `form:"DiagnoseTemplateID" json:"DiagnoseTemplateID,omitempty" query:"DiagnoseTemplateID"`
	DiagnoseItemIDs    []int64 `form:"DiagnoseItemIDs" json:"DiagnoseItemIDs,omitempty" query:"DiagnoseItemIDs"`
	Dimension          *string `form:"Dimension" json:"Dimension,omitempty" query:"Dimension"`
	UpdateUserID       string  `form:"UpdateUserID,required" json:"UpdateUserID,required" query:"UpdateUserID,required"`
	TicketID           *string `form:"TicketID" json:"TicketID,omitempty" query:"TicketID"`
	ProductCategoryIDs []int64 `form:"ProductCategoryIDs" json:"ProductCategoryIDs,omitempty" query:"ProductCategoryIDs"`
	Action             string  `json:"Action,required" query:"Action,required"`
	Version            *string `json:"Version,omitempty" query:"Version"`
}

type UpdateDiagnoseTaskResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}
