// This file is automatically generated. Do not modify.

package aksk

import (
	"fmt"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/base"
	"code.byted.org/volcengine-support/cloud-sherlock/gen/hertz/common"
)

var _ = fmt.Sprintf

type AppAkSk struct {
	AppID       string  `form:"AppID,required" json:"AppID,required" query:"AppID,required"`
	AppName     *string `form:"AppName" json:"AppName,omitempty" query:"AppName"`
	AppKey      string  `form:"AppKey,required" json:"AppKey,required" query:"AppKey,required"`
	AppSecret   string  `form:"AppSecret,required" json:"AppSecret,required" query:"AppSecret,required"`
	OutDateTime *string `form:"OutDateTime" json:"OutDateTime,omitempty" query:"OutDateTime"`
}

type CreateAppAkSkReq struct {
	AppID       string  `form:"AppID,required" json:"AppID,required" query:"AppID,required"`
	AppName     *string `form:"AppName" json:"AppName,omitempty" query:"AppName"`
	OutDateTime *string `form:"OutDateTime" json:"OutDateTime,omitempty" query:"OutDateTime"`
	Action      string  `json:"Action,required" query:"Action,required"`
	Version     *string `json:"Version,omitempty" query:"Version"`
}

type CreateAppAkSkResp struct {
	Result           *CreateAppAkSkResult   `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type CreateAppAkSkResult struct {
	AppKey    string `form:"AppKey,required" json:"AppKey,required" query:"AppKey,required"`
	AppSecret string `form:"AppSecret,required" json:"AppSecret,required" query:"AppSecret,required"`
}

type DeleteAppAkSkReq struct {
	AppID   string  `form:"AppID,required" json:"AppID,required" query:"AppID,required"`
	AppName *string `form:"AppName" json:"AppName,omitempty" query:"AppName"`
	Action  string  `json:"Action,required" query:"Action,required"`
	Version *string `json:"Version,omitempty" query:"Version"`
}

type DeleteAppAkSkResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryAppAkSkListReq struct {
	AppID       *string `form:"AppID" json:"AppID,omitempty" query:"AppID"`
	AppName     *string `form:"AppName" json:"AppName,omitempty" query:"AppName"`
	OutDateTime *string `form:"OutDateTime" json:"OutDateTime,omitempty" query:"OutDateTime"`
	AppKey      *string `form:"AppKey" json:"AppKey,omitempty" query:"AppKey"`
	PageNumber  *int32  `form:"PageNumber" json:"PageNumber,omitempty" query:"PageNumber"`
	PageSize    *int32  `form:"PageSize" json:"PageSize,omitempty" query:"PageSize"`
	Action      string  `json:"Action,required" query:"Action,required"`
	Version     *string `json:"Version,omitempty" query:"Version"`
}

type QueryAppAkSkListResp struct {
	Result           *QueryAppAkSkListResult `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata  `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryAppAkSkListResult struct {
	AppAkSkList []*AppAkSk         `form:"AppAkSkList,required" json:"AppAkSkList,required" query:"AppAkSkList,required"`
	Pagination  *common.Pagination `form:"Pagination" json:"Pagination,omitempty" query:"Pagination"`
}

type QueryAppAkSkReq struct {
	AppID  string `form:"AppID,required" json:"AppID,required" query:"AppID,required"`
	Action string `json:"Action,required" query:"Action,required"`
}

type QueryAppAkSkResp struct {
	Result           *QueryAppAkSkResult    `form:"Result,required" json:"Result,required" query:"Result,required"`
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}

type QueryAppAkSkResult struct {
	AppAkSk *AppAkSk `form:"AppAkSk,required" json:"AppAkSk,required" query:"AppAkSk,required"`
}

type UpdateAppAkSkReq struct {
	AppID       string  `form:"AppID,required" json:"AppID,required" query:"AppID,required"`
	AppName     *string `form:"AppName" json:"AppName,omitempty" query:"AppName"`
	OutDateTime *string `form:"OutDateTime" json:"OutDateTime,omitempty" query:"OutDateTime"`
	Action      string  `json:"Action,required" query:"Action,required"`
	Version     *string `json:"Version,omitempty" query:"Version"`
}

type UpdateAppAkSkResp struct {
	ResponseMetadata *base.ResponseMetadata `form:"ResponseMetadata" json:"ResponseMetadata,omitempty" query:"ResponseMetadata"`
}
