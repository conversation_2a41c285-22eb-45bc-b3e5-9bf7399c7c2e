// This file is automatically generated. Do not modify.

package error_code

import (
	"fmt"
	"strconv"
)

var _ = fmt.Sprintf

type ErrorCode int32

const (
	ErrorCodeErrorMonitorEndTimeBeforeStartTime                 ErrorCode = -180100023
	ErrorCodeErrorMonitorTimeBeforeNow                          ErrorCode = -180100022
	ErrorCodeErrorMonitorTimeDuration                           ErrorCode = -180100021
	ErrorCodeErrorCodeAkSkNotMatch                              ErrorCode = -180100020
	ErrorCodeErrorCodeAkSkExpired                               ErrorCode = -180100019
	ErrorCodeErrorCodeAkSkInvalid                               ErrorCode = -180100018
	ErrorCodeErrorCodeAkSkMissing                               ErrorCode = -180100017
	ErrorCodeStoreRedisError                                    ErrorCode = -180100016
	ErrorCodeAuthTokenMissing                                   ErrorCode = -180100015
	ErrorCodeAuthTokenInvalid                                   ErrorCode = -180100014
	ErrorCodeAuthStateInvalid                                   ErrorCode = -180100013
	ErrorCodeErrRegisterDiagnoseTemplateSuccessRelateItemFailed ErrorCode = -180100012
	ErrorCodeDataAlreadyExist                                   ErrorCode = -180100011
	ErrorCodeBizError                                           ErrorCode = -18020001
	ErrorCodeUndefinedError                                     ErrorCode = -18010999
	ErrorCodeTccConfigNotFound                                  ErrorCode = -18010010
	ErrorCodeReLoadingFrontEndError                             ErrorCode = -18010009
	ErrorCodeUpStreamSystemError                                ErrorCode = -18010008
	ErrorCodeUserHasNoViewableData                              ErrorCode = -18010007
	ErrorCodeDatabaseError                                      ErrorCode = -18010006
	ErrorCodeDataNotFound                                       ErrorCode = -18010005
	ErrorCodeRequestParamInvalid                                ErrorCode = -18010004
	ErrorCodeNoAuthentication                                   ErrorCode = -18010003
	ErrorCodeUnAuthentication                                   ErrorCode = -18010002
	ErrorCodeCommonError                                        ErrorCode = -18010001
)

var (
	ErrorCodeByName = map[string]ErrorCode{
		"ErrorCode.ErrorMonitorEndTimeBeforeStartTime":                 ErrorCodeErrorMonitorEndTimeBeforeStartTime,
		"ErrorCode.ErrorMonitorTimeBeforeNow":                          ErrorCodeErrorMonitorTimeBeforeNow,
		"ErrorCode.ErrorMonitorTimeDuration":                           ErrorCodeErrorMonitorTimeDuration,
		"ErrorCode.ErrorCodeAkSkNotMatch":                              ErrorCodeErrorCodeAkSkNotMatch,
		"ErrorCode.ErrorCodeAkSkExpired":                               ErrorCodeErrorCodeAkSkExpired,
		"ErrorCode.ErrorCodeAkSkInvalid":                               ErrorCodeErrorCodeAkSkInvalid,
		"ErrorCode.ErrorCodeAkSkMissing":                               ErrorCodeErrorCodeAkSkMissing,
		"ErrorCode.StoreRedisError":                                    ErrorCodeStoreRedisError,
		"ErrorCode.AuthTokenMissing":                                   ErrorCodeAuthTokenMissing,
		"ErrorCode.AuthTokenInvalid":                                   ErrorCodeAuthTokenInvalid,
		"ErrorCode.AuthStateInvalid":                                   ErrorCodeAuthStateInvalid,
		"ErrorCode.ErrRegisterDiagnoseTemplateSuccessRelateItemFailed": ErrorCodeErrRegisterDiagnoseTemplateSuccessRelateItemFailed,
		"ErrorCode.DataAlreadyExist":                                   ErrorCodeDataAlreadyExist,
		"ErrorCode.BizError":                                           ErrorCodeBizError,
		"ErrorCode.UndefinedError":                                     ErrorCodeUndefinedError,
		"ErrorCode.TccConfigNotFound":                                  ErrorCodeTccConfigNotFound,
		"ErrorCode.ReLoadingFrontEndError":                             ErrorCodeReLoadingFrontEndError,
		"ErrorCode.UpStreamSystemError":                                ErrorCodeUpStreamSystemError,
		"ErrorCode.UserHasNoViewableData":                              ErrorCodeUserHasNoViewableData,
		"ErrorCode.DatabaseError":                                      ErrorCodeDatabaseError,
		"ErrorCode.DataNotFound":                                       ErrorCodeDataNotFound,
		"ErrorCode.RequestParamInvalid":                                ErrorCodeRequestParamInvalid,
		"ErrorCode.NoAuthentication":                                   ErrorCodeNoAuthentication,
		"ErrorCode.UnAuthentication":                                   ErrorCodeUnAuthentication,
		"ErrorCode.CommonError":                                        ErrorCodeCommonError,
	}
	ErrorCodeByValue = map[ErrorCode]string{
		ErrorCodeErrorMonitorEndTimeBeforeStartTime:                 "ErrorCode.ErrorMonitorEndTimeBeforeStartTime",
		ErrorCodeErrorMonitorTimeBeforeNow:                          "ErrorCode.ErrorMonitorTimeBeforeNow",
		ErrorCodeErrorMonitorTimeDuration:                           "ErrorCode.ErrorMonitorTimeDuration",
		ErrorCodeErrorCodeAkSkNotMatch:                              "ErrorCode.ErrorCodeAkSkNotMatch",
		ErrorCodeErrorCodeAkSkExpired:                               "ErrorCode.ErrorCodeAkSkExpired",
		ErrorCodeErrorCodeAkSkInvalid:                               "ErrorCode.ErrorCodeAkSkInvalid",
		ErrorCodeErrorCodeAkSkMissing:                               "ErrorCode.ErrorCodeAkSkMissing",
		ErrorCodeStoreRedisError:                                    "ErrorCode.StoreRedisError",
		ErrorCodeAuthTokenMissing:                                   "ErrorCode.AuthTokenMissing",
		ErrorCodeAuthTokenInvalid:                                   "ErrorCode.AuthTokenInvalid",
		ErrorCodeAuthStateInvalid:                                   "ErrorCode.AuthStateInvalid",
		ErrorCodeErrRegisterDiagnoseTemplateSuccessRelateItemFailed: "ErrorCode.ErrRegisterDiagnoseTemplateSuccessRelateItemFailed",
		ErrorCodeDataAlreadyExist:                                   "ErrorCode.DataAlreadyExist",
		ErrorCodeBizError:                                           "ErrorCode.BizError",
		ErrorCodeUndefinedError:                                     "ErrorCode.UndefinedError",
		ErrorCodeTccConfigNotFound:                                  "ErrorCode.TccConfigNotFound",
		ErrorCodeReLoadingFrontEndError:                             "ErrorCode.ReLoadingFrontEndError",
		ErrorCodeUpStreamSystemError:                                "ErrorCode.UpStreamSystemError",
		ErrorCodeUserHasNoViewableData:                              "ErrorCode.UserHasNoViewableData",
		ErrorCodeDatabaseError:                                      "ErrorCode.DatabaseError",
		ErrorCodeDataNotFound:                                       "ErrorCode.DataNotFound",
		ErrorCodeRequestParamInvalid:                                "ErrorCode.RequestParamInvalid",
		ErrorCodeNoAuthentication:                                   "ErrorCode.NoAuthentication",
		ErrorCodeUnAuthentication:                                   "ErrorCode.UnAuthentication",
		ErrorCodeCommonError:                                        "ErrorCode.CommonError",
	}
)

func (e ErrorCode) String() string {
	name := ErrorCodeByValue[e]
	if name == "" {
		name = fmt.Sprintf("Unknown enum value ErrorCode(%d)", e)
	}
	return name
}

func (e *ErrorCode) UnmarshalJSON(b []byte) error {
	st := string(b)
	if st[0] == '"' {
		*e = ErrorCode(ErrorCodeByName[st[1:len(st)-1]])
		return nil
	}
	i, err := strconv.Atoi(st)
	*e = ErrorCode(i)
	return err
}
