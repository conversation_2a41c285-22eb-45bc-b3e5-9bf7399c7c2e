// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiscourseFeeback = "discourse_feeback"

// DiscourseFeeback 智能问诊反馈表
type DiscourseFeeback struct {
	ID         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:消息唯一标识（主键）" json:"ID"`  // 消息唯一标识（主键）
	SessionID  string    `gorm:"column:session_id;not null;comment:会话ID（关联会话表）" json:"SessionID"`       // 会话ID（关联会话表）
	MessageID  string    `gorm:"column:message_id;not null;comment:消息内容ID（唯一标识消息内容）" json:"MessageID"`  // 消息内容ID（唯一标识消息内容）
	Type       int32     `gorm:"column:type;not null;comment:消息类型（如文本、图片、视频等，可通过枚举值定义）" json:"Type"`    // 消息类型（如文本、图片、视频等，可通过枚举值定义）
	Upvote     *bool     `gorm:"column:upvote;comment:是否点赞（0=否，1=是）" json:"Upvote"`                     // 是否点赞（0=否，1=是）
	CreateTime time.Time `gorm:"column:create_time;not null;comment:创建时间（时间戳，单位：毫秒）" json:"CreateTime"` // 创建时间（时间戳，单位：毫秒）
}

// TableName DiscourseFeeback's table name
func (*DiscourseFeeback) TableName() string {
	return TableNameDiscourseFeeback
}
