// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseAuth = "diagnose_auth"

// DiagnoseAuth 诊断授权表
type DiagnoseAuth struct {
	ID                   int64      `gorm:"column:id;primaryKey;comment:主键" json:"ID"`                                        // 主键
	RemoteID             *int64     `gorm:"column:remote_id;comment:远程运维平台数据库中该条数据对应的id" json:"RemoteID"`                     // 远程运维平台数据库中该条数据对应的id
	ApplicationPolicy    *string    `gorm:"column:application_policy;comment:策略名称(申请策略)，名称之间用“，”隔开" json:"ApplicationPolicy"` // 策略名称(申请策略)，名称之间用“，”隔开
	CreatedTime          *time.Time `gorm:"column:created_time;comment:授权提交时间（发起申请的时间）" json:"CreatedTime"`                   // 授权提交时间（发起申请的时间）
	UpdateTime           *time.Time `gorm:"column:update_time;comment:更新时间，可借助该字段同步官网的数据" json:"UpdateTime"`                  // 更新时间，可借助该字段同步官网的数据
	StartTime            *time.Time `gorm:"column:start_time;comment:授权开始时间（通过后）" json:"StartTime"`                           // 授权开始时间（通过后）
	EndTime              *time.Time `gorm:"column:end_time;comment:授权结束时间" json:"EndTime"`                                    // 授权结束时间
	ExpiredTime          *time.Time `gorm:"column:expired_time;comment:授权过期时间" json:"ExpiredTime"`                            // 授权过期时间
	Status               *int32     `gorm:"column:status;comment:状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期" json:"Status"`           // 状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期
	AccountID            *string    `gorm:"column:account_id;comment:火山账号id" json:"AccountID"`                                // 火山账号id
	ApplyUser            *string    `gorm:"column:apply_user;comment:申请人邮箱" json:"ApplyUser"`                                 // 申请人邮箱
	TicketD              *string    `gorm:"column:ticket_d;comment:工单id" json:"TicketD"`                                      // 工单id
	DiagnoseTemplateName *string    `gorm:"column:diagnose_template_name;comment:诊断场景名称" json:"DiagnoseTemplateName"`         // 诊断场景名称
	ProductCategoryID    *int32     `gorm:"column:product_category_id;comment:产品类型id" json:"ProductCategoryID"`               // 产品类型id
	InstanseIds          *string    `gorm:"column:instanse_ids;comment:实例id" json:"InstanseIDs"`                              // 实例id
}

// TableName DiagnoseAuth's table name
func (*DiagnoseAuth) TableName() string {
	return TableNameDiagnoseAuth
}
