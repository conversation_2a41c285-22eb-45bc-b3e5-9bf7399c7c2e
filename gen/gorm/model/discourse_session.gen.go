// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiscourseSession = "discourse_session"

// DiscourseSession 用户对话会话状态表
type DiscourseSession struct {
	ID               int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"ID"`  // 主键ID
	UserID           string     `gorm:"column:user_id;not null;comment:用户id(email)" json:"UserID"`       // 用户id(email)
	SessionID        string     `gorm:"column:session_id;not null;comment:会话" json:"SessionID"`          // 会话
	Title            string     `gorm:"column:title;not null;comment:第一条消息，用于历史列表展示（20字）" json:"Title"`  // 第一条消息，用于历史列表展示（20字）
	IntentionSession *string    `gorm:"column:intention_session;comment:意图会话" json:"IntentionSession"`   // 意图会话
	DiagnoseSession  *string    `gorm:"column:diagnose_session;comment:诊断会话" json:"DiagnoseSession"`     // 诊断会话
	IsHistory        *bool      `gorm:"column:is_history;comment:是否关闭" json:"IsHistory"`                 // 是否关闭
	StartDate        *time.Time `gorm:"column:start_date;comment:用户会话开始时间" json:"StartDate"`             // 用户会话开始时间
	SummarySession   *string    `gorm:"column:summary_session;comment:总结会话 （无状态）" json:"SummarySession"` // 总结会话 （无状态）
}

// TableName DiscourseSession's table name
func (*DiscourseSession) TableName() string {
	return TableNameDiscourseSession
}
