// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseTaskV2 = "diagnose_task_v2"

// DiagnoseTaskV2 诊断任务表V2
type DiagnoseTaskV2 struct {
	ID                 int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:诊断任务，自增ID" json:"ID"`    // 诊断任务，自增ID
	Name               *string    `gorm:"column:name;comment:诊断任务名称" json:"Name"`                                 // 诊断任务名称
	DiagnoseTemplateID *int64     `gorm:"column:diagnose_template_id;comment:诊断场景模板ID" json:"DiagnoseTemplateID"` // 诊断场景模板ID
	DiagnoseItemIds    *string    `gorm:"column:diagnose_item_ids;comment:诊断项ID" json:"DiagnoseItemIDs"`          // 诊断项ID
	CreateUserID       *string    `gorm:"column:create_user_id;comment:发起人ID" json:"CreateUserID"`                // 发起人ID
	CreateUserName     *string    `gorm:"column:create_user_name;comment:发起人姓名" json:"CreateUserName"`            // 发起人姓名
	UpdateUserID       *string    `gorm:"column:update_user_id;comment:更新人ID" json:"UpdateUserID"`                // 更新人ID
	UpdateUserName     *string    `gorm:"column:update_user_name;comment:更新人姓名" json:"UpdateUserName"`            // 更新人姓名
	UpdateTime         *time.Time `gorm:"column:update_time;comment:更新时间" json:"UpdateTime"`                      // 更新时间
	LastRunTime        *time.Time `gorm:"column:last_run_time;comment:最近执行时间" json:"LastRunTime"`                 // 最近执行时间
	Dimension          *string    `gorm:"column:dimension;comment:维度" json:"Dimension"`                           // 维度
	Origin             *int32     `gorm:"column:origin;comment:来源 1-工单 2-工具平台" json:"Origin"`                     // 来源 1-工单 2-工具平台
	CreateTime         *time.Time `gorm:"column:create_time;comment:创建时间" json:"CreateTime"`                      // 创建时间
	DiagnoseType       *int32     `gorm:"column:diagnose_type;comment:诊断类型 1-原子 2-编排" json:"DiagnoseType"`        // 诊断类型 1-原子 2-编排
	TicketID           *string    `gorm:"column:ticket_id;comment:关联工单ID" json:"TicketID"`                        // 关联工单ID
	ProductCategoryIds *string    `gorm:"column:product_category_ids;comment:产品类型ID" json:"ProductCategoryIDs"`   // 产品类型ID
}

// TableName DiagnoseTaskV2's table name
func (*DiagnoseTaskV2) TableName() string {
	return TableNameDiagnoseTaskV2
}
