// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseTask = "diagnose_task"

// DiagnoseTask 诊断任务表
type DiagnoseTask struct {
	ID                      int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`                     // 自增ID
	TicketID                *string    `gorm:"column:ticket_id;comment:关联工单ID" json:"TicketID"`                                    // 关联工单ID
	DiagnoseTemplateID      *int64     `gorm:"column:diagnose_template_id;comment:诊断场景模板ID" json:"DiagnoseTemplateID"`             // 诊断场景模板ID
	DiagnoseItemIds         *string    `gorm:"column:diagnose_item_ids;comment:诊断项ID" json:"DiagnoseItemIDs"`                      // 诊断项ID
	StartTime               *time.Time `gorm:"column:start_time;comment:开始时间" json:"StartTime"`                                    // 开始时间
	EndTime                 *time.Time `gorm:"column:end_time;comment:结束时间" json:"EndTime"`                                        // 结束时间
	Resources               *string    `gorm:"column:resources;comment:诊断资源" json:"Resources"`                                     // 诊断资源
	Status                  *string    `gorm:"column:status;comment:诊断任务状态" json:"Status"`                                         // 诊断任务状态
	FeedbackID              *int64     `gorm:"column:feedback_id;comment:反馈ID" json:"FeedbackID"`                                  // 反馈ID
	AccountID               *string    `gorm:"column:account_id;comment:诊断账号ID" json:"AccountID"`                                  // 诊断账号ID
	CreateUserID            *string    `gorm:"column:create_user_id;comment:发起人ID" json:"CreateUserID"`                            // 发起人ID
	CreateUserName          *string    `gorm:"column:create_user_name;comment:发起人姓名" json:"CreateUserName"`                        // 发起人姓名
	DiagnoseStartTime       *int64     `gorm:"column:diagnose_start_time;comment:诊断开始时间" json:"DiagnoseStartTime"`                 // 诊断开始时间
	DiagnoseEndTime         *int64     `gorm:"column:diagnose_end_time;comment:诊断结束时间" json:"DiagnoseEndTime"`                     // 诊断结束时间
	FinishedDiagnoseItemIds *string    `gorm:"column:finished_diagnose_item_ids;comment:已完成的诊断项ID" json:"FinishedDiagnoseItemIDs"` // 已完成的诊断项ID
	WaitingDiagnoseItemIds  *string    `gorm:"column:waiting_diagnose_item_ids;comment:运行中的诊断项ID" json:"WaitingDiagnoseItemIDs"`   // 运行中的诊断项ID
	Removed                 *bool      `gorm:"column:removed;comment:是否被转移" json:"Removed"`                                        // 是否被转移
}

// TableName DiagnoseTask's table name
func (*DiagnoseTask) TableName() string {
	return TableNameDiagnoseTask
}
