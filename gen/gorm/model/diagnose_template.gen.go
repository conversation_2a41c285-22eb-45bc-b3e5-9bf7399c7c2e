// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseTemplate = "diagnose_template"

// DiagnoseTemplate 场景模版信息表
type DiagnoseTemplate struct {
	DiagnoseTemplateID   int64      `gorm:"column:diagnose_template_id;primaryKey;autoIncrement:true;comment:诊断场景Id" json:"DiagnoseTemplateID"` // 诊断场景Id
	DiagnoseTemplateName *string    `gorm:"column:diagnose_template_name;comment:诊断场景名称" json:"DiagnoseTemplateName"`                           // 诊断场景名称
	CreateTime           *time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:诊断场景创建时间" json:"CreateTime"`                    // 诊断场景创建时间
	CreatorID            *string    `gorm:"column:creator_id;comment:创建人账号Id" json:"CreatorID"`                                                 // 创建人账号Id
	CreatorName          *string    `gorm:"column:creator_name;comment:创建人账号名" json:"CreatorName"`                                              // 创建人账号名
	UpdateTime           *time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:诊断场景更新时间" json:"UpdateTime"`                    // 诊断场景更新时间
	UpdaterID            *string    `gorm:"column:updater_id;comment:更新人账号Id" json:"UpdaterID"`                                                 // 更新人账号Id
	UpdaterName          *string    `gorm:"column:updater_name;comment:更新人账号名" json:"UpdaterName"`                                              // 更新人账号名
	DeleteAt             *time.Time `gorm:"column:delete_at;comment:诊断场景删除时间" json:"DeleteAt"`                                                  // 诊断场景删除时间
	DeleterID            *string    `gorm:"column:deleter_id;comment:删除人账号Id" json:"DeleterID"`                                                 // 删除人账号Id
	DeleterName          *string    `gorm:"column:deleter_name;comment:删除人账号名" json:"DeleterName"`                                              // 删除人账号名
	TemplateDescription  *string    `gorm:"column:template_description;comment:场景描述" json:"TemplateDescription"`                                // 场景描述
	Suggestion           *string    `gorm:"column:suggestion;comment:场景处理建议" json:"Suggestion"`                                                 // 场景处理建议
	StateMachineInfo     *string    `gorm:"column:state_machine_info;comment:状态机信息" json:"StateMachineInfo"`                                    // 状态机信息
	Inputs               *string    `gorm:"column:inputs;comment:执行该诊断场景的输入" json:"Inputs"`                                                     // 执行该诊断场景的输入
	Status               string     `gorm:"column:status;not null;default:on;comment:诊断模版的状态" json:"Status"`                                    // 诊断模版的状态
	ResponsiblePerson    string     `gorm:"column:responsible_person;not null;default:default;comment:诊断模版的负责人" json:"ResponsiblePerson"`       // 诊断模版的负责人
	Product              *string    `gorm:"column:product;comment:产品类型" json:"Product"`                                                         // 产品类型
	Subproduct           *string    `gorm:"column:subproduct;comment:子产品类型" json:"Subproduct"`                                                  // 子产品类型
	ResourceType         *string    `gorm:"column:resource_type;comment:资源类型" json:"ResourceType"`                                              // 资源类型
	ShowLevel            *int32     `gorm:"column:show_level;comment:展示优先级" json:"ShowLevel"`                                                   // 展示优先级
	ProductCategoryID    *int32     `gorm:"column:product_category_id;comment:产品分类的ID" json:"ProductCategoryID"`                                // 产品分类的ID
}

// TableName DiagnoseTemplate's table name
func (*DiagnoseTemplate) TableName() string {
	return TableNameDiagnoseTemplate
}
