// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseTaskRun = "diagnose_task_run"

// DiagnoseTaskRun 诊断任务运行表
type DiagnoseTaskRun struct {
	ID                   int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:执行ID，自增ID" json:"ID"`        // 执行ID，自增ID
	Name                 *string    `gorm:"column:name;comment:诊断任务执行名称" json:"Name"`                                   // 诊断任务执行名称
	DiagnoseTaskID       int64      `gorm:"column:diagnose_task_id;not null;comment:关联诊断任务ID" json:"DiagnoseTaskID"`    // 关联诊断任务ID
	AccountID            *string    `gorm:"column:account_id;comment:诊断账号ID" json:"AccountID"`                          // 诊断账号ID
	Resources            *string    `gorm:"column:resources;comment:诊断资源" json:"Resources"`                             // 诊断资源
	CreateUserID         *string    `gorm:"column:create_user_id;comment:发起人ID" json:"CreateUserID"`                    // 发起人ID
	CreateUserName       *string    `gorm:"column:create_user_name;comment:发起人姓名" json:"CreateUserName"`                // 发起人姓名
	StartTime            *time.Time `gorm:"column:start_time;comment:开始时间" json:"StartTime"`                            // 开始时间
	EndTime              *time.Time `gorm:"column:end_time;comment:结束时间" json:"EndTime"`                                // 结束时间
	FeedbackID           *int64     `gorm:"column:feedback_id;comment:反馈ID" json:"FeedbackID"`                          // 反馈ID
	Status               *string    `gorm:"column:status;comment:诊断任务状态" json:"Status"`                                 // 诊断任务状态
	DiagnoseStartTime    *int64     `gorm:"column:diagnose_start_time;comment:诊断开始时间" json:"DiagnoseStartTime"`         // 诊断开始时间
	DiagnoseEndTime      *int64     `gorm:"column:diagnose_end_time;comment:诊断结束时间" json:"DiagnoseEndTime"`             // 诊断结束时间
	StateMachineInfo     *string    `gorm:"column:state_machine_info;comment:诊断编排信息快照" json:"StateMachineInfo"`         // 诊断编排信息快照
	DiagnoseItemStatus   *string    `gorm:"column:diagnose_item_status;comment:诊断项运行状态" json:"DiagnoseItemStatus"`      // 诊断项运行状态
	TicketID             *string    `gorm:"column:ticket_id;comment:关联工单ID" json:"TicketID"`                            // 关联工单ID
	DiagnoseTemplateID   *int64     `gorm:"column:diagnose_template_id;comment:关联诊断模板ID" json:"DiagnoseTemplateID"`     // 关联诊断模板ID
	DiagnoseTemplateName *string    `gorm:"column:diagnose_template_name;comment:关联诊断模板名称" json:"DiagnoseTemplateName"` // 关联诊断模板名称
	DiagnoseType         *int32     `gorm:"column:diagnose_type;comment:诊断类型 1-原子 2-编排" json:"DiagnoseType"`            // 诊断类型 1-原子 2-编排
	ResourceTypeIds      *string    `gorm:"column:resource_type_ids;comment:资源类型id" json:"ResourceTypeIDs"`             // 资源类型id
	ActivityDiagnoses    *string    `gorm:"column:activity_diagnoses;comment:模板activity映射" json:"ActivityDiagnoses"`    // 模板activity映射
	Origin               int32      `gorm:"column:origin;not null;default:1;comment:来源" json:"Origin"`                  // 来源
	OldTaskID            *int64     `gorm:"column:old_task_id;comment:原始表taskID" json:"OldTaskID"`                      // 原始表taskID
}

// TableName DiagnoseTaskRun's table name
func (*DiagnoseTaskRun) TableName() string {
	return TableNameDiagnoseTaskRun
}
