// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiscourseMessage = "discourse_message"

// DiscourseMessage 对话信息表
type DiscourseMessage struct {
	ID        int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:主键ID" json:"ID"` // 主键ID
	SessionID int64     `gorm:"column:session_id;not null;comment:会话表 id" json:"SessionID"`     // 会话表 id
	Type      *int32    `gorm:"column:type;comment:类型 1-中台 2-maas" json:"Type"`                 // 类型 1-中台 2-maas
	TaskID    *int64    `gorm:"column:task_id;comment:诊断任务 id" json:"TaskID"`                   // 诊断任务 id
	TaskRunID *int64    `gorm:"column:task_run_id;comment:诊断执行任务 id" json:"TaskRunID"`          // 诊断执行任务 id
	StartDate time.Time `gorm:"column:start_date;not null;comment:时间" json:"StartDate"`         // 时间
	Summary   *string   `gorm:"column:summary;comment:总结 id" json:"Summary"`                    // 总结 id
	Recommend *string   `gorm:"column:recommend;comment:推荐 id" json:"Recommend"`                // 推荐 id
	Requery   *string   `gorm:"column:requery;comment:用户 query" json:"Requery"`                 // 用户 query
}

// TableName DiscourseMessage's table name
func (*DiscourseMessage) TableName() string {
	return TableNameDiscourseMessage
}
