// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameDiagnoseItem = "diagnose_item"

// DiagnoseItem mapped from table <diagnose_item>
type DiagnoseItem struct {
	DiagnoseItemID          int64      `gorm:"column:diagnose_item_id;primaryKey;autoIncrement:true;comment:诊断项Id" json:"DiagnoseItemID"` // 诊断项Id
	DiagnoseItemName        *string    `gorm:"column:diagnose_item_name;comment:诊断项名称" json:"DiagnoseItemName"`                           // 诊断项名称
	CreateTime              *time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:诊断项创建时间" json:"CreateTime"`            // 诊断项创建时间
	CreatorID               *string    `gorm:"column:creator_id;comment:创建人账号Id" json:"CreatorID"`                                        // 创建人账号Id
	CreatorName             *string    `gorm:"column:creator_name;comment:创建人账号名" json:"CreatorName"`                                     // 创建人账号名
	UpdateTime              *time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:诊断项更新时间" json:"UpdateTime"`            // 诊断项更新时间
	UpdaterID               *string    `gorm:"column:updater_id;comment:更新人账号Id" json:"UpdaterID"`                                        // 更新人账号Id
	UpdaterName             *string    `gorm:"column:updater_name;comment:更新人账号名" json:"UpdaterName"`                                     // 更新人账号名
	DeleteAt                *time.Time `gorm:"column:delete_at;comment:诊断项删除时间" json:"DeleteAt"`                                          // 诊断项删除时间
	DeleterID               *string    `gorm:"column:deleter_id;comment:删除人账号Id" json:"DeleterID"`                                        // 删除人账号Id
	DeleterName             *string    `gorm:"column:deleter_name;comment:删除人账号名" json:"DeleterName"`                                     // 删除人账号名
	DiagnoseItemCode        *string    `gorm:"column:diagnose_item_code;comment:诊断项编码" json:"DiagnoseItemCode"`                           // 诊断项编码
	Status                  *string    `gorm:"column:status;comment:诊断项状态" json:"Status"`                                                 // 诊断项状态
	Product                 *string    `gorm:"column:product;comment:产品分类" json:"Product"`                                                // 产品分类
	Subproduct              *string    `gorm:"column:subproduct;comment:子产品分类" json:"Subproduct"`                                         // 子产品分类
	AccessType              *string    `gorm:"column:access_type;comment:接入类型" json:"AccessType"`                                         // 接入类型
	AccessResponsiblePerson *string    `gorm:"column:access_responsible_person;comment:接入负责人" json:"AccessResponsiblePerson"`             // 接入负责人
	AccessPath              *string    `gorm:"column:access_path;comment:接入路径" json:"AccessPath"`                                         // 接入路径
	InterAction             *string    `gorm:"column:inter_action" json:"InterAction"`
	InterVersion            *string    `gorm:"column:inter_version;comment:接口版本" json:"InterVersion"`                                               // 接口版本
	Suggestion              *string    `gorm:"column:suggestion;comment:处理建议" json:"Suggestion"`                                                    // 处理建议
	SuggestionLink          *string    `gorm:"column:suggestion_link;comment:处理建议的链接" json:"SuggestionLink"`                                        // 处理建议的链接
	Timeout                 *int32     `gorm:"column:timeout;comment:超时时间（单位：秒）" json:"Timeout"`                                                    // 超时时间（单位：秒）
	InterParams             *string    `gorm:"column:inter_params;comment:接口参数" json:"InterParams"`                                                 // 接口参数
	StateMachineInfo        string     `gorm:"column:state_machine_info;not null;default:state_machine_info;comment:状态机语言" json:"StateMachineInfo"` // 状态机语言
	ResourceType            *string    `gorm:"column:resource_type;comment:资源类型" json:"ResourceType"`                                               // 资源类型
	ProductCategoryID       int64      `gorm:"column:product_category_id;not null;default:5;comment:产品类型ID" json:"ProductCategoryID"`               // 产品类型ID
	Activity                *string    `gorm:"column:activity;comment:诊断项关联的activity" json:"Activity"`                                              // 诊断项关联的activity
	DiagnoseItemDescription *string    `gorm:"column:diagnose_item_description;comment:诊断项描述" json:"DiagnoseItemDescription"`                       // 诊断项描述
	NeedAuth                bool       `gorm:"column:need_auth;not null;comment:表示该诊断项是否需要用户授权" json:"NeedAuth"`                                    // 表示该诊断项是否需要用户授权
}

// TableName DiagnoseItem's table name
func (*DiagnoseItem) TableName() string {
	return TableNameDiagnoseItem
}
