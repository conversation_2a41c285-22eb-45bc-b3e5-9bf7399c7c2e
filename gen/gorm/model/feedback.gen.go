// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameFeedback = "feedback"

// Feedback 问题反馈表
type Feedback struct {
	ID                int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`                 // 自增ID
	DiagnoseTaskID    *int64     `gorm:"column:diagnose_task_id;comment:关联任务ID" json:"DiagnoseTaskID"`                   // 关联任务ID
	Resolved          bool       `gorm:"column:resolved;not null;comment:问题是否已解决" json:"Resolved"`                       // 问题是否已解决
	Description       *string    `gorm:"column:description;comment:问题描述" json:"Description"`                             // 问题描述
	ProblemItems      *string    `gorm:"column:problem_items;comment:问题项" json:"ProblemItems"`                           // 问题项
	FeedbackUserID    *string    `gorm:"column:feedback_user_id;comment:反馈人邮箱" json:"FeedbackUserID"`                    // 反馈人邮箱
	DiagnoseRunUserID *string    `gorm:"column:diagnose_run_user_id;comment:任务执行人邮箱" json:"DiagnoseRunUserID"`           // 任务执行人邮箱
	FeedbackTime      *time.Time `gorm:"column:feedback_time;comment:反馈时间" json:"FeedbackTime"`                          // 反馈时间
	DiagnoseTaskRunID int64      `gorm:"column:diagnose_task_run_id;not null;comment:关联任务运行ID" json:"DiagnoseTaskRunID"` // 关联任务运行ID
}

// TableName Feedback's table name
func (*Feedback) TableName() string {
	return TableNameFeedback
}
