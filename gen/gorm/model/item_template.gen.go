// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameItemTemplate = "item_template"

// ItemTemplate 诊断项—场景模版-关联表
type ItemTemplate struct {
	DiagnoseItemID     int64      `gorm:"column:diagnose_item_id;primaryKey;comment:诊断项Id" json:"DiagnoseItemID"`          // 诊断项Id
	DiagnoseTemplateID int64      `gorm:"column:diagnose_template_id;primaryKey;comment:诊断模版Id" json:"DiagnoseTemplateID"` // 诊断模版Id
	CreateTime         *time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:关联创建时间" json:"CreateTime"`   // 关联创建时间
	CreatorID          *string    `gorm:"column:creator_id;comment:创建人账号Id" json:"CreatorID"`                              // 创建人账号Id
	CreatorName        *string    `gorm:"column:creator_name;comment:创建人账号名" json:"CreatorName"`                           // 创建人账号名
	DeleteAt           *time.Time `gorm:"column:delete_at;comment:诊断场景删除时间" json:"DeleteAt"`                               // 诊断场景删除时间
	DeleterID          *string    `gorm:"column:deleter_id;comment:删除人账号Id" json:"DeleterID"`                              // 删除人账号Id
	DeleterName        *string    `gorm:"column:deleter_name;comment:删除人账号名" json:"DeleterName"`                           // 删除人账号名
	Activity           *string    `gorm:"column:activity;comment:由该activity执行诊断项" json:"Activity"`                         // 由该activity执行诊断项
}

// TableName ItemTemplate's table name
func (*ItemTemplate) TableName() string {
	return TableNameItemTemplate
}
