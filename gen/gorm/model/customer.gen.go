// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameCustomer = "customer"

// Customer mapped from table <customer>
type Customer struct {
	ID                 int64                 `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`                         // 自增ID
	SfID               string                `gorm:"column:sf_id;not null;comment:sf_id" json:"SfID"`                                        // sf_id
	ParentID           string                `gorm:"column:parent_id;not null;comment:主客户sf_id" json:"ParentID"`                             // 主客户sf_id
	CustomerNumber     string                `gorm:"column:customer_number;not null;comment:客户编号" json:"CustomerNumber"`                     // 客户编号
	MainCustomerNumber string                `gorm:"column:main_customer_number;not null;comment:主客户编号" json:"MainCustomerNumber"`           // 主客户编号
	CustomerName       string                `gorm:"column:customer_name;not null;comment:客户名" json:"CustomerName"`                          // 客户名
	CustomerShortName  string                `gorm:"column:customer_short_name;not null;comment:客户简称" json:"CustomerShortName"`              // 客户简称
	Industry           string                `gorm:"column:industry;not null;comment:行业" json:"Industry"`                                    // 行业
	SubIndustry        string                `gorm:"column:sub_industry;not null;comment:二级行业" json:"SubIndustry"`                           // 二级行业
	OwnerID            string                `gorm:"column:owner_id;not null;comment:客户归属人sf_id" json:"OwnerID"`                             // 客户归属人sf_id
	OwnerEmail         string                `gorm:"column:owner_email;not null;comment:客户owner邮箱" json:"OwnerEmail"`                        // 客户owner邮箱
	Country            string                `gorm:"column:country;not null;comment:国家" json:"Country"`                                      // 国家
	Region             string                `gorm:"column:region;not null;comment:区域" json:"Region"`                                        // 区域
	Province           string                `gorm:"column:province;not null;comment:省份" json:"Province"`                                    // 省份
	City               string                `gorm:"column:city;not null;comment:城市" json:"City"`                                            // 城市
	CustomerTier       string                `gorm:"column:customer_tier;not null;comment:客户等级" json:"CustomerTier"`                         // 客户等级
	CustomerPLevel     string                `gorm:"column:customer_p_level;not null;comment:客户P级" json:"CustomerPLevel"`                    // 客户P级
	Status             string                `gorm:"column:status;not null;comment:客户状态" json:"Status"`                                      // 客户状态
	DeletedTime        soft_delete.DeletedAt `gorm:"column:deleted_time;comment:删除时间" json:"DeletedTime"`                                    // 删除时间
	CreatedTime        time.Time             `gorm:"column:created_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreatedTime"` // 创建时间
	UpdatedTime        time.Time             `gorm:"column:updated_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"UpdatedTime"` // 更新时间
	Display            int32                 `gorm:"column:display;not null;comment:展示配置" json:"Display"`                                    // 展示配置
}

// TableName Customer's table name
func (*Customer) TableName() string {
	return TableNameCustomer
}
