// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameProductCategory = "product_category"

// ProductCategory 产品类型表
type ProductCategory struct {
	ID                        int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:产品类型ID，自增ID" json:"ID"`                        // 产品类型ID，自增ID
	Product                   string  `gorm:"column:product;not null;comment:产品" json:"Product"`                                            // 产品
	SubProduct                string  `gorm:"column:sub_product;not null;comment:子产品" json:"SubProduct"`                                    // 子产品
	ResourceType              string  `gorm:"column:resource_type;not null;comment:资源类型" json:"ResourceType"`                               // 资源类型
	ProductCn                 string  `gorm:"column:product_cn;not null;comment:产品中文" json:"ProductCn"`                                     // 产品中文
	SubProductCn              string  `gorm:"column:sub_product_cn;not null;comment:子产品中文" json:"SubProductCn"`                             // 子产品中文
	ResourceTypeCn            string  `gorm:"column:resource_type_cn;not null;comment:资源类型中文" json:"ResourceTypeCn"`                        // 资源类型中文
	Arguments                 *string `gorm:"column:arguments;comment:参数" json:"Arguments"`                                                 // 参数
	GrafanaTemplate           string  `gorm:"column:grafana_template;not null;comment:Grafana配置模板" json:"GrafanaTemplate"`                  // Grafana配置模板
	MaxDurationHour           *int64  `gorm:"column:max_duration_hour;comment:监控时间限制最大时间跨度（小时）" json:"MaxDurationHour"`                     // 监控时间限制最大时间跨度（小时）
	MaxStartTimeBeforeNowHour *int64  `gorm:"column:max_start_time_before_now_hour;comment:监控最久可查询时间（小时）" json:"MaxStartTimeBeforeNowHour"` // 监控最久可查询时间（小时）
	PolicyKey                 *string `gorm:"column:policy_key;comment:远程运维平台策略标识" json:"PolicyKey"`                                        // 远程运维平台策略标识
}

// TableName ProductCategory's table name
func (*ProductCategory) TableName() string {
	return TableNameProductCategory
}
