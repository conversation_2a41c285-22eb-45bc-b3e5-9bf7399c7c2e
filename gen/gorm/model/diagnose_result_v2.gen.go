// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameDiagnoseResultV2 = "diagnose_result_v2"

// DiagnoseResultV2 诊断结果表V2
type DiagnoseResultV2 struct {
	ID                     int64   `gorm:"column:id;primaryKey;autoIncrement:true;comment:自增ID" json:"ID"`                 // 自增ID
	DiagnoseItemID         int64   `gorm:"column:diagnose_item_id;not null;comment:诊断项ID" json:"DiagnoseItemID"`           // 诊断项ID
	DiagnoseTaskRunID      int64   `gorm:"column:diagnose_task_run_id;not null;comment:关联任务运行ID" json:"DiagnoseTaskRunID"` // 关联任务运行ID
	DiagnoseResultLevelNum *int32  `gorm:"column:diagnose_result_level_num;comment:结果等级num" json:"DiagnoseResultLevelNum"` // 结果等级num
	InstanceID             string  `gorm:"column:instance_id;not null;comment:实例ID" json:"InstanceID"`                     // 实例ID
	InstanceName           *string `gorm:"column:instance_name;comment:实例名称" json:"InstanceName"`                          // 实例名称
	Region                 *string `gorm:"column:region;comment:实例区域" json:"Region"`                                       // 实例区域
	DiagnoseMessage        *string `gorm:"column:diagnose_message;comment:诊断信息" json:"DiagnoseMessage"`                    // 诊断信息
	DiagnoseResultLevel    string  `gorm:"column:diagnose_result_level;not null;comment:诊断结果" json:"DiagnoseResultLevel"`  // 诊断结果
	DiagnoseSuggestion     *string `gorm:"column:diagnose_suggestion;comment:诊断建议" json:"DiagnoseSuggestion"`              // 诊断建议
	DiagnoseOperate        *string `gorm:"column:diagnose_operate;comment:诊断操作" json:"DiagnoseOperate"`                    // 诊断操作
	Status                 *string `gorm:"column:status;comment:诊断状态(Succeed,Running,Failed)" json:"Status"`               // 诊断状态(Succeed,Running,Failed)
	ErrorInfo              *string `gorm:"column:error_info;comment:诊断错误信息" json:"ErrorInfo"`                              // 诊断错误信息
	ProductCategoryID      int64   `gorm:"column:product_category_id;not null;comment:产品类型ID" json:"ProductCategoryID"`    // 产品类型ID
}

// TableName DiagnoseResultV2's table name
func (*DiagnoseResultV2) TableName() string {
	return TableNameDiagnoseResultV2
}
