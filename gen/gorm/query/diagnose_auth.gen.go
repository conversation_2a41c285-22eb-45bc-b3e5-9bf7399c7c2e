// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseAuth(db *gorm.DB, opts ...gen.DOOption) diagnoseAuth {
	_diagnoseAuth := diagnoseAuth{}

	_diagnoseAuth.diagnoseAuthDo.UseDB(db, opts...)
	_diagnoseAuth.diagnoseAuthDo.UseModel(&model.DiagnoseAuth{})

	tableName := _diagnoseAuth.diagnoseAuthDo.TableName()
	_diagnoseAuth.ALL = field.NewAsterisk(tableName)
	_diagnoseAuth.ID = field.NewInt64(tableName, "id")
	_diagnoseAuth.RemoteID = field.NewInt64(tableName, "remote_id")
	_diagnoseAuth.ApplicationPolicy = field.NewString(tableName, "application_policy")
	_diagnoseAuth.CreatedTime = field.NewTime(tableName, "created_time")
	_diagnoseAuth.UpdateTime = field.NewTime(tableName, "update_time")
	_diagnoseAuth.StartTime = field.NewTime(tableName, "start_time")
	_diagnoseAuth.EndTime = field.NewTime(tableName, "end_time")
	_diagnoseAuth.ExpiredTime = field.NewTime(tableName, "expired_time")
	_diagnoseAuth.Status = field.NewInt32(tableName, "status")
	_diagnoseAuth.AccountID = field.NewString(tableName, "account_id")
	_diagnoseAuth.ApplyUser = field.NewString(tableName, "apply_user")
	_diagnoseAuth.TicketD = field.NewString(tableName, "ticket_d")
	_diagnoseAuth.DiagnoseTemplateName = field.NewString(tableName, "diagnose_template_name")
	_diagnoseAuth.ProductCategoryID = field.NewInt32(tableName, "product_category_id")
	_diagnoseAuth.InstanseIds = field.NewString(tableName, "instanse_ids")

	_diagnoseAuth.fillFieldMap()

	return _diagnoseAuth
}

// diagnoseAuth 诊断授权表
type diagnoseAuth struct {
	diagnoseAuthDo diagnoseAuthDo

	ALL                  field.Asterisk
	ID                   field.Int64  // 主键
	RemoteID             field.Int64  // 远程运维平台数据库中该条数据对应的id
	ApplicationPolicy    field.String // 策略名称(申请策略)，名称之间用“，”隔开
	CreatedTime          field.Time   // 授权提交时间（发起申请的时间）
	UpdateTime           field.Time   // 更新时间，可借助该字段同步官网的数据
	StartTime            field.Time   // 授权开始时间（通过后）
	EndTime              field.Time   // 授权结束时间
	ExpiredTime          field.Time   // 授权过期时间
	Status               field.Int32  // 状态：1. 待审批；2. 生效中；3. 拒绝；4. 结束；5. 过期
	AccountID            field.String // 火山账号id
	ApplyUser            field.String // 申请人邮箱
	TicketD              field.String // 工单id
	DiagnoseTemplateName field.String // 诊断场景名称
	ProductCategoryID    field.Int32  // 产品类型id
	InstanseIds          field.String // 实例id

	fieldMap map[string]field.Expr
}

func (d diagnoseAuth) Table(newTableName string) *diagnoseAuth {
	d.diagnoseAuthDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseAuth) As(alias string) *diagnoseAuth {
	d.diagnoseAuthDo.DO = *(d.diagnoseAuthDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseAuth) updateTableName(table string) *diagnoseAuth {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.RemoteID = field.NewInt64(table, "remote_id")
	d.ApplicationPolicy = field.NewString(table, "application_policy")
	d.CreatedTime = field.NewTime(table, "created_time")
	d.UpdateTime = field.NewTime(table, "update_time")
	d.StartTime = field.NewTime(table, "start_time")
	d.EndTime = field.NewTime(table, "end_time")
	d.ExpiredTime = field.NewTime(table, "expired_time")
	d.Status = field.NewInt32(table, "status")
	d.AccountID = field.NewString(table, "account_id")
	d.ApplyUser = field.NewString(table, "apply_user")
	d.TicketD = field.NewString(table, "ticket_d")
	d.DiagnoseTemplateName = field.NewString(table, "diagnose_template_name")
	d.ProductCategoryID = field.NewInt32(table, "product_category_id")
	d.InstanseIds = field.NewString(table, "instanse_ids")

	d.fillFieldMap()

	return d
}

func (d *diagnoseAuth) WithContext(ctx context.Context) *diagnoseAuthDo {
	return d.diagnoseAuthDo.WithContext(ctx)
}

func (d diagnoseAuth) TableName() string { return d.diagnoseAuthDo.TableName() }

func (d diagnoseAuth) Alias() string { return d.diagnoseAuthDo.Alias() }

func (d diagnoseAuth) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseAuthDo.Columns(cols...)
}

func (d *diagnoseAuth) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseAuth) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 15)
	d.fieldMap["id"] = d.ID
	d.fieldMap["remote_id"] = d.RemoteID
	d.fieldMap["application_policy"] = d.ApplicationPolicy
	d.fieldMap["created_time"] = d.CreatedTime
	d.fieldMap["update_time"] = d.UpdateTime
	d.fieldMap["start_time"] = d.StartTime
	d.fieldMap["end_time"] = d.EndTime
	d.fieldMap["expired_time"] = d.ExpiredTime
	d.fieldMap["status"] = d.Status
	d.fieldMap["account_id"] = d.AccountID
	d.fieldMap["apply_user"] = d.ApplyUser
	d.fieldMap["ticket_d"] = d.TicketD
	d.fieldMap["diagnose_template_name"] = d.DiagnoseTemplateName
	d.fieldMap["product_category_id"] = d.ProductCategoryID
	d.fieldMap["instanse_ids"] = d.InstanseIds
}

func (d diagnoseAuth) clone(db *gorm.DB) diagnoseAuth {
	d.diagnoseAuthDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseAuth) replaceDB(db *gorm.DB) diagnoseAuth {
	d.diagnoseAuthDo.ReplaceDB(db)
	return d
}

type diagnoseAuthDo struct{ gen.DO }

func (d diagnoseAuthDo) Debug() *diagnoseAuthDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseAuthDo) WithContext(ctx context.Context) *diagnoseAuthDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseAuthDo) ReadDB() *diagnoseAuthDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseAuthDo) WriteDB() *diagnoseAuthDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseAuthDo) Session(config *gorm.Session) *diagnoseAuthDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseAuthDo) Clauses(conds ...clause.Expression) *diagnoseAuthDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseAuthDo) Returning(value interface{}, columns ...string) *diagnoseAuthDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseAuthDo) Not(conds ...gen.Condition) *diagnoseAuthDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseAuthDo) Or(conds ...gen.Condition) *diagnoseAuthDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseAuthDo) Select(conds ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseAuthDo) Where(conds ...gen.Condition) *diagnoseAuthDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseAuthDo) Order(conds ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseAuthDo) Distinct(cols ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseAuthDo) Omit(cols ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseAuthDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseAuthDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseAuthDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseAuthDo) Group(cols ...field.Expr) *diagnoseAuthDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseAuthDo) Having(conds ...gen.Condition) *diagnoseAuthDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseAuthDo) Limit(limit int) *diagnoseAuthDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseAuthDo) Offset(offset int) *diagnoseAuthDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseAuthDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseAuthDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseAuthDo) Unscoped() *diagnoseAuthDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseAuthDo) Create(values ...*model.DiagnoseAuth) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseAuthDo) CreateInBatches(values []*model.DiagnoseAuth, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseAuthDo) Save(values ...*model.DiagnoseAuth) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseAuthDo) First() (*model.DiagnoseAuth, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuth), nil
	}
}

func (d diagnoseAuthDo) Take() (*model.DiagnoseAuth, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuth), nil
	}
}

func (d diagnoseAuthDo) Last() (*model.DiagnoseAuth, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuth), nil
	}
}

func (d diagnoseAuthDo) Find() ([]*model.DiagnoseAuth, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseAuth), err
}

func (d diagnoseAuthDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseAuth, err error) {
	buf := make([]*model.DiagnoseAuth, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseAuthDo) FindInBatches(result *[]*model.DiagnoseAuth, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseAuthDo) Attrs(attrs ...field.AssignExpr) *diagnoseAuthDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseAuthDo) Assign(attrs ...field.AssignExpr) *diagnoseAuthDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseAuthDo) Joins(fields ...field.RelationField) *diagnoseAuthDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseAuthDo) Preload(fields ...field.RelationField) *diagnoseAuthDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseAuthDo) FirstOrInit() (*model.DiagnoseAuth, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuth), nil
	}
}

func (d diagnoseAuthDo) FirstOrCreate() (*model.DiagnoseAuth, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseAuth), nil
	}
}

func (d diagnoseAuthDo) FindByPage(offset int, limit int) (result []*model.DiagnoseAuth, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseAuthDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseAuthDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseAuthDo) Delete(models ...*model.DiagnoseAuth) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseAuthDo) withDO(do gen.Dao) *diagnoseAuthDo {
	d.DO = *do.(*gen.DO)
	return d
}
