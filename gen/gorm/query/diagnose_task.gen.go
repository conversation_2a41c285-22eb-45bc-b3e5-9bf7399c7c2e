// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseTask(db *gorm.DB, opts ...gen.DOOption) diagnoseTask {
	_diagnoseTask := diagnoseTask{}

	_diagnoseTask.diagnoseTaskDo.UseDB(db, opts...)
	_diagnoseTask.diagnoseTaskDo.UseModel(&model.DiagnoseTask{})

	tableName := _diagnoseTask.diagnoseTaskDo.TableName()
	_diagnoseTask.ALL = field.NewAsterisk(tableName)
	_diagnoseTask.ID = field.NewInt64(tableName, "id")
	_diagnoseTask.TicketID = field.NewString(tableName, "ticket_id")
	_diagnoseTask.DiagnoseTemplateID = field.NewInt64(tableName, "diagnose_template_id")
	_diagnoseTask.DiagnoseItemIds = field.NewString(tableName, "diagnose_item_ids")
	_diagnoseTask.StartTime = field.NewTime(tableName, "start_time")
	_diagnoseTask.EndTime = field.NewTime(tableName, "end_time")
	_diagnoseTask.Resources = field.NewString(tableName, "resources")
	_diagnoseTask.Status = field.NewString(tableName, "status")
	_diagnoseTask.FeedbackID = field.NewInt64(tableName, "feedback_id")
	_diagnoseTask.AccountID = field.NewString(tableName, "account_id")
	_diagnoseTask.CreateUserID = field.NewString(tableName, "create_user_id")
	_diagnoseTask.CreateUserName = field.NewString(tableName, "create_user_name")
	_diagnoseTask.DiagnoseStartTime = field.NewInt64(tableName, "diagnose_start_time")
	_diagnoseTask.DiagnoseEndTime = field.NewInt64(tableName, "diagnose_end_time")
	_diagnoseTask.FinishedDiagnoseItemIds = field.NewString(tableName, "finished_diagnose_item_ids")
	_diagnoseTask.WaitingDiagnoseItemIds = field.NewString(tableName, "waiting_diagnose_item_ids")
	_diagnoseTask.Removed = field.NewBool(tableName, "removed")

	_diagnoseTask.fillFieldMap()

	return _diagnoseTask
}

// diagnoseTask 诊断任务表
type diagnoseTask struct {
	diagnoseTaskDo diagnoseTaskDo

	ALL                     field.Asterisk
	ID                      field.Int64  // 自增ID
	TicketID                field.String // 关联工单ID
	DiagnoseTemplateID      field.Int64  // 诊断场景模板ID
	DiagnoseItemIds         field.String // 诊断项ID
	StartTime               field.Time   // 开始时间
	EndTime                 field.Time   // 结束时间
	Resources               field.String // 诊断资源
	Status                  field.String // 诊断任务状态
	FeedbackID              field.Int64  // 反馈ID
	AccountID               field.String // 诊断账号ID
	CreateUserID            field.String // 发起人ID
	CreateUserName          field.String // 发起人姓名
	DiagnoseStartTime       field.Int64  // 诊断开始时间
	DiagnoseEndTime         field.Int64  // 诊断结束时间
	FinishedDiagnoseItemIds field.String // 已完成的诊断项ID
	WaitingDiagnoseItemIds  field.String // 运行中的诊断项ID
	Removed                 field.Bool   // 是否被转移

	fieldMap map[string]field.Expr
}

func (d diagnoseTask) Table(newTableName string) *diagnoseTask {
	d.diagnoseTaskDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseTask) As(alias string) *diagnoseTask {
	d.diagnoseTaskDo.DO = *(d.diagnoseTaskDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseTask) updateTableName(table string) *diagnoseTask {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.TicketID = field.NewString(table, "ticket_id")
	d.DiagnoseTemplateID = field.NewInt64(table, "diagnose_template_id")
	d.DiagnoseItemIds = field.NewString(table, "diagnose_item_ids")
	d.StartTime = field.NewTime(table, "start_time")
	d.EndTime = field.NewTime(table, "end_time")
	d.Resources = field.NewString(table, "resources")
	d.Status = field.NewString(table, "status")
	d.FeedbackID = field.NewInt64(table, "feedback_id")
	d.AccountID = field.NewString(table, "account_id")
	d.CreateUserID = field.NewString(table, "create_user_id")
	d.CreateUserName = field.NewString(table, "create_user_name")
	d.DiagnoseStartTime = field.NewInt64(table, "diagnose_start_time")
	d.DiagnoseEndTime = field.NewInt64(table, "diagnose_end_time")
	d.FinishedDiagnoseItemIds = field.NewString(table, "finished_diagnose_item_ids")
	d.WaitingDiagnoseItemIds = field.NewString(table, "waiting_diagnose_item_ids")
	d.Removed = field.NewBool(table, "removed")

	d.fillFieldMap()

	return d
}

func (d *diagnoseTask) WithContext(ctx context.Context) *diagnoseTaskDo {
	return d.diagnoseTaskDo.WithContext(ctx)
}

func (d diagnoseTask) TableName() string { return d.diagnoseTaskDo.TableName() }

func (d diagnoseTask) Alias() string { return d.diagnoseTaskDo.Alias() }

func (d diagnoseTask) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseTaskDo.Columns(cols...)
}

func (d *diagnoseTask) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseTask) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 17)
	d.fieldMap["id"] = d.ID
	d.fieldMap["ticket_id"] = d.TicketID
	d.fieldMap["diagnose_template_id"] = d.DiagnoseTemplateID
	d.fieldMap["diagnose_item_ids"] = d.DiagnoseItemIds
	d.fieldMap["start_time"] = d.StartTime
	d.fieldMap["end_time"] = d.EndTime
	d.fieldMap["resources"] = d.Resources
	d.fieldMap["status"] = d.Status
	d.fieldMap["feedback_id"] = d.FeedbackID
	d.fieldMap["account_id"] = d.AccountID
	d.fieldMap["create_user_id"] = d.CreateUserID
	d.fieldMap["create_user_name"] = d.CreateUserName
	d.fieldMap["diagnose_start_time"] = d.DiagnoseStartTime
	d.fieldMap["diagnose_end_time"] = d.DiagnoseEndTime
	d.fieldMap["finished_diagnose_item_ids"] = d.FinishedDiagnoseItemIds
	d.fieldMap["waiting_diagnose_item_ids"] = d.WaitingDiagnoseItemIds
	d.fieldMap["removed"] = d.Removed
}

func (d diagnoseTask) clone(db *gorm.DB) diagnoseTask {
	d.diagnoseTaskDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseTask) replaceDB(db *gorm.DB) diagnoseTask {
	d.diagnoseTaskDo.ReplaceDB(db)
	return d
}

type diagnoseTaskDo struct{ gen.DO }

func (d diagnoseTaskDo) Debug() *diagnoseTaskDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseTaskDo) WithContext(ctx context.Context) *diagnoseTaskDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseTaskDo) ReadDB() *diagnoseTaskDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseTaskDo) WriteDB() *diagnoseTaskDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseTaskDo) Session(config *gorm.Session) *diagnoseTaskDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseTaskDo) Clauses(conds ...clause.Expression) *diagnoseTaskDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseTaskDo) Returning(value interface{}, columns ...string) *diagnoseTaskDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseTaskDo) Not(conds ...gen.Condition) *diagnoseTaskDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseTaskDo) Or(conds ...gen.Condition) *diagnoseTaskDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseTaskDo) Select(conds ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseTaskDo) Where(conds ...gen.Condition) *diagnoseTaskDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseTaskDo) Order(conds ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseTaskDo) Distinct(cols ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseTaskDo) Omit(cols ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseTaskDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseTaskDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseTaskDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseTaskDo) Group(cols ...field.Expr) *diagnoseTaskDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseTaskDo) Having(conds ...gen.Condition) *diagnoseTaskDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseTaskDo) Limit(limit int) *diagnoseTaskDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseTaskDo) Offset(offset int) *diagnoseTaskDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseTaskDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseTaskDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseTaskDo) Unscoped() *diagnoseTaskDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseTaskDo) Create(values ...*model.DiagnoseTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseTaskDo) CreateInBatches(values []*model.DiagnoseTask, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseTaskDo) Save(values ...*model.DiagnoseTask) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseTaskDo) First() (*model.DiagnoseTask, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTask), nil
	}
}

func (d diagnoseTaskDo) Take() (*model.DiagnoseTask, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTask), nil
	}
}

func (d diagnoseTaskDo) Last() (*model.DiagnoseTask, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTask), nil
	}
}

func (d diagnoseTaskDo) Find() ([]*model.DiagnoseTask, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseTask), err
}

func (d diagnoseTaskDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseTask, err error) {
	buf := make([]*model.DiagnoseTask, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseTaskDo) FindInBatches(result *[]*model.DiagnoseTask, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseTaskDo) Attrs(attrs ...field.AssignExpr) *diagnoseTaskDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseTaskDo) Assign(attrs ...field.AssignExpr) *diagnoseTaskDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseTaskDo) Joins(fields ...field.RelationField) *diagnoseTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseTaskDo) Preload(fields ...field.RelationField) *diagnoseTaskDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseTaskDo) FirstOrInit() (*model.DiagnoseTask, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTask), nil
	}
}

func (d diagnoseTaskDo) FirstOrCreate() (*model.DiagnoseTask, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTask), nil
	}
}

func (d diagnoseTaskDo) FindByPage(offset int, limit int) (result []*model.DiagnoseTask, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseTaskDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseTaskDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseTaskDo) Delete(models ...*model.DiagnoseTask) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseTaskDo) withDO(do gen.Dao) *diagnoseTaskDo {
	d.DO = *do.(*gen.DO)
	return d
}
