// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseTemplate(db *gorm.DB, opts ...gen.DOOption) diagnoseTemplate {
	_diagnoseTemplate := diagnoseTemplate{}

	_diagnoseTemplate.diagnoseTemplateDo.UseDB(db, opts...)
	_diagnoseTemplate.diagnoseTemplateDo.UseModel(&model.DiagnoseTemplate{})

	tableName := _diagnoseTemplate.diagnoseTemplateDo.TableName()
	_diagnoseTemplate.ALL = field.NewAsterisk(tableName)
	_diagnoseTemplate.DiagnoseTemplateID = field.NewInt64(tableName, "diagnose_template_id")
	_diagnoseTemplate.DiagnoseTemplateName = field.NewString(tableName, "diagnose_template_name")
	_diagnoseTemplate.CreateTime = field.NewTime(tableName, "create_time")
	_diagnoseTemplate.CreatorID = field.NewString(tableName, "creator_id")
	_diagnoseTemplate.CreatorName = field.NewString(tableName, "creator_name")
	_diagnoseTemplate.UpdateTime = field.NewTime(tableName, "update_time")
	_diagnoseTemplate.UpdaterID = field.NewString(tableName, "updater_id")
	_diagnoseTemplate.UpdaterName = field.NewString(tableName, "updater_name")
	_diagnoseTemplate.DeleteAt = field.NewTime(tableName, "delete_at")
	_diagnoseTemplate.DeleterID = field.NewString(tableName, "deleter_id")
	_diagnoseTemplate.DeleterName = field.NewString(tableName, "deleter_name")
	_diagnoseTemplate.TemplateDescription = field.NewString(tableName, "template_description")
	_diagnoseTemplate.Suggestion = field.NewString(tableName, "suggestion")
	_diagnoseTemplate.StateMachineInfo = field.NewString(tableName, "state_machine_info")
	_diagnoseTemplate.Inputs = field.NewString(tableName, "inputs")
	_diagnoseTemplate.Status = field.NewString(tableName, "status")
	_diagnoseTemplate.ResponsiblePerson = field.NewString(tableName, "responsible_person")
	_diagnoseTemplate.Product = field.NewString(tableName, "product")
	_diagnoseTemplate.Subproduct = field.NewString(tableName, "subproduct")
	_diagnoseTemplate.ResourceType = field.NewString(tableName, "resource_type")
	_diagnoseTemplate.ShowLevel = field.NewInt32(tableName, "show_level")
	_diagnoseTemplate.ProductCategoryID = field.NewInt32(tableName, "product_category_id")

	_diagnoseTemplate.fillFieldMap()

	return _diagnoseTemplate
}

// diagnoseTemplate 场景模版信息表
type diagnoseTemplate struct {
	diagnoseTemplateDo diagnoseTemplateDo

	ALL                  field.Asterisk
	DiagnoseTemplateID   field.Int64  // 诊断场景Id
	DiagnoseTemplateName field.String // 诊断场景名称
	CreateTime           field.Time   // 诊断场景创建时间
	CreatorID            field.String // 创建人账号Id
	CreatorName          field.String // 创建人账号名
	UpdateTime           field.Time   // 诊断场景更新时间
	UpdaterID            field.String // 更新人账号Id
	UpdaterName          field.String // 更新人账号名
	DeleteAt             field.Time   // 诊断场景删除时间
	DeleterID            field.String // 删除人账号Id
	DeleterName          field.String // 删除人账号名
	TemplateDescription  field.String // 场景描述
	Suggestion           field.String // 场景处理建议
	StateMachineInfo     field.String // 状态机信息
	Inputs               field.String // 执行该诊断场景的输入
	Status               field.String // 诊断模版的状态
	ResponsiblePerson    field.String // 诊断模版的负责人
	Product              field.String // 产品类型
	Subproduct           field.String // 子产品类型
	ResourceType         field.String // 资源类型
	ShowLevel            field.Int32  // 展示优先级
	ProductCategoryID    field.Int32  // 产品分类的ID

	fieldMap map[string]field.Expr
}

func (d diagnoseTemplate) Table(newTableName string) *diagnoseTemplate {
	d.diagnoseTemplateDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseTemplate) As(alias string) *diagnoseTemplate {
	d.diagnoseTemplateDo.DO = *(d.diagnoseTemplateDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseTemplate) updateTableName(table string) *diagnoseTemplate {
	d.ALL = field.NewAsterisk(table)
	d.DiagnoseTemplateID = field.NewInt64(table, "diagnose_template_id")
	d.DiagnoseTemplateName = field.NewString(table, "diagnose_template_name")
	d.CreateTime = field.NewTime(table, "create_time")
	d.CreatorID = field.NewString(table, "creator_id")
	d.CreatorName = field.NewString(table, "creator_name")
	d.UpdateTime = field.NewTime(table, "update_time")
	d.UpdaterID = field.NewString(table, "updater_id")
	d.UpdaterName = field.NewString(table, "updater_name")
	d.DeleteAt = field.NewTime(table, "delete_at")
	d.DeleterID = field.NewString(table, "deleter_id")
	d.DeleterName = field.NewString(table, "deleter_name")
	d.TemplateDescription = field.NewString(table, "template_description")
	d.Suggestion = field.NewString(table, "suggestion")
	d.StateMachineInfo = field.NewString(table, "state_machine_info")
	d.Inputs = field.NewString(table, "inputs")
	d.Status = field.NewString(table, "status")
	d.ResponsiblePerson = field.NewString(table, "responsible_person")
	d.Product = field.NewString(table, "product")
	d.Subproduct = field.NewString(table, "subproduct")
	d.ResourceType = field.NewString(table, "resource_type")
	d.ShowLevel = field.NewInt32(table, "show_level")
	d.ProductCategoryID = field.NewInt32(table, "product_category_id")

	d.fillFieldMap()

	return d
}

func (d *diagnoseTemplate) WithContext(ctx context.Context) *diagnoseTemplateDo {
	return d.diagnoseTemplateDo.WithContext(ctx)
}

func (d diagnoseTemplate) TableName() string { return d.diagnoseTemplateDo.TableName() }

func (d diagnoseTemplate) Alias() string { return d.diagnoseTemplateDo.Alias() }

func (d diagnoseTemplate) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseTemplateDo.Columns(cols...)
}

func (d *diagnoseTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseTemplate) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 22)
	d.fieldMap["diagnose_template_id"] = d.DiagnoseTemplateID
	d.fieldMap["diagnose_template_name"] = d.DiagnoseTemplateName
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["creator_id"] = d.CreatorID
	d.fieldMap["creator_name"] = d.CreatorName
	d.fieldMap["update_time"] = d.UpdateTime
	d.fieldMap["updater_id"] = d.UpdaterID
	d.fieldMap["updater_name"] = d.UpdaterName
	d.fieldMap["delete_at"] = d.DeleteAt
	d.fieldMap["deleter_id"] = d.DeleterID
	d.fieldMap["deleter_name"] = d.DeleterName
	d.fieldMap["template_description"] = d.TemplateDescription
	d.fieldMap["suggestion"] = d.Suggestion
	d.fieldMap["state_machine_info"] = d.StateMachineInfo
	d.fieldMap["inputs"] = d.Inputs
	d.fieldMap["status"] = d.Status
	d.fieldMap["responsible_person"] = d.ResponsiblePerson
	d.fieldMap["product"] = d.Product
	d.fieldMap["subproduct"] = d.Subproduct
	d.fieldMap["resource_type"] = d.ResourceType
	d.fieldMap["show_level"] = d.ShowLevel
	d.fieldMap["product_category_id"] = d.ProductCategoryID
}

func (d diagnoseTemplate) clone(db *gorm.DB) diagnoseTemplate {
	d.diagnoseTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseTemplate) replaceDB(db *gorm.DB) diagnoseTemplate {
	d.diagnoseTemplateDo.ReplaceDB(db)
	return d
}

type diagnoseTemplateDo struct{ gen.DO }

func (d diagnoseTemplateDo) Debug() *diagnoseTemplateDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseTemplateDo) WithContext(ctx context.Context) *diagnoseTemplateDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseTemplateDo) ReadDB() *diagnoseTemplateDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseTemplateDo) WriteDB() *diagnoseTemplateDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseTemplateDo) Session(config *gorm.Session) *diagnoseTemplateDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseTemplateDo) Clauses(conds ...clause.Expression) *diagnoseTemplateDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseTemplateDo) Returning(value interface{}, columns ...string) *diagnoseTemplateDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseTemplateDo) Not(conds ...gen.Condition) *diagnoseTemplateDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseTemplateDo) Or(conds ...gen.Condition) *diagnoseTemplateDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseTemplateDo) Select(conds ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseTemplateDo) Where(conds ...gen.Condition) *diagnoseTemplateDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseTemplateDo) Order(conds ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseTemplateDo) Distinct(cols ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseTemplateDo) Omit(cols ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseTemplateDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseTemplateDo) Group(cols ...field.Expr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseTemplateDo) Having(conds ...gen.Condition) *diagnoseTemplateDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseTemplateDo) Limit(limit int) *diagnoseTemplateDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseTemplateDo) Offset(offset int) *diagnoseTemplateDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseTemplateDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseTemplateDo) Unscoped() *diagnoseTemplateDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseTemplateDo) Create(values ...*model.DiagnoseTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseTemplateDo) CreateInBatches(values []*model.DiagnoseTemplate, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseTemplateDo) Save(values ...*model.DiagnoseTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseTemplateDo) First() (*model.DiagnoseTemplate, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTemplate), nil
	}
}

func (d diagnoseTemplateDo) Take() (*model.DiagnoseTemplate, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTemplate), nil
	}
}

func (d diagnoseTemplateDo) Last() (*model.DiagnoseTemplate, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTemplate), nil
	}
}

func (d diagnoseTemplateDo) Find() ([]*model.DiagnoseTemplate, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseTemplate), err
}

func (d diagnoseTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseTemplate, err error) {
	buf := make([]*model.DiagnoseTemplate, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseTemplateDo) FindInBatches(result *[]*model.DiagnoseTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseTemplateDo) Attrs(attrs ...field.AssignExpr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseTemplateDo) Assign(attrs ...field.AssignExpr) *diagnoseTemplateDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseTemplateDo) Joins(fields ...field.RelationField) *diagnoseTemplateDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseTemplateDo) Preload(fields ...field.RelationField) *diagnoseTemplateDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseTemplateDo) FirstOrInit() (*model.DiagnoseTemplate, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTemplate), nil
	}
}

func (d diagnoseTemplateDo) FirstOrCreate() (*model.DiagnoseTemplate, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTemplate), nil
	}
}

func (d diagnoseTemplateDo) FindByPage(offset int, limit int) (result []*model.DiagnoseTemplate, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseTemplateDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseTemplateDo) Delete(models ...*model.DiagnoseTemplate) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseTemplateDo) withDO(do gen.Dao) *diagnoseTemplateDo {
	d.DO = *do.(*gen.DO)
	return d
}
