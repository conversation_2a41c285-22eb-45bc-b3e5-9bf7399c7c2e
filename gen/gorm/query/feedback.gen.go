// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newFeedback(db *gorm.DB, opts ...gen.DOOption) feedback {
	_feedback := feedback{}

	_feedback.feedbackDo.UseDB(db, opts...)
	_feedback.feedbackDo.UseModel(&model.Feedback{})

	tableName := _feedback.feedbackDo.TableName()
	_feedback.ALL = field.NewAsterisk(tableName)
	_feedback.ID = field.NewInt64(tableName, "id")
	_feedback.DiagnoseTaskID = field.NewInt64(tableName, "diagnose_task_id")
	_feedback.Resolved = field.NewBool(tableName, "resolved")
	_feedback.Description = field.NewString(tableName, "description")
	_feedback.ProblemItems = field.NewString(tableName, "problem_items")
	_feedback.FeedbackUserID = field.NewString(tableName, "feedback_user_id")
	_feedback.DiagnoseRunUserID = field.NewString(tableName, "diagnose_run_user_id")
	_feedback.FeedbackTime = field.NewTime(tableName, "feedback_time")
	_feedback.DiagnoseTaskRunID = field.NewInt64(tableName, "diagnose_task_run_id")

	_feedback.fillFieldMap()

	return _feedback
}

// feedback 问题反馈表
type feedback struct {
	feedbackDo feedbackDo

	ALL               field.Asterisk
	ID                field.Int64  // 自增ID
	DiagnoseTaskID    field.Int64  // 关联任务ID
	Resolved          field.Bool   // 问题是否已解决
	Description       field.String // 问题描述
	ProblemItems      field.String // 问题项
	FeedbackUserID    field.String // 反馈人邮箱
	DiagnoseRunUserID field.String // 任务执行人邮箱
	FeedbackTime      field.Time   // 反馈时间
	DiagnoseTaskRunID field.Int64  // 关联任务运行ID

	fieldMap map[string]field.Expr
}

func (f feedback) Table(newTableName string) *feedback {
	f.feedbackDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f feedback) As(alias string) *feedback {
	f.feedbackDo.DO = *(f.feedbackDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *feedback) updateTableName(table string) *feedback {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.DiagnoseTaskID = field.NewInt64(table, "diagnose_task_id")
	f.Resolved = field.NewBool(table, "resolved")
	f.Description = field.NewString(table, "description")
	f.ProblemItems = field.NewString(table, "problem_items")
	f.FeedbackUserID = field.NewString(table, "feedback_user_id")
	f.DiagnoseRunUserID = field.NewString(table, "diagnose_run_user_id")
	f.FeedbackTime = field.NewTime(table, "feedback_time")
	f.DiagnoseTaskRunID = field.NewInt64(table, "diagnose_task_run_id")

	f.fillFieldMap()

	return f
}

func (f *feedback) WithContext(ctx context.Context) *feedbackDo { return f.feedbackDo.WithContext(ctx) }

func (f feedback) TableName() string { return f.feedbackDo.TableName() }

func (f feedback) Alias() string { return f.feedbackDo.Alias() }

func (f feedback) Columns(cols ...field.Expr) gen.Columns { return f.feedbackDo.Columns(cols...) }

func (f *feedback) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *feedback) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 9)
	f.fieldMap["id"] = f.ID
	f.fieldMap["diagnose_task_id"] = f.DiagnoseTaskID
	f.fieldMap["resolved"] = f.Resolved
	f.fieldMap["description"] = f.Description
	f.fieldMap["problem_items"] = f.ProblemItems
	f.fieldMap["feedback_user_id"] = f.FeedbackUserID
	f.fieldMap["diagnose_run_user_id"] = f.DiagnoseRunUserID
	f.fieldMap["feedback_time"] = f.FeedbackTime
	f.fieldMap["diagnose_task_run_id"] = f.DiagnoseTaskRunID
}

func (f feedback) clone(db *gorm.DB) feedback {
	f.feedbackDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f feedback) replaceDB(db *gorm.DB) feedback {
	f.feedbackDo.ReplaceDB(db)
	return f
}

type feedbackDo struct{ gen.DO }

func (f feedbackDo) Debug() *feedbackDo {
	return f.withDO(f.DO.Debug())
}

func (f feedbackDo) WithContext(ctx context.Context) *feedbackDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f feedbackDo) ReadDB() *feedbackDo {
	return f.Clauses(dbresolver.Read)
}

func (f feedbackDo) WriteDB() *feedbackDo {
	return f.Clauses(dbresolver.Write)
}

func (f feedbackDo) Session(config *gorm.Session) *feedbackDo {
	return f.withDO(f.DO.Session(config))
}

func (f feedbackDo) Clauses(conds ...clause.Expression) *feedbackDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f feedbackDo) Returning(value interface{}, columns ...string) *feedbackDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f feedbackDo) Not(conds ...gen.Condition) *feedbackDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f feedbackDo) Or(conds ...gen.Condition) *feedbackDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f feedbackDo) Select(conds ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f feedbackDo) Where(conds ...gen.Condition) *feedbackDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f feedbackDo) Order(conds ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f feedbackDo) Distinct(cols ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f feedbackDo) Omit(cols ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f feedbackDo) Join(table schema.Tabler, on ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f feedbackDo) LeftJoin(table schema.Tabler, on ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f feedbackDo) RightJoin(table schema.Tabler, on ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f feedbackDo) Group(cols ...field.Expr) *feedbackDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f feedbackDo) Having(conds ...gen.Condition) *feedbackDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f feedbackDo) Limit(limit int) *feedbackDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f feedbackDo) Offset(offset int) *feedbackDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f feedbackDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *feedbackDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f feedbackDo) Unscoped() *feedbackDo {
	return f.withDO(f.DO.Unscoped())
}

func (f feedbackDo) Create(values ...*model.Feedback) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f feedbackDo) CreateInBatches(values []*model.Feedback, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f feedbackDo) Save(values ...*model.Feedback) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f feedbackDo) First() (*model.Feedback, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Feedback), nil
	}
}

func (f feedbackDo) Take() (*model.Feedback, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Feedback), nil
	}
}

func (f feedbackDo) Last() (*model.Feedback, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Feedback), nil
	}
}

func (f feedbackDo) Find() ([]*model.Feedback, error) {
	result, err := f.DO.Find()
	return result.([]*model.Feedback), err
}

func (f feedbackDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Feedback, err error) {
	buf := make([]*model.Feedback, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f feedbackDo) FindInBatches(result *[]*model.Feedback, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f feedbackDo) Attrs(attrs ...field.AssignExpr) *feedbackDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f feedbackDo) Assign(attrs ...field.AssignExpr) *feedbackDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f feedbackDo) Joins(fields ...field.RelationField) *feedbackDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f feedbackDo) Preload(fields ...field.RelationField) *feedbackDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f feedbackDo) FirstOrInit() (*model.Feedback, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Feedback), nil
	}
}

func (f feedbackDo) FirstOrCreate() (*model.Feedback, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Feedback), nil
	}
}

func (f feedbackDo) FindByPage(offset int, limit int) (result []*model.Feedback, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f feedbackDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f feedbackDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f feedbackDo) Delete(models ...*model.Feedback) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *feedbackDo) withDO(do gen.Dao) *feedbackDo {
	f.DO = *do.(*gen.DO)
	return f
}
