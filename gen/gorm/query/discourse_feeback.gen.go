// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiscourseFeeback(db *gorm.DB, opts ...gen.DOOption) discourseFeeback {
	_discourseFeeback := discourseFeeback{}

	_discourseFeeback.discourseFeebackDo.UseDB(db, opts...)
	_discourseFeeback.discourseFeebackDo.UseModel(&model.DiscourseFeeback{})

	tableName := _discourseFeeback.discourseFeebackDo.TableName()
	_discourseFeeback.ALL = field.NewAsterisk(tableName)
	_discourseFeeback.ID = field.NewInt64(tableName, "id")
	_discourseFeeback.SessionID = field.NewString(tableName, "session_id")
	_discourseFeeback.MessageID = field.NewString(tableName, "message_id")
	_discourseFeeback.Type = field.NewInt32(tableName, "type")
	_discourseFeeback.Upvote = field.NewBool(tableName, "upvote")
	_discourseFeeback.CreateTime = field.NewTime(tableName, "create_time")

	_discourseFeeback.fillFieldMap()

	return _discourseFeeback
}

// discourseFeeback 智能问诊反馈表
type discourseFeeback struct {
	discourseFeebackDo discourseFeebackDo

	ALL        field.Asterisk
	ID         field.Int64  // 消息唯一标识（主键）
	SessionID  field.String // 会话ID（关联会话表）
	MessageID  field.String // 消息内容ID（唯一标识消息内容）
	Type       field.Int32  // 消息类型（如文本、图片、视频等，可通过枚举值定义）
	Upvote     field.Bool   // 是否点赞（0=否，1=是）
	CreateTime field.Time   // 创建时间（时间戳，单位：毫秒）

	fieldMap map[string]field.Expr
}

func (d discourseFeeback) Table(newTableName string) *discourseFeeback {
	d.discourseFeebackDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d discourseFeeback) As(alias string) *discourseFeeback {
	d.discourseFeebackDo.DO = *(d.discourseFeebackDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *discourseFeeback) updateTableName(table string) *discourseFeeback {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.SessionID = field.NewString(table, "session_id")
	d.MessageID = field.NewString(table, "message_id")
	d.Type = field.NewInt32(table, "type")
	d.Upvote = field.NewBool(table, "upvote")
	d.CreateTime = field.NewTime(table, "create_time")

	d.fillFieldMap()

	return d
}

func (d *discourseFeeback) WithContext(ctx context.Context) *discourseFeebackDo {
	return d.discourseFeebackDo.WithContext(ctx)
}

func (d discourseFeeback) TableName() string { return d.discourseFeebackDo.TableName() }

func (d discourseFeeback) Alias() string { return d.discourseFeebackDo.Alias() }

func (d discourseFeeback) Columns(cols ...field.Expr) gen.Columns {
	return d.discourseFeebackDo.Columns(cols...)
}

func (d *discourseFeeback) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *discourseFeeback) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 6)
	d.fieldMap["id"] = d.ID
	d.fieldMap["session_id"] = d.SessionID
	d.fieldMap["message_id"] = d.MessageID
	d.fieldMap["type"] = d.Type
	d.fieldMap["upvote"] = d.Upvote
	d.fieldMap["create_time"] = d.CreateTime
}

func (d discourseFeeback) clone(db *gorm.DB) discourseFeeback {
	d.discourseFeebackDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d discourseFeeback) replaceDB(db *gorm.DB) discourseFeeback {
	d.discourseFeebackDo.ReplaceDB(db)
	return d
}

type discourseFeebackDo struct{ gen.DO }

func (d discourseFeebackDo) Debug() *discourseFeebackDo {
	return d.withDO(d.DO.Debug())
}

func (d discourseFeebackDo) WithContext(ctx context.Context) *discourseFeebackDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d discourseFeebackDo) ReadDB() *discourseFeebackDo {
	return d.Clauses(dbresolver.Read)
}

func (d discourseFeebackDo) WriteDB() *discourseFeebackDo {
	return d.Clauses(dbresolver.Write)
}

func (d discourseFeebackDo) Session(config *gorm.Session) *discourseFeebackDo {
	return d.withDO(d.DO.Session(config))
}

func (d discourseFeebackDo) Clauses(conds ...clause.Expression) *discourseFeebackDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d discourseFeebackDo) Returning(value interface{}, columns ...string) *discourseFeebackDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d discourseFeebackDo) Not(conds ...gen.Condition) *discourseFeebackDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d discourseFeebackDo) Or(conds ...gen.Condition) *discourseFeebackDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d discourseFeebackDo) Select(conds ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d discourseFeebackDo) Where(conds ...gen.Condition) *discourseFeebackDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d discourseFeebackDo) Order(conds ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d discourseFeebackDo) Distinct(cols ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d discourseFeebackDo) Omit(cols ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d discourseFeebackDo) Join(table schema.Tabler, on ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d discourseFeebackDo) LeftJoin(table schema.Tabler, on ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d discourseFeebackDo) RightJoin(table schema.Tabler, on ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d discourseFeebackDo) Group(cols ...field.Expr) *discourseFeebackDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d discourseFeebackDo) Having(conds ...gen.Condition) *discourseFeebackDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d discourseFeebackDo) Limit(limit int) *discourseFeebackDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d discourseFeebackDo) Offset(offset int) *discourseFeebackDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d discourseFeebackDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *discourseFeebackDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d discourseFeebackDo) Unscoped() *discourseFeebackDo {
	return d.withDO(d.DO.Unscoped())
}

func (d discourseFeebackDo) Create(values ...*model.DiscourseFeeback) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d discourseFeebackDo) CreateInBatches(values []*model.DiscourseFeeback, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d discourseFeebackDo) Save(values ...*model.DiscourseFeeback) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d discourseFeebackDo) First() (*model.DiscourseFeeback, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseFeeback), nil
	}
}

func (d discourseFeebackDo) Take() (*model.DiscourseFeeback, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseFeeback), nil
	}
}

func (d discourseFeebackDo) Last() (*model.DiscourseFeeback, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseFeeback), nil
	}
}

func (d discourseFeebackDo) Find() ([]*model.DiscourseFeeback, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiscourseFeeback), err
}

func (d discourseFeebackDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiscourseFeeback, err error) {
	buf := make([]*model.DiscourseFeeback, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d discourseFeebackDo) FindInBatches(result *[]*model.DiscourseFeeback, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d discourseFeebackDo) Attrs(attrs ...field.AssignExpr) *discourseFeebackDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d discourseFeebackDo) Assign(attrs ...field.AssignExpr) *discourseFeebackDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d discourseFeebackDo) Joins(fields ...field.RelationField) *discourseFeebackDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d discourseFeebackDo) Preload(fields ...field.RelationField) *discourseFeebackDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d discourseFeebackDo) FirstOrInit() (*model.DiscourseFeeback, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseFeeback), nil
	}
}

func (d discourseFeebackDo) FirstOrCreate() (*model.DiscourseFeeback, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseFeeback), nil
	}
}

func (d discourseFeebackDo) FindByPage(offset int, limit int) (result []*model.DiscourseFeeback, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d discourseFeebackDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d discourseFeebackDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d discourseFeebackDo) Delete(models ...*model.DiscourseFeeback) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *discourseFeebackDo) withDO(do gen.Dao) *discourseFeebackDo {
	d.DO = *do.(*gen.DO)
	return d
}
