// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newItemTemplate(db *gorm.DB, opts ...gen.DOOption) itemTemplate {
	_itemTemplate := itemTemplate{}

	_itemTemplate.itemTemplateDo.UseDB(db, opts...)
	_itemTemplate.itemTemplateDo.UseModel(&model.ItemTemplate{})

	tableName := _itemTemplate.itemTemplateDo.TableName()
	_itemTemplate.ALL = field.NewAsterisk(tableName)
	_itemTemplate.DiagnoseItemID = field.NewInt64(tableName, "diagnose_item_id")
	_itemTemplate.DiagnoseTemplateID = field.NewInt64(tableName, "diagnose_template_id")
	_itemTemplate.CreateTime = field.NewTime(tableName, "create_time")
	_itemTemplate.CreatorID = field.NewString(tableName, "creator_id")
	_itemTemplate.CreatorName = field.NewString(tableName, "creator_name")
	_itemTemplate.DeleteAt = field.NewTime(tableName, "delete_at")
	_itemTemplate.DeleterID = field.NewString(tableName, "deleter_id")
	_itemTemplate.DeleterName = field.NewString(tableName, "deleter_name")
	_itemTemplate.Activity = field.NewString(tableName, "activity")

	_itemTemplate.fillFieldMap()

	return _itemTemplate
}

// itemTemplate 诊断项—场景模版-关联表
type itemTemplate struct {
	itemTemplateDo itemTemplateDo

	ALL                field.Asterisk
	DiagnoseItemID     field.Int64  // 诊断项Id
	DiagnoseTemplateID field.Int64  // 诊断模版Id
	CreateTime         field.Time   // 关联创建时间
	CreatorID          field.String // 创建人账号Id
	CreatorName        field.String // 创建人账号名
	DeleteAt           field.Time   // 诊断场景删除时间
	DeleterID          field.String // 删除人账号Id
	DeleterName        field.String // 删除人账号名
	Activity           field.String // 由该activity执行诊断项

	fieldMap map[string]field.Expr
}

func (i itemTemplate) Table(newTableName string) *itemTemplate {
	i.itemTemplateDo.UseTable(newTableName)
	return i.updateTableName(newTableName)
}

func (i itemTemplate) As(alias string) *itemTemplate {
	i.itemTemplateDo.DO = *(i.itemTemplateDo.As(alias).(*gen.DO))
	return i.updateTableName(alias)
}

func (i *itemTemplate) updateTableName(table string) *itemTemplate {
	i.ALL = field.NewAsterisk(table)
	i.DiagnoseItemID = field.NewInt64(table, "diagnose_item_id")
	i.DiagnoseTemplateID = field.NewInt64(table, "diagnose_template_id")
	i.CreateTime = field.NewTime(table, "create_time")
	i.CreatorID = field.NewString(table, "creator_id")
	i.CreatorName = field.NewString(table, "creator_name")
	i.DeleteAt = field.NewTime(table, "delete_at")
	i.DeleterID = field.NewString(table, "deleter_id")
	i.DeleterName = field.NewString(table, "deleter_name")
	i.Activity = field.NewString(table, "activity")

	i.fillFieldMap()

	return i
}

func (i *itemTemplate) WithContext(ctx context.Context) *itemTemplateDo {
	return i.itemTemplateDo.WithContext(ctx)
}

func (i itemTemplate) TableName() string { return i.itemTemplateDo.TableName() }

func (i itemTemplate) Alias() string { return i.itemTemplateDo.Alias() }

func (i itemTemplate) Columns(cols ...field.Expr) gen.Columns {
	return i.itemTemplateDo.Columns(cols...)
}

func (i *itemTemplate) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := i.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (i *itemTemplate) fillFieldMap() {
	i.fieldMap = make(map[string]field.Expr, 9)
	i.fieldMap["diagnose_item_id"] = i.DiagnoseItemID
	i.fieldMap["diagnose_template_id"] = i.DiagnoseTemplateID
	i.fieldMap["create_time"] = i.CreateTime
	i.fieldMap["creator_id"] = i.CreatorID
	i.fieldMap["creator_name"] = i.CreatorName
	i.fieldMap["delete_at"] = i.DeleteAt
	i.fieldMap["deleter_id"] = i.DeleterID
	i.fieldMap["deleter_name"] = i.DeleterName
	i.fieldMap["activity"] = i.Activity
}

func (i itemTemplate) clone(db *gorm.DB) itemTemplate {
	i.itemTemplateDo.ReplaceConnPool(db.Statement.ConnPool)
	return i
}

func (i itemTemplate) replaceDB(db *gorm.DB) itemTemplate {
	i.itemTemplateDo.ReplaceDB(db)
	return i
}

type itemTemplateDo struct{ gen.DO }

func (i itemTemplateDo) Debug() *itemTemplateDo {
	return i.withDO(i.DO.Debug())
}

func (i itemTemplateDo) WithContext(ctx context.Context) *itemTemplateDo {
	return i.withDO(i.DO.WithContext(ctx))
}

func (i itemTemplateDo) ReadDB() *itemTemplateDo {
	return i.Clauses(dbresolver.Read)
}

func (i itemTemplateDo) WriteDB() *itemTemplateDo {
	return i.Clauses(dbresolver.Write)
}

func (i itemTemplateDo) Session(config *gorm.Session) *itemTemplateDo {
	return i.withDO(i.DO.Session(config))
}

func (i itemTemplateDo) Clauses(conds ...clause.Expression) *itemTemplateDo {
	return i.withDO(i.DO.Clauses(conds...))
}

func (i itemTemplateDo) Returning(value interface{}, columns ...string) *itemTemplateDo {
	return i.withDO(i.DO.Returning(value, columns...))
}

func (i itemTemplateDo) Not(conds ...gen.Condition) *itemTemplateDo {
	return i.withDO(i.DO.Not(conds...))
}

func (i itemTemplateDo) Or(conds ...gen.Condition) *itemTemplateDo {
	return i.withDO(i.DO.Or(conds...))
}

func (i itemTemplateDo) Select(conds ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Select(conds...))
}

func (i itemTemplateDo) Where(conds ...gen.Condition) *itemTemplateDo {
	return i.withDO(i.DO.Where(conds...))
}

func (i itemTemplateDo) Order(conds ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Order(conds...))
}

func (i itemTemplateDo) Distinct(cols ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Distinct(cols...))
}

func (i itemTemplateDo) Omit(cols ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Omit(cols...))
}

func (i itemTemplateDo) Join(table schema.Tabler, on ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Join(table, on...))
}

func (i itemTemplateDo) LeftJoin(table schema.Tabler, on ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.LeftJoin(table, on...))
}

func (i itemTemplateDo) RightJoin(table schema.Tabler, on ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.RightJoin(table, on...))
}

func (i itemTemplateDo) Group(cols ...field.Expr) *itemTemplateDo {
	return i.withDO(i.DO.Group(cols...))
}

func (i itemTemplateDo) Having(conds ...gen.Condition) *itemTemplateDo {
	return i.withDO(i.DO.Having(conds...))
}

func (i itemTemplateDo) Limit(limit int) *itemTemplateDo {
	return i.withDO(i.DO.Limit(limit))
}

func (i itemTemplateDo) Offset(offset int) *itemTemplateDo {
	return i.withDO(i.DO.Offset(offset))
}

func (i itemTemplateDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *itemTemplateDo {
	return i.withDO(i.DO.Scopes(funcs...))
}

func (i itemTemplateDo) Unscoped() *itemTemplateDo {
	return i.withDO(i.DO.Unscoped())
}

func (i itemTemplateDo) Create(values ...*model.ItemTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Create(values)
}

func (i itemTemplateDo) CreateInBatches(values []*model.ItemTemplate, batchSize int) error {
	return i.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (i itemTemplateDo) Save(values ...*model.ItemTemplate) error {
	if len(values) == 0 {
		return nil
	}
	return i.DO.Save(values)
}

func (i itemTemplateDo) First() (*model.ItemTemplate, error) {
	if result, err := i.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemTemplate), nil
	}
}

func (i itemTemplateDo) Take() (*model.ItemTemplate, error) {
	if result, err := i.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemTemplate), nil
	}
}

func (i itemTemplateDo) Last() (*model.ItemTemplate, error) {
	if result, err := i.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemTemplate), nil
	}
}

func (i itemTemplateDo) Find() ([]*model.ItemTemplate, error) {
	result, err := i.DO.Find()
	return result.([]*model.ItemTemplate), err
}

func (i itemTemplateDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ItemTemplate, err error) {
	buf := make([]*model.ItemTemplate, 0, batchSize)
	err = i.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (i itemTemplateDo) FindInBatches(result *[]*model.ItemTemplate, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return i.DO.FindInBatches(result, batchSize, fc)
}

func (i itemTemplateDo) Attrs(attrs ...field.AssignExpr) *itemTemplateDo {
	return i.withDO(i.DO.Attrs(attrs...))
}

func (i itemTemplateDo) Assign(attrs ...field.AssignExpr) *itemTemplateDo {
	return i.withDO(i.DO.Assign(attrs...))
}

func (i itemTemplateDo) Joins(fields ...field.RelationField) *itemTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Joins(_f))
	}
	return &i
}

func (i itemTemplateDo) Preload(fields ...field.RelationField) *itemTemplateDo {
	for _, _f := range fields {
		i = *i.withDO(i.DO.Preload(_f))
	}
	return &i
}

func (i itemTemplateDo) FirstOrInit() (*model.ItemTemplate, error) {
	if result, err := i.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemTemplate), nil
	}
}

func (i itemTemplateDo) FirstOrCreate() (*model.ItemTemplate, error) {
	if result, err := i.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ItemTemplate), nil
	}
}

func (i itemTemplateDo) FindByPage(offset int, limit int) (result []*model.ItemTemplate, count int64, err error) {
	result, err = i.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = i.Offset(-1).Limit(-1).Count()
	return
}

func (i itemTemplateDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = i.Count()
	if err != nil {
		return
	}

	err = i.Offset(offset).Limit(limit).Scan(result)
	return
}

func (i itemTemplateDo) Scan(result interface{}) (err error) {
	return i.DO.Scan(result)
}

func (i itemTemplateDo) Delete(models ...*model.ItemTemplate) (result gen.ResultInfo, err error) {
	return i.DO.Delete(models)
}

func (i *itemTemplateDo) withDO(do gen.Dao) *itemTemplateDo {
	i.DO = *do.(*gen.DO)
	return i
}
