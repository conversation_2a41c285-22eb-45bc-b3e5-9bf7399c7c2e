// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newCustomer(db *gorm.DB, opts ...gen.DOOption) customer {
	_customer := customer{}

	_customer.customerDo.UseDB(db, opts...)
	_customer.customerDo.UseModel(&model.Customer{})

	tableName := _customer.customerDo.TableName()
	_customer.ALL = field.NewAsterisk(tableName)
	_customer.ID = field.NewInt64(tableName, "id")
	_customer.SfID = field.NewString(tableName, "sf_id")
	_customer.ParentID = field.NewString(tableName, "parent_id")
	_customer.CustomerNumber = field.NewString(tableName, "customer_number")
	_customer.MainCustomerNumber = field.NewString(tableName, "main_customer_number")
	_customer.CustomerName = field.NewString(tableName, "customer_name")
	_customer.CustomerShortName = field.NewString(tableName, "customer_short_name")
	_customer.Industry = field.NewString(tableName, "industry")
	_customer.SubIndustry = field.NewString(tableName, "sub_industry")
	_customer.OwnerID = field.NewString(tableName, "owner_id")
	_customer.OwnerEmail = field.NewString(tableName, "owner_email")
	_customer.Country = field.NewString(tableName, "country")
	_customer.Region = field.NewString(tableName, "region")
	_customer.Province = field.NewString(tableName, "province")
	_customer.City = field.NewString(tableName, "city")
	_customer.CustomerTier = field.NewString(tableName, "customer_tier")
	_customer.CustomerPLevel = field.NewString(tableName, "customer_p_level")
	_customer.Status = field.NewString(tableName, "status")
	_customer.DeletedTime = field.NewField(tableName, "deleted_time")
	_customer.CreatedTime = field.NewTime(tableName, "created_time")
	_customer.UpdatedTime = field.NewTime(tableName, "updated_time")
	_customer.Display = field.NewInt32(tableName, "display")

	_customer.fillFieldMap()

	return _customer
}

type customer struct {
	customerDo customerDo

	ALL                field.Asterisk
	ID                 field.Int64  // 自增ID
	SfID               field.String // sf_id
	ParentID           field.String // 主客户sf_id
	CustomerNumber     field.String // 客户编号
	MainCustomerNumber field.String // 主客户编号
	CustomerName       field.String // 客户名
	CustomerShortName  field.String // 客户简称
	Industry           field.String // 行业
	SubIndustry        field.String // 二级行业
	OwnerID            field.String // 客户归属人sf_id
	OwnerEmail         field.String // 客户owner邮箱
	Country            field.String // 国家
	Region             field.String // 区域
	Province           field.String // 省份
	City               field.String // 城市
	CustomerTier       field.String // 客户等级
	CustomerPLevel     field.String // 客户P级
	Status             field.String // 客户状态
	DeletedTime        field.Field  // 删除时间
	CreatedTime        field.Time   // 创建时间
	UpdatedTime        field.Time   // 更新时间
	Display            field.Int32  // 展示配置

	fieldMap map[string]field.Expr
}

func (c customer) Table(newTableName string) *customer {
	c.customerDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c customer) As(alias string) *customer {
	c.customerDo.DO = *(c.customerDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *customer) updateTableName(table string) *customer {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.SfID = field.NewString(table, "sf_id")
	c.ParentID = field.NewString(table, "parent_id")
	c.CustomerNumber = field.NewString(table, "customer_number")
	c.MainCustomerNumber = field.NewString(table, "main_customer_number")
	c.CustomerName = field.NewString(table, "customer_name")
	c.CustomerShortName = field.NewString(table, "customer_short_name")
	c.Industry = field.NewString(table, "industry")
	c.SubIndustry = field.NewString(table, "sub_industry")
	c.OwnerID = field.NewString(table, "owner_id")
	c.OwnerEmail = field.NewString(table, "owner_email")
	c.Country = field.NewString(table, "country")
	c.Region = field.NewString(table, "region")
	c.Province = field.NewString(table, "province")
	c.City = field.NewString(table, "city")
	c.CustomerTier = field.NewString(table, "customer_tier")
	c.CustomerPLevel = field.NewString(table, "customer_p_level")
	c.Status = field.NewString(table, "status")
	c.DeletedTime = field.NewField(table, "deleted_time")
	c.CreatedTime = field.NewTime(table, "created_time")
	c.UpdatedTime = field.NewTime(table, "updated_time")
	c.Display = field.NewInt32(table, "display")

	c.fillFieldMap()

	return c
}

func (c *customer) WithContext(ctx context.Context) *customerDo { return c.customerDo.WithContext(ctx) }

func (c customer) TableName() string { return c.customerDo.TableName() }

func (c customer) Alias() string { return c.customerDo.Alias() }

func (c customer) Columns(cols ...field.Expr) gen.Columns { return c.customerDo.Columns(cols...) }

func (c *customer) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *customer) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 22)
	c.fieldMap["id"] = c.ID
	c.fieldMap["sf_id"] = c.SfID
	c.fieldMap["parent_id"] = c.ParentID
	c.fieldMap["customer_number"] = c.CustomerNumber
	c.fieldMap["main_customer_number"] = c.MainCustomerNumber
	c.fieldMap["customer_name"] = c.CustomerName
	c.fieldMap["customer_short_name"] = c.CustomerShortName
	c.fieldMap["industry"] = c.Industry
	c.fieldMap["sub_industry"] = c.SubIndustry
	c.fieldMap["owner_id"] = c.OwnerID
	c.fieldMap["owner_email"] = c.OwnerEmail
	c.fieldMap["country"] = c.Country
	c.fieldMap["region"] = c.Region
	c.fieldMap["province"] = c.Province
	c.fieldMap["city"] = c.City
	c.fieldMap["customer_tier"] = c.CustomerTier
	c.fieldMap["customer_p_level"] = c.CustomerPLevel
	c.fieldMap["status"] = c.Status
	c.fieldMap["deleted_time"] = c.DeletedTime
	c.fieldMap["created_time"] = c.CreatedTime
	c.fieldMap["updated_time"] = c.UpdatedTime
	c.fieldMap["display"] = c.Display
}

func (c customer) clone(db *gorm.DB) customer {
	c.customerDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c customer) replaceDB(db *gorm.DB) customer {
	c.customerDo.ReplaceDB(db)
	return c
}

type customerDo struct{ gen.DO }

func (c customerDo) Debug() *customerDo {
	return c.withDO(c.DO.Debug())
}

func (c customerDo) WithContext(ctx context.Context) *customerDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c customerDo) ReadDB() *customerDo {
	return c.Clauses(dbresolver.Read)
}

func (c customerDo) WriteDB() *customerDo {
	return c.Clauses(dbresolver.Write)
}

func (c customerDo) Session(config *gorm.Session) *customerDo {
	return c.withDO(c.DO.Session(config))
}

func (c customerDo) Clauses(conds ...clause.Expression) *customerDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c customerDo) Returning(value interface{}, columns ...string) *customerDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c customerDo) Not(conds ...gen.Condition) *customerDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c customerDo) Or(conds ...gen.Condition) *customerDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c customerDo) Select(conds ...field.Expr) *customerDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c customerDo) Where(conds ...gen.Condition) *customerDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c customerDo) Order(conds ...field.Expr) *customerDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c customerDo) Distinct(cols ...field.Expr) *customerDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c customerDo) Omit(cols ...field.Expr) *customerDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c customerDo) Join(table schema.Tabler, on ...field.Expr) *customerDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c customerDo) LeftJoin(table schema.Tabler, on ...field.Expr) *customerDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c customerDo) RightJoin(table schema.Tabler, on ...field.Expr) *customerDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c customerDo) Group(cols ...field.Expr) *customerDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c customerDo) Having(conds ...gen.Condition) *customerDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c customerDo) Limit(limit int) *customerDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c customerDo) Offset(offset int) *customerDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c customerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *customerDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c customerDo) Unscoped() *customerDo {
	return c.withDO(c.DO.Unscoped())
}

func (c customerDo) Create(values ...*model.Customer) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c customerDo) CreateInBatches(values []*model.Customer, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c customerDo) Save(values ...*model.Customer) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c customerDo) First() (*model.Customer, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Customer), nil
	}
}

func (c customerDo) Take() (*model.Customer, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Customer), nil
	}
}

func (c customerDo) Last() (*model.Customer, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Customer), nil
	}
}

func (c customerDo) Find() ([]*model.Customer, error) {
	result, err := c.DO.Find()
	return result.([]*model.Customer), err
}

func (c customerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Customer, err error) {
	buf := make([]*model.Customer, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c customerDo) FindInBatches(result *[]*model.Customer, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c customerDo) Attrs(attrs ...field.AssignExpr) *customerDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c customerDo) Assign(attrs ...field.AssignExpr) *customerDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c customerDo) Joins(fields ...field.RelationField) *customerDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c customerDo) Preload(fields ...field.RelationField) *customerDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c customerDo) FirstOrInit() (*model.Customer, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Customer), nil
	}
}

func (c customerDo) FirstOrCreate() (*model.Customer, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Customer), nil
	}
}

func (c customerDo) FindByPage(offset int, limit int) (result []*model.Customer, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c customerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c customerDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c customerDo) Delete(models ...*model.Customer) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *customerDo) withDO(do gen.Dao) *customerDo {
	c.DO = *do.(*gen.DO)
	return c
}
