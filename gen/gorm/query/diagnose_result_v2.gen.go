// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseResultV2(db *gorm.DB, opts ...gen.DOOption) diagnoseResultV2 {
	_diagnoseResultV2 := diagnoseResultV2{}

	_diagnoseResultV2.diagnoseResultV2Do.UseDB(db, opts...)
	_diagnoseResultV2.diagnoseResultV2Do.UseModel(&model.DiagnoseResultV2{})

	tableName := _diagnoseResultV2.diagnoseResultV2Do.TableName()
	_diagnoseResultV2.ALL = field.NewAsterisk(tableName)
	_diagnoseResultV2.ID = field.NewInt64(tableName, "id")
	_diagnoseResultV2.DiagnoseItemID = field.NewInt64(tableName, "diagnose_item_id")
	_diagnoseResultV2.DiagnoseTaskRunID = field.NewInt64(tableName, "diagnose_task_run_id")
	_diagnoseResultV2.DiagnoseResultLevelNum = field.NewInt32(tableName, "diagnose_result_level_num")
	_diagnoseResultV2.InstanceID = field.NewString(tableName, "instance_id")
	_diagnoseResultV2.InstanceName = field.NewString(tableName, "instance_name")
	_diagnoseResultV2.Region = field.NewString(tableName, "region")
	_diagnoseResultV2.DiagnoseMessage = field.NewString(tableName, "diagnose_message")
	_diagnoseResultV2.DiagnoseResultLevel = field.NewString(tableName, "diagnose_result_level")
	_diagnoseResultV2.DiagnoseSuggestion = field.NewString(tableName, "diagnose_suggestion")
	_diagnoseResultV2.DiagnoseOperate = field.NewString(tableName, "diagnose_operate")
	_diagnoseResultV2.Status = field.NewString(tableName, "status")
	_diagnoseResultV2.ErrorInfo = field.NewString(tableName, "error_info")
	_diagnoseResultV2.ProductCategoryID = field.NewInt64(tableName, "product_category_id")

	_diagnoseResultV2.fillFieldMap()

	return _diagnoseResultV2
}

// diagnoseResultV2 诊断结果表V2
type diagnoseResultV2 struct {
	diagnoseResultV2Do diagnoseResultV2Do

	ALL                    field.Asterisk
	ID                     field.Int64  // 自增ID
	DiagnoseItemID         field.Int64  // 诊断项ID
	DiagnoseTaskRunID      field.Int64  // 关联任务运行ID
	DiagnoseResultLevelNum field.Int32  // 结果等级num
	InstanceID             field.String // 实例ID
	InstanceName           field.String // 实例名称
	Region                 field.String // 实例区域
	DiagnoseMessage        field.String // 诊断信息
	DiagnoseResultLevel    field.String // 诊断结果
	DiagnoseSuggestion     field.String // 诊断建议
	DiagnoseOperate        field.String // 诊断操作
	Status                 field.String // 诊断状态(Succeed,Running,Failed)
	ErrorInfo              field.String // 诊断错误信息
	ProductCategoryID      field.Int64  // 产品类型ID

	fieldMap map[string]field.Expr
}

func (d diagnoseResultV2) Table(newTableName string) *diagnoseResultV2 {
	d.diagnoseResultV2Do.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseResultV2) As(alias string) *diagnoseResultV2 {
	d.diagnoseResultV2Do.DO = *(d.diagnoseResultV2Do.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseResultV2) updateTableName(table string) *diagnoseResultV2 {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.DiagnoseItemID = field.NewInt64(table, "diagnose_item_id")
	d.DiagnoseTaskRunID = field.NewInt64(table, "diagnose_task_run_id")
	d.DiagnoseResultLevelNum = field.NewInt32(table, "diagnose_result_level_num")
	d.InstanceID = field.NewString(table, "instance_id")
	d.InstanceName = field.NewString(table, "instance_name")
	d.Region = field.NewString(table, "region")
	d.DiagnoseMessage = field.NewString(table, "diagnose_message")
	d.DiagnoseResultLevel = field.NewString(table, "diagnose_result_level")
	d.DiagnoseSuggestion = field.NewString(table, "diagnose_suggestion")
	d.DiagnoseOperate = field.NewString(table, "diagnose_operate")
	d.Status = field.NewString(table, "status")
	d.ErrorInfo = field.NewString(table, "error_info")
	d.ProductCategoryID = field.NewInt64(table, "product_category_id")

	d.fillFieldMap()

	return d
}

func (d *diagnoseResultV2) WithContext(ctx context.Context) *diagnoseResultV2Do {
	return d.diagnoseResultV2Do.WithContext(ctx)
}

func (d diagnoseResultV2) TableName() string { return d.diagnoseResultV2Do.TableName() }

func (d diagnoseResultV2) Alias() string { return d.diagnoseResultV2Do.Alias() }

func (d diagnoseResultV2) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseResultV2Do.Columns(cols...)
}

func (d *diagnoseResultV2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseResultV2) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 14)
	d.fieldMap["id"] = d.ID
	d.fieldMap["diagnose_item_id"] = d.DiagnoseItemID
	d.fieldMap["diagnose_task_run_id"] = d.DiagnoseTaskRunID
	d.fieldMap["diagnose_result_level_num"] = d.DiagnoseResultLevelNum
	d.fieldMap["instance_id"] = d.InstanceID
	d.fieldMap["instance_name"] = d.InstanceName
	d.fieldMap["region"] = d.Region
	d.fieldMap["diagnose_message"] = d.DiagnoseMessage
	d.fieldMap["diagnose_result_level"] = d.DiagnoseResultLevel
	d.fieldMap["diagnose_suggestion"] = d.DiagnoseSuggestion
	d.fieldMap["diagnose_operate"] = d.DiagnoseOperate
	d.fieldMap["status"] = d.Status
	d.fieldMap["error_info"] = d.ErrorInfo
	d.fieldMap["product_category_id"] = d.ProductCategoryID
}

func (d diagnoseResultV2) clone(db *gorm.DB) diagnoseResultV2 {
	d.diagnoseResultV2Do.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseResultV2) replaceDB(db *gorm.DB) diagnoseResultV2 {
	d.diagnoseResultV2Do.ReplaceDB(db)
	return d
}

type diagnoseResultV2Do struct{ gen.DO }

func (d diagnoseResultV2Do) Debug() *diagnoseResultV2Do {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseResultV2Do) WithContext(ctx context.Context) *diagnoseResultV2Do {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseResultV2Do) ReadDB() *diagnoseResultV2Do {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseResultV2Do) WriteDB() *diagnoseResultV2Do {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseResultV2Do) Session(config *gorm.Session) *diagnoseResultV2Do {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseResultV2Do) Clauses(conds ...clause.Expression) *diagnoseResultV2Do {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseResultV2Do) Returning(value interface{}, columns ...string) *diagnoseResultV2Do {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseResultV2Do) Not(conds ...gen.Condition) *diagnoseResultV2Do {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseResultV2Do) Or(conds ...gen.Condition) *diagnoseResultV2Do {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseResultV2Do) Select(conds ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseResultV2Do) Where(conds ...gen.Condition) *diagnoseResultV2Do {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseResultV2Do) Order(conds ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseResultV2Do) Distinct(cols ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseResultV2Do) Omit(cols ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseResultV2Do) Join(table schema.Tabler, on ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseResultV2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseResultV2Do) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseResultV2Do) Group(cols ...field.Expr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseResultV2Do) Having(conds ...gen.Condition) *diagnoseResultV2Do {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseResultV2Do) Limit(limit int) *diagnoseResultV2Do {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseResultV2Do) Offset(offset int) *diagnoseResultV2Do {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseResultV2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseResultV2Do {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseResultV2Do) Unscoped() *diagnoseResultV2Do {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseResultV2Do) Create(values ...*model.DiagnoseResultV2) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseResultV2Do) CreateInBatches(values []*model.DiagnoseResultV2, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseResultV2Do) Save(values ...*model.DiagnoseResultV2) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseResultV2Do) First() (*model.DiagnoseResultV2, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResultV2), nil
	}
}

func (d diagnoseResultV2Do) Take() (*model.DiagnoseResultV2, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResultV2), nil
	}
}

func (d diagnoseResultV2Do) Last() (*model.DiagnoseResultV2, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResultV2), nil
	}
}

func (d diagnoseResultV2Do) Find() ([]*model.DiagnoseResultV2, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseResultV2), err
}

func (d diagnoseResultV2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseResultV2, err error) {
	buf := make([]*model.DiagnoseResultV2, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseResultV2Do) FindInBatches(result *[]*model.DiagnoseResultV2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseResultV2Do) Attrs(attrs ...field.AssignExpr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseResultV2Do) Assign(attrs ...field.AssignExpr) *diagnoseResultV2Do {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseResultV2Do) Joins(fields ...field.RelationField) *diagnoseResultV2Do {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseResultV2Do) Preload(fields ...field.RelationField) *diagnoseResultV2Do {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseResultV2Do) FirstOrInit() (*model.DiagnoseResultV2, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResultV2), nil
	}
}

func (d diagnoseResultV2Do) FirstOrCreate() (*model.DiagnoseResultV2, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResultV2), nil
	}
}

func (d diagnoseResultV2Do) FindByPage(offset int, limit int) (result []*model.DiagnoseResultV2, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseResultV2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseResultV2Do) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseResultV2Do) Delete(models ...*model.DiagnoseResultV2) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseResultV2Do) withDO(do gen.Dao) *diagnoseResultV2Do {
	d.DO = *do.(*gen.DO)
	return d
}
