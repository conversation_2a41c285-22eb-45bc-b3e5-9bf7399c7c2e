// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseTaskV2(db *gorm.DB, opts ...gen.DOOption) diagnoseTaskV2 {
	_diagnoseTaskV2 := diagnoseTaskV2{}

	_diagnoseTaskV2.diagnoseTaskV2Do.UseDB(db, opts...)
	_diagnoseTaskV2.diagnoseTaskV2Do.UseModel(&model.DiagnoseTaskV2{})

	tableName := _diagnoseTaskV2.diagnoseTaskV2Do.TableName()
	_diagnoseTaskV2.ALL = field.NewAsterisk(tableName)
	_diagnoseTaskV2.ID = field.NewInt64(tableName, "id")
	_diagnoseTaskV2.Name = field.NewString(tableName, "name")
	_diagnoseTaskV2.DiagnoseTemplateID = field.NewInt64(tableName, "diagnose_template_id")
	_diagnoseTaskV2.DiagnoseItemIds = field.NewString(tableName, "diagnose_item_ids")
	_diagnoseTaskV2.CreateUserID = field.NewString(tableName, "create_user_id")
	_diagnoseTaskV2.CreateUserName = field.NewString(tableName, "create_user_name")
	_diagnoseTaskV2.UpdateUserID = field.NewString(tableName, "update_user_id")
	_diagnoseTaskV2.UpdateUserName = field.NewString(tableName, "update_user_name")
	_diagnoseTaskV2.UpdateTime = field.NewTime(tableName, "update_time")
	_diagnoseTaskV2.LastRunTime = field.NewTime(tableName, "last_run_time")
	_diagnoseTaskV2.Dimension = field.NewString(tableName, "dimension")
	_diagnoseTaskV2.Origin = field.NewInt32(tableName, "origin")
	_diagnoseTaskV2.CreateTime = field.NewTime(tableName, "create_time")
	_diagnoseTaskV2.DiagnoseType = field.NewInt32(tableName, "diagnose_type")
	_diagnoseTaskV2.TicketID = field.NewString(tableName, "ticket_id")
	_diagnoseTaskV2.ProductCategoryIds = field.NewString(tableName, "product_category_ids")

	_diagnoseTaskV2.fillFieldMap()

	return _diagnoseTaskV2
}

// diagnoseTaskV2 诊断任务表V2
type diagnoseTaskV2 struct {
	diagnoseTaskV2Do diagnoseTaskV2Do

	ALL                field.Asterisk
	ID                 field.Int64  // 诊断任务，自增ID
	Name               field.String // 诊断任务名称
	DiagnoseTemplateID field.Int64  // 诊断场景模板ID
	DiagnoseItemIds    field.String // 诊断项ID
	CreateUserID       field.String // 发起人ID
	CreateUserName     field.String // 发起人姓名
	UpdateUserID       field.String // 更新人ID
	UpdateUserName     field.String // 更新人姓名
	UpdateTime         field.Time   // 更新时间
	LastRunTime        field.Time   // 最近执行时间
	Dimension          field.String // 维度
	Origin             field.Int32  // 来源 1-工单 2-工具平台
	CreateTime         field.Time   // 创建时间
	DiagnoseType       field.Int32  // 诊断类型 1-原子 2-编排
	TicketID           field.String // 关联工单ID
	ProductCategoryIds field.String // 产品类型ID

	fieldMap map[string]field.Expr
}

func (d diagnoseTaskV2) Table(newTableName string) *diagnoseTaskV2 {
	d.diagnoseTaskV2Do.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseTaskV2) As(alias string) *diagnoseTaskV2 {
	d.diagnoseTaskV2Do.DO = *(d.diagnoseTaskV2Do.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseTaskV2) updateTableName(table string) *diagnoseTaskV2 {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.Name = field.NewString(table, "name")
	d.DiagnoseTemplateID = field.NewInt64(table, "diagnose_template_id")
	d.DiagnoseItemIds = field.NewString(table, "diagnose_item_ids")
	d.CreateUserID = field.NewString(table, "create_user_id")
	d.CreateUserName = field.NewString(table, "create_user_name")
	d.UpdateUserID = field.NewString(table, "update_user_id")
	d.UpdateUserName = field.NewString(table, "update_user_name")
	d.UpdateTime = field.NewTime(table, "update_time")
	d.LastRunTime = field.NewTime(table, "last_run_time")
	d.Dimension = field.NewString(table, "dimension")
	d.Origin = field.NewInt32(table, "origin")
	d.CreateTime = field.NewTime(table, "create_time")
	d.DiagnoseType = field.NewInt32(table, "diagnose_type")
	d.TicketID = field.NewString(table, "ticket_id")
	d.ProductCategoryIds = field.NewString(table, "product_category_ids")

	d.fillFieldMap()

	return d
}

func (d *diagnoseTaskV2) WithContext(ctx context.Context) *diagnoseTaskV2Do {
	return d.diagnoseTaskV2Do.WithContext(ctx)
}

func (d diagnoseTaskV2) TableName() string { return d.diagnoseTaskV2Do.TableName() }

func (d diagnoseTaskV2) Alias() string { return d.diagnoseTaskV2Do.Alias() }

func (d diagnoseTaskV2) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseTaskV2Do.Columns(cols...)
}

func (d *diagnoseTaskV2) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseTaskV2) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 16)
	d.fieldMap["id"] = d.ID
	d.fieldMap["name"] = d.Name
	d.fieldMap["diagnose_template_id"] = d.DiagnoseTemplateID
	d.fieldMap["diagnose_item_ids"] = d.DiagnoseItemIds
	d.fieldMap["create_user_id"] = d.CreateUserID
	d.fieldMap["create_user_name"] = d.CreateUserName
	d.fieldMap["update_user_id"] = d.UpdateUserID
	d.fieldMap["update_user_name"] = d.UpdateUserName
	d.fieldMap["update_time"] = d.UpdateTime
	d.fieldMap["last_run_time"] = d.LastRunTime
	d.fieldMap["dimension"] = d.Dimension
	d.fieldMap["origin"] = d.Origin
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["diagnose_type"] = d.DiagnoseType
	d.fieldMap["ticket_id"] = d.TicketID
	d.fieldMap["product_category_ids"] = d.ProductCategoryIds
}

func (d diagnoseTaskV2) clone(db *gorm.DB) diagnoseTaskV2 {
	d.diagnoseTaskV2Do.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseTaskV2) replaceDB(db *gorm.DB) diagnoseTaskV2 {
	d.diagnoseTaskV2Do.ReplaceDB(db)
	return d
}

type diagnoseTaskV2Do struct{ gen.DO }

func (d diagnoseTaskV2Do) Debug() *diagnoseTaskV2Do {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseTaskV2Do) WithContext(ctx context.Context) *diagnoseTaskV2Do {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseTaskV2Do) ReadDB() *diagnoseTaskV2Do {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseTaskV2Do) WriteDB() *diagnoseTaskV2Do {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseTaskV2Do) Session(config *gorm.Session) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseTaskV2Do) Clauses(conds ...clause.Expression) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseTaskV2Do) Returning(value interface{}, columns ...string) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseTaskV2Do) Not(conds ...gen.Condition) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseTaskV2Do) Or(conds ...gen.Condition) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseTaskV2Do) Select(conds ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseTaskV2Do) Where(conds ...gen.Condition) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseTaskV2Do) Order(conds ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseTaskV2Do) Distinct(cols ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseTaskV2Do) Omit(cols ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseTaskV2Do) Join(table schema.Tabler, on ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseTaskV2Do) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseTaskV2Do) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseTaskV2Do) Group(cols ...field.Expr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseTaskV2Do) Having(conds ...gen.Condition) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseTaskV2Do) Limit(limit int) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseTaskV2Do) Offset(offset int) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseTaskV2Do) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseTaskV2Do) Unscoped() *diagnoseTaskV2Do {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseTaskV2Do) Create(values ...*model.DiagnoseTaskV2) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseTaskV2Do) CreateInBatches(values []*model.DiagnoseTaskV2, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseTaskV2Do) Save(values ...*model.DiagnoseTaskV2) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseTaskV2Do) First() (*model.DiagnoseTaskV2, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskV2), nil
	}
}

func (d diagnoseTaskV2Do) Take() (*model.DiagnoseTaskV2, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskV2), nil
	}
}

func (d diagnoseTaskV2Do) Last() (*model.DiagnoseTaskV2, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskV2), nil
	}
}

func (d diagnoseTaskV2Do) Find() ([]*model.DiagnoseTaskV2, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseTaskV2), err
}

func (d diagnoseTaskV2Do) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseTaskV2, err error) {
	buf := make([]*model.DiagnoseTaskV2, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseTaskV2Do) FindInBatches(result *[]*model.DiagnoseTaskV2, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseTaskV2Do) Attrs(attrs ...field.AssignExpr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseTaskV2Do) Assign(attrs ...field.AssignExpr) *diagnoseTaskV2Do {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseTaskV2Do) Joins(fields ...field.RelationField) *diagnoseTaskV2Do {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseTaskV2Do) Preload(fields ...field.RelationField) *diagnoseTaskV2Do {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseTaskV2Do) FirstOrInit() (*model.DiagnoseTaskV2, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskV2), nil
	}
}

func (d diagnoseTaskV2Do) FirstOrCreate() (*model.DiagnoseTaskV2, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskV2), nil
	}
}

func (d diagnoseTaskV2Do) FindByPage(offset int, limit int) (result []*model.DiagnoseTaskV2, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseTaskV2Do) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseTaskV2Do) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseTaskV2Do) Delete(models ...*model.DiagnoseTaskV2) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseTaskV2Do) withDO(do gen.Dao) *diagnoseTaskV2Do {
	d.DO = *do.(*gen.DO)
	return d
}
