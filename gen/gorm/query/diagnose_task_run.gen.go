// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseTaskRun(db *gorm.DB, opts ...gen.DOOption) diagnoseTaskRun {
	_diagnoseTaskRun := diagnoseTaskRun{}

	_diagnoseTaskRun.diagnoseTaskRunDo.UseDB(db, opts...)
	_diagnoseTaskRun.diagnoseTaskRunDo.UseModel(&model.DiagnoseTaskRun{})

	tableName := _diagnoseTaskRun.diagnoseTaskRunDo.TableName()
	_diagnoseTaskRun.ALL = field.NewAsterisk(tableName)
	_diagnoseTaskRun.ID = field.NewInt64(tableName, "id")
	_diagnoseTaskRun.Name = field.NewString(tableName, "name")
	_diagnoseTaskRun.DiagnoseTaskID = field.NewInt64(tableName, "diagnose_task_id")
	_diagnoseTaskRun.AccountID = field.NewString(tableName, "account_id")
	_diagnoseTaskRun.Resources = field.NewString(tableName, "resources")
	_diagnoseTaskRun.CreateUserID = field.NewString(tableName, "create_user_id")
	_diagnoseTaskRun.CreateUserName = field.NewString(tableName, "create_user_name")
	_diagnoseTaskRun.StartTime = field.NewTime(tableName, "start_time")
	_diagnoseTaskRun.EndTime = field.NewTime(tableName, "end_time")
	_diagnoseTaskRun.FeedbackID = field.NewInt64(tableName, "feedback_id")
	_diagnoseTaskRun.Status = field.NewString(tableName, "status")
	_diagnoseTaskRun.DiagnoseStartTime = field.NewInt64(tableName, "diagnose_start_time")
	_diagnoseTaskRun.DiagnoseEndTime = field.NewInt64(tableName, "diagnose_end_time")
	_diagnoseTaskRun.StateMachineInfo = field.NewString(tableName, "state_machine_info")
	_diagnoseTaskRun.DiagnoseItemStatus = field.NewString(tableName, "diagnose_item_status")
	_diagnoseTaskRun.TicketID = field.NewString(tableName, "ticket_id")
	_diagnoseTaskRun.DiagnoseTemplateID = field.NewInt64(tableName, "diagnose_template_id")
	_diagnoseTaskRun.DiagnoseTemplateName = field.NewString(tableName, "diagnose_template_name")
	_diagnoseTaskRun.DiagnoseType = field.NewInt32(tableName, "diagnose_type")
	_diagnoseTaskRun.ResourceTypeIds = field.NewString(tableName, "resource_type_ids")
	_diagnoseTaskRun.ActivityDiagnoses = field.NewString(tableName, "activity_diagnoses")
	_diagnoseTaskRun.Origin = field.NewInt32(tableName, "origin")
	_diagnoseTaskRun.OldTaskID = field.NewInt64(tableName, "old_task_id")

	_diagnoseTaskRun.fillFieldMap()

	return _diagnoseTaskRun
}

// diagnoseTaskRun 诊断任务运行表
type diagnoseTaskRun struct {
	diagnoseTaskRunDo diagnoseTaskRunDo

	ALL                  field.Asterisk
	ID                   field.Int64  // 执行ID，自增ID
	Name                 field.String // 诊断任务执行名称
	DiagnoseTaskID       field.Int64  // 关联诊断任务ID
	AccountID            field.String // 诊断账号ID
	Resources            field.String // 诊断资源
	CreateUserID         field.String // 发起人ID
	CreateUserName       field.String // 发起人姓名
	StartTime            field.Time   // 开始时间
	EndTime              field.Time   // 结束时间
	FeedbackID           field.Int64  // 反馈ID
	Status               field.String // 诊断任务状态
	DiagnoseStartTime    field.Int64  // 诊断开始时间
	DiagnoseEndTime      field.Int64  // 诊断结束时间
	StateMachineInfo     field.String // 诊断编排信息快照
	DiagnoseItemStatus   field.String // 诊断项运行状态
	TicketID             field.String // 关联工单ID
	DiagnoseTemplateID   field.Int64  // 关联诊断模板ID
	DiagnoseTemplateName field.String // 关联诊断模板名称
	DiagnoseType         field.Int32  // 诊断类型 1-原子 2-编排
	ResourceTypeIds      field.String // 资源类型id
	ActivityDiagnoses    field.String // 模板activity映射
	Origin               field.Int32  // 来源
	OldTaskID            field.Int64  // 原始表taskID

	fieldMap map[string]field.Expr
}

func (d diagnoseTaskRun) Table(newTableName string) *diagnoseTaskRun {
	d.diagnoseTaskRunDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseTaskRun) As(alias string) *diagnoseTaskRun {
	d.diagnoseTaskRunDo.DO = *(d.diagnoseTaskRunDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseTaskRun) updateTableName(table string) *diagnoseTaskRun {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.Name = field.NewString(table, "name")
	d.DiagnoseTaskID = field.NewInt64(table, "diagnose_task_id")
	d.AccountID = field.NewString(table, "account_id")
	d.Resources = field.NewString(table, "resources")
	d.CreateUserID = field.NewString(table, "create_user_id")
	d.CreateUserName = field.NewString(table, "create_user_name")
	d.StartTime = field.NewTime(table, "start_time")
	d.EndTime = field.NewTime(table, "end_time")
	d.FeedbackID = field.NewInt64(table, "feedback_id")
	d.Status = field.NewString(table, "status")
	d.DiagnoseStartTime = field.NewInt64(table, "diagnose_start_time")
	d.DiagnoseEndTime = field.NewInt64(table, "diagnose_end_time")
	d.StateMachineInfo = field.NewString(table, "state_machine_info")
	d.DiagnoseItemStatus = field.NewString(table, "diagnose_item_status")
	d.TicketID = field.NewString(table, "ticket_id")
	d.DiagnoseTemplateID = field.NewInt64(table, "diagnose_template_id")
	d.DiagnoseTemplateName = field.NewString(table, "diagnose_template_name")
	d.DiagnoseType = field.NewInt32(table, "diagnose_type")
	d.ResourceTypeIds = field.NewString(table, "resource_type_ids")
	d.ActivityDiagnoses = field.NewString(table, "activity_diagnoses")
	d.Origin = field.NewInt32(table, "origin")
	d.OldTaskID = field.NewInt64(table, "old_task_id")

	d.fillFieldMap()

	return d
}

func (d *diagnoseTaskRun) WithContext(ctx context.Context) *diagnoseTaskRunDo {
	return d.diagnoseTaskRunDo.WithContext(ctx)
}

func (d diagnoseTaskRun) TableName() string { return d.diagnoseTaskRunDo.TableName() }

func (d diagnoseTaskRun) Alias() string { return d.diagnoseTaskRunDo.Alias() }

func (d diagnoseTaskRun) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseTaskRunDo.Columns(cols...)
}

func (d *diagnoseTaskRun) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseTaskRun) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 23)
	d.fieldMap["id"] = d.ID
	d.fieldMap["name"] = d.Name
	d.fieldMap["diagnose_task_id"] = d.DiagnoseTaskID
	d.fieldMap["account_id"] = d.AccountID
	d.fieldMap["resources"] = d.Resources
	d.fieldMap["create_user_id"] = d.CreateUserID
	d.fieldMap["create_user_name"] = d.CreateUserName
	d.fieldMap["start_time"] = d.StartTime
	d.fieldMap["end_time"] = d.EndTime
	d.fieldMap["feedback_id"] = d.FeedbackID
	d.fieldMap["status"] = d.Status
	d.fieldMap["diagnose_start_time"] = d.DiagnoseStartTime
	d.fieldMap["diagnose_end_time"] = d.DiagnoseEndTime
	d.fieldMap["state_machine_info"] = d.StateMachineInfo
	d.fieldMap["diagnose_item_status"] = d.DiagnoseItemStatus
	d.fieldMap["ticket_id"] = d.TicketID
	d.fieldMap["diagnose_template_id"] = d.DiagnoseTemplateID
	d.fieldMap["diagnose_template_name"] = d.DiagnoseTemplateName
	d.fieldMap["diagnose_type"] = d.DiagnoseType
	d.fieldMap["resource_type_ids"] = d.ResourceTypeIds
	d.fieldMap["activity_diagnoses"] = d.ActivityDiagnoses
	d.fieldMap["origin"] = d.Origin
	d.fieldMap["old_task_id"] = d.OldTaskID
}

func (d diagnoseTaskRun) clone(db *gorm.DB) diagnoseTaskRun {
	d.diagnoseTaskRunDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseTaskRun) replaceDB(db *gorm.DB) diagnoseTaskRun {
	d.diagnoseTaskRunDo.ReplaceDB(db)
	return d
}

type diagnoseTaskRunDo struct{ gen.DO }

func (d diagnoseTaskRunDo) Debug() *diagnoseTaskRunDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseTaskRunDo) WithContext(ctx context.Context) *diagnoseTaskRunDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseTaskRunDo) ReadDB() *diagnoseTaskRunDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseTaskRunDo) WriteDB() *diagnoseTaskRunDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseTaskRunDo) Session(config *gorm.Session) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseTaskRunDo) Clauses(conds ...clause.Expression) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseTaskRunDo) Returning(value interface{}, columns ...string) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseTaskRunDo) Not(conds ...gen.Condition) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseTaskRunDo) Or(conds ...gen.Condition) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseTaskRunDo) Select(conds ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseTaskRunDo) Where(conds ...gen.Condition) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseTaskRunDo) Order(conds ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseTaskRunDo) Distinct(cols ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseTaskRunDo) Omit(cols ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseTaskRunDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseTaskRunDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseTaskRunDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseTaskRunDo) Group(cols ...field.Expr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseTaskRunDo) Having(conds ...gen.Condition) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseTaskRunDo) Limit(limit int) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseTaskRunDo) Offset(offset int) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseTaskRunDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseTaskRunDo) Unscoped() *diagnoseTaskRunDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseTaskRunDo) Create(values ...*model.DiagnoseTaskRun) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseTaskRunDo) CreateInBatches(values []*model.DiagnoseTaskRun, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseTaskRunDo) Save(values ...*model.DiagnoseTaskRun) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseTaskRunDo) First() (*model.DiagnoseTaskRun, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskRun), nil
	}
}

func (d diagnoseTaskRunDo) Take() (*model.DiagnoseTaskRun, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskRun), nil
	}
}

func (d diagnoseTaskRunDo) Last() (*model.DiagnoseTaskRun, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskRun), nil
	}
}

func (d diagnoseTaskRunDo) Find() ([]*model.DiagnoseTaskRun, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseTaskRun), err
}

func (d diagnoseTaskRunDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseTaskRun, err error) {
	buf := make([]*model.DiagnoseTaskRun, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseTaskRunDo) FindInBatches(result *[]*model.DiagnoseTaskRun, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseTaskRunDo) Attrs(attrs ...field.AssignExpr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseTaskRunDo) Assign(attrs ...field.AssignExpr) *diagnoseTaskRunDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseTaskRunDo) Joins(fields ...field.RelationField) *diagnoseTaskRunDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseTaskRunDo) Preload(fields ...field.RelationField) *diagnoseTaskRunDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseTaskRunDo) FirstOrInit() (*model.DiagnoseTaskRun, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskRun), nil
	}
}

func (d diagnoseTaskRunDo) FirstOrCreate() (*model.DiagnoseTaskRun, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseTaskRun), nil
	}
}

func (d diagnoseTaskRunDo) FindByPage(offset int, limit int) (result []*model.DiagnoseTaskRun, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseTaskRunDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseTaskRunDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseTaskRunDo) Delete(models ...*model.DiagnoseTaskRun) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseTaskRunDo) withDO(do gen.Dao) *diagnoseTaskRunDo {
	d.DO = *do.(*gen.DO)
	return d
}
