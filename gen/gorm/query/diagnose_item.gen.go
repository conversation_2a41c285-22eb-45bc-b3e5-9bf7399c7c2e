// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseItem(db *gorm.DB, opts ...gen.DOOption) diagnoseItem {
	_diagnoseItem := diagnoseItem{}

	_diagnoseItem.diagnoseItemDo.UseDB(db, opts...)
	_diagnoseItem.diagnoseItemDo.UseModel(&model.DiagnoseItem{})

	tableName := _diagnoseItem.diagnoseItemDo.TableName()
	_diagnoseItem.ALL = field.NewAsterisk(tableName)
	_diagnoseItem.DiagnoseItemID = field.NewInt64(tableName, "diagnose_item_id")
	_diagnoseItem.DiagnoseItemName = field.NewString(tableName, "diagnose_item_name")
	_diagnoseItem.CreateTime = field.NewTime(tableName, "create_time")
	_diagnoseItem.CreatorID = field.NewString(tableName, "creator_id")
	_diagnoseItem.CreatorName = field.NewString(tableName, "creator_name")
	_diagnoseItem.UpdateTime = field.NewTime(tableName, "update_time")
	_diagnoseItem.UpdaterID = field.NewString(tableName, "updater_id")
	_diagnoseItem.UpdaterName = field.NewString(tableName, "updater_name")
	_diagnoseItem.DeleteAt = field.NewTime(tableName, "delete_at")
	_diagnoseItem.DeleterID = field.NewString(tableName, "deleter_id")
	_diagnoseItem.DeleterName = field.NewString(tableName, "deleter_name")
	_diagnoseItem.DiagnoseItemCode = field.NewString(tableName, "diagnose_item_code")
	_diagnoseItem.Status = field.NewString(tableName, "status")
	_diagnoseItem.Product = field.NewString(tableName, "product")
	_diagnoseItem.Subproduct = field.NewString(tableName, "subproduct")
	_diagnoseItem.AccessType = field.NewString(tableName, "access_type")
	_diagnoseItem.AccessResponsiblePerson = field.NewString(tableName, "access_responsible_person")
	_diagnoseItem.AccessPath = field.NewString(tableName, "access_path")
	_diagnoseItem.InterAction = field.NewString(tableName, "inter_action")
	_diagnoseItem.InterVersion = field.NewString(tableName, "inter_version")
	_diagnoseItem.Suggestion = field.NewString(tableName, "suggestion")
	_diagnoseItem.SuggestionLink = field.NewString(tableName, "suggestion_link")
	_diagnoseItem.Timeout = field.NewInt32(tableName, "timeout")
	_diagnoseItem.InterParams = field.NewString(tableName, "inter_params")
	_diagnoseItem.StateMachineInfo = field.NewString(tableName, "state_machine_info")
	_diagnoseItem.ResourceType = field.NewString(tableName, "resource_type")
	_diagnoseItem.ProductCategoryID = field.NewInt64(tableName, "product_category_id")
	_diagnoseItem.Activity = field.NewString(tableName, "activity")
	_diagnoseItem.DiagnoseItemDescription = field.NewString(tableName, "diagnose_item_description")
	_diagnoseItem.NeedAuth = field.NewBool(tableName, "need_auth")

	_diagnoseItem.fillFieldMap()

	return _diagnoseItem
}

type diagnoseItem struct {
	diagnoseItemDo diagnoseItemDo

	ALL                     field.Asterisk
	DiagnoseItemID          field.Int64  // 诊断项Id
	DiagnoseItemName        field.String // 诊断项名称
	CreateTime              field.Time   // 诊断项创建时间
	CreatorID               field.String // 创建人账号Id
	CreatorName             field.String // 创建人账号名
	UpdateTime              field.Time   // 诊断项更新时间
	UpdaterID               field.String // 更新人账号Id
	UpdaterName             field.String // 更新人账号名
	DeleteAt                field.Time   // 诊断项删除时间
	DeleterID               field.String // 删除人账号Id
	DeleterName             field.String // 删除人账号名
	DiagnoseItemCode        field.String // 诊断项编码
	Status                  field.String // 诊断项状态
	Product                 field.String // 产品分类
	Subproduct              field.String // 子产品分类
	AccessType              field.String // 接入类型
	AccessResponsiblePerson field.String // 接入负责人
	AccessPath              field.String // 接入路径
	InterAction             field.String
	InterVersion            field.String // 接口版本
	Suggestion              field.String // 处理建议
	SuggestionLink          field.String // 处理建议的链接
	Timeout                 field.Int32  // 超时时间（单位：秒）
	InterParams             field.String // 接口参数
	StateMachineInfo        field.String // 状态机语言
	ResourceType            field.String // 资源类型
	ProductCategoryID       field.Int64  // 产品类型ID
	Activity                field.String // 诊断项关联的activity
	DiagnoseItemDescription field.String // 诊断项描述
	NeedAuth                field.Bool   // 表示该诊断项是否需要用户授权

	fieldMap map[string]field.Expr
}

func (d diagnoseItem) Table(newTableName string) *diagnoseItem {
	d.diagnoseItemDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseItem) As(alias string) *diagnoseItem {
	d.diagnoseItemDo.DO = *(d.diagnoseItemDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseItem) updateTableName(table string) *diagnoseItem {
	d.ALL = field.NewAsterisk(table)
	d.DiagnoseItemID = field.NewInt64(table, "diagnose_item_id")
	d.DiagnoseItemName = field.NewString(table, "diagnose_item_name")
	d.CreateTime = field.NewTime(table, "create_time")
	d.CreatorID = field.NewString(table, "creator_id")
	d.CreatorName = field.NewString(table, "creator_name")
	d.UpdateTime = field.NewTime(table, "update_time")
	d.UpdaterID = field.NewString(table, "updater_id")
	d.UpdaterName = field.NewString(table, "updater_name")
	d.DeleteAt = field.NewTime(table, "delete_at")
	d.DeleterID = field.NewString(table, "deleter_id")
	d.DeleterName = field.NewString(table, "deleter_name")
	d.DiagnoseItemCode = field.NewString(table, "diagnose_item_code")
	d.Status = field.NewString(table, "status")
	d.Product = field.NewString(table, "product")
	d.Subproduct = field.NewString(table, "subproduct")
	d.AccessType = field.NewString(table, "access_type")
	d.AccessResponsiblePerson = field.NewString(table, "access_responsible_person")
	d.AccessPath = field.NewString(table, "access_path")
	d.InterAction = field.NewString(table, "inter_action")
	d.InterVersion = field.NewString(table, "inter_version")
	d.Suggestion = field.NewString(table, "suggestion")
	d.SuggestionLink = field.NewString(table, "suggestion_link")
	d.Timeout = field.NewInt32(table, "timeout")
	d.InterParams = field.NewString(table, "inter_params")
	d.StateMachineInfo = field.NewString(table, "state_machine_info")
	d.ResourceType = field.NewString(table, "resource_type")
	d.ProductCategoryID = field.NewInt64(table, "product_category_id")
	d.Activity = field.NewString(table, "activity")
	d.DiagnoseItemDescription = field.NewString(table, "diagnose_item_description")
	d.NeedAuth = field.NewBool(table, "need_auth")

	d.fillFieldMap()

	return d
}

func (d *diagnoseItem) WithContext(ctx context.Context) *diagnoseItemDo {
	return d.diagnoseItemDo.WithContext(ctx)
}

func (d diagnoseItem) TableName() string { return d.diagnoseItemDo.TableName() }

func (d diagnoseItem) Alias() string { return d.diagnoseItemDo.Alias() }

func (d diagnoseItem) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseItemDo.Columns(cols...)
}

func (d *diagnoseItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseItem) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 30)
	d.fieldMap["diagnose_item_id"] = d.DiagnoseItemID
	d.fieldMap["diagnose_item_name"] = d.DiagnoseItemName
	d.fieldMap["create_time"] = d.CreateTime
	d.fieldMap["creator_id"] = d.CreatorID
	d.fieldMap["creator_name"] = d.CreatorName
	d.fieldMap["update_time"] = d.UpdateTime
	d.fieldMap["updater_id"] = d.UpdaterID
	d.fieldMap["updater_name"] = d.UpdaterName
	d.fieldMap["delete_at"] = d.DeleteAt
	d.fieldMap["deleter_id"] = d.DeleterID
	d.fieldMap["deleter_name"] = d.DeleterName
	d.fieldMap["diagnose_item_code"] = d.DiagnoseItemCode
	d.fieldMap["status"] = d.Status
	d.fieldMap["product"] = d.Product
	d.fieldMap["subproduct"] = d.Subproduct
	d.fieldMap["access_type"] = d.AccessType
	d.fieldMap["access_responsible_person"] = d.AccessResponsiblePerson
	d.fieldMap["access_path"] = d.AccessPath
	d.fieldMap["inter_action"] = d.InterAction
	d.fieldMap["inter_version"] = d.InterVersion
	d.fieldMap["suggestion"] = d.Suggestion
	d.fieldMap["suggestion_link"] = d.SuggestionLink
	d.fieldMap["timeout"] = d.Timeout
	d.fieldMap["inter_params"] = d.InterParams
	d.fieldMap["state_machine_info"] = d.StateMachineInfo
	d.fieldMap["resource_type"] = d.ResourceType
	d.fieldMap["product_category_id"] = d.ProductCategoryID
	d.fieldMap["activity"] = d.Activity
	d.fieldMap["diagnose_item_description"] = d.DiagnoseItemDescription
	d.fieldMap["need_auth"] = d.NeedAuth
}

func (d diagnoseItem) clone(db *gorm.DB) diagnoseItem {
	d.diagnoseItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseItem) replaceDB(db *gorm.DB) diagnoseItem {
	d.diagnoseItemDo.ReplaceDB(db)
	return d
}

type diagnoseItemDo struct{ gen.DO }

func (d diagnoseItemDo) Debug() *diagnoseItemDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseItemDo) WithContext(ctx context.Context) *diagnoseItemDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseItemDo) ReadDB() *diagnoseItemDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseItemDo) WriteDB() *diagnoseItemDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseItemDo) Session(config *gorm.Session) *diagnoseItemDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseItemDo) Clauses(conds ...clause.Expression) *diagnoseItemDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseItemDo) Returning(value interface{}, columns ...string) *diagnoseItemDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseItemDo) Not(conds ...gen.Condition) *diagnoseItemDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseItemDo) Or(conds ...gen.Condition) *diagnoseItemDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseItemDo) Select(conds ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseItemDo) Where(conds ...gen.Condition) *diagnoseItemDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseItemDo) Order(conds ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseItemDo) Distinct(cols ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseItemDo) Omit(cols ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseItemDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseItemDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseItemDo) Group(cols ...field.Expr) *diagnoseItemDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseItemDo) Having(conds ...gen.Condition) *diagnoseItemDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseItemDo) Limit(limit int) *diagnoseItemDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseItemDo) Offset(offset int) *diagnoseItemDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseItemDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseItemDo) Unscoped() *diagnoseItemDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseItemDo) Create(values ...*model.DiagnoseItem) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseItemDo) CreateInBatches(values []*model.DiagnoseItem, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseItemDo) Save(values ...*model.DiagnoseItem) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseItemDo) First() (*model.DiagnoseItem, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseItem), nil
	}
}

func (d diagnoseItemDo) Take() (*model.DiagnoseItem, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseItem), nil
	}
}

func (d diagnoseItemDo) Last() (*model.DiagnoseItem, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseItem), nil
	}
}

func (d diagnoseItemDo) Find() ([]*model.DiagnoseItem, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseItem), err
}

func (d diagnoseItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseItem, err error) {
	buf := make([]*model.DiagnoseItem, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseItemDo) FindInBatches(result *[]*model.DiagnoseItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseItemDo) Attrs(attrs ...field.AssignExpr) *diagnoseItemDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseItemDo) Assign(attrs ...field.AssignExpr) *diagnoseItemDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseItemDo) Joins(fields ...field.RelationField) *diagnoseItemDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseItemDo) Preload(fields ...field.RelationField) *diagnoseItemDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseItemDo) FirstOrInit() (*model.DiagnoseItem, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseItem), nil
	}
}

func (d diagnoseItemDo) FirstOrCreate() (*model.DiagnoseItem, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseItem), nil
	}
}

func (d diagnoseItemDo) FindByPage(offset int, limit int) (result []*model.DiagnoseItem, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseItemDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseItemDo) Delete(models ...*model.DiagnoseItem) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseItemDo) withDO(do gen.Dao) *diagnoseItemDo {
	d.DO = *do.(*gen.DO)
	return d
}
