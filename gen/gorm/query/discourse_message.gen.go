// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiscourseMessage(db *gorm.DB, opts ...gen.DOOption) discourseMessage {
	_discourseMessage := discourseMessage{}

	_discourseMessage.discourseMessageDo.UseDB(db, opts...)
	_discourseMessage.discourseMessageDo.UseModel(&model.DiscourseMessage{})

	tableName := _discourseMessage.discourseMessageDo.TableName()
	_discourseMessage.ALL = field.NewAsterisk(tableName)
	_discourseMessage.ID = field.NewInt64(tableName, "id")
	_discourseMessage.SessionID = field.NewInt64(tableName, "session_id")
	_discourseMessage.Type = field.NewInt32(tableName, "type")
	_discourseMessage.TaskID = field.NewInt64(tableName, "task_id")
	_discourseMessage.TaskRunID = field.NewInt64(tableName, "task_run_id")
	_discourseMessage.StartDate = field.NewTime(tableName, "start_date")
	_discourseMessage.Summary = field.NewString(tableName, "summary")
	_discourseMessage.Recommend = field.NewString(tableName, "recommend")
	_discourseMessage.Requery = field.NewString(tableName, "requery")

	_discourseMessage.fillFieldMap()

	return _discourseMessage
}

// discourseMessage 对话信息表
type discourseMessage struct {
	discourseMessageDo discourseMessageDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键ID
	SessionID field.Int64  // 会话表 id
	Type      field.Int32  // 类型 1-中台 2-maas
	TaskID    field.Int64  // 诊断任务 id
	TaskRunID field.Int64  // 诊断执行任务 id
	StartDate field.Time   // 时间
	Summary   field.String // 总结 id
	Recommend field.String // 推荐 id
	Requery   field.String // 用户 query

	fieldMap map[string]field.Expr
}

func (d discourseMessage) Table(newTableName string) *discourseMessage {
	d.discourseMessageDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d discourseMessage) As(alias string) *discourseMessage {
	d.discourseMessageDo.DO = *(d.discourseMessageDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *discourseMessage) updateTableName(table string) *discourseMessage {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.SessionID = field.NewInt64(table, "session_id")
	d.Type = field.NewInt32(table, "type")
	d.TaskID = field.NewInt64(table, "task_id")
	d.TaskRunID = field.NewInt64(table, "task_run_id")
	d.StartDate = field.NewTime(table, "start_date")
	d.Summary = field.NewString(table, "summary")
	d.Recommend = field.NewString(table, "recommend")
	d.Requery = field.NewString(table, "requery")

	d.fillFieldMap()

	return d
}

func (d *discourseMessage) WithContext(ctx context.Context) *discourseMessageDo {
	return d.discourseMessageDo.WithContext(ctx)
}

func (d discourseMessage) TableName() string { return d.discourseMessageDo.TableName() }

func (d discourseMessage) Alias() string { return d.discourseMessageDo.Alias() }

func (d discourseMessage) Columns(cols ...field.Expr) gen.Columns {
	return d.discourseMessageDo.Columns(cols...)
}

func (d *discourseMessage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *discourseMessage) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 9)
	d.fieldMap["id"] = d.ID
	d.fieldMap["session_id"] = d.SessionID
	d.fieldMap["type"] = d.Type
	d.fieldMap["task_id"] = d.TaskID
	d.fieldMap["task_run_id"] = d.TaskRunID
	d.fieldMap["start_date"] = d.StartDate
	d.fieldMap["summary"] = d.Summary
	d.fieldMap["recommend"] = d.Recommend
	d.fieldMap["requery"] = d.Requery
}

func (d discourseMessage) clone(db *gorm.DB) discourseMessage {
	d.discourseMessageDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d discourseMessage) replaceDB(db *gorm.DB) discourseMessage {
	d.discourseMessageDo.ReplaceDB(db)
	return d
}

type discourseMessageDo struct{ gen.DO }

func (d discourseMessageDo) Debug() *discourseMessageDo {
	return d.withDO(d.DO.Debug())
}

func (d discourseMessageDo) WithContext(ctx context.Context) *discourseMessageDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d discourseMessageDo) ReadDB() *discourseMessageDo {
	return d.Clauses(dbresolver.Read)
}

func (d discourseMessageDo) WriteDB() *discourseMessageDo {
	return d.Clauses(dbresolver.Write)
}

func (d discourseMessageDo) Session(config *gorm.Session) *discourseMessageDo {
	return d.withDO(d.DO.Session(config))
}

func (d discourseMessageDo) Clauses(conds ...clause.Expression) *discourseMessageDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d discourseMessageDo) Returning(value interface{}, columns ...string) *discourseMessageDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d discourseMessageDo) Not(conds ...gen.Condition) *discourseMessageDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d discourseMessageDo) Or(conds ...gen.Condition) *discourseMessageDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d discourseMessageDo) Select(conds ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d discourseMessageDo) Where(conds ...gen.Condition) *discourseMessageDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d discourseMessageDo) Order(conds ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d discourseMessageDo) Distinct(cols ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d discourseMessageDo) Omit(cols ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d discourseMessageDo) Join(table schema.Tabler, on ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d discourseMessageDo) LeftJoin(table schema.Tabler, on ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d discourseMessageDo) RightJoin(table schema.Tabler, on ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d discourseMessageDo) Group(cols ...field.Expr) *discourseMessageDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d discourseMessageDo) Having(conds ...gen.Condition) *discourseMessageDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d discourseMessageDo) Limit(limit int) *discourseMessageDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d discourseMessageDo) Offset(offset int) *discourseMessageDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d discourseMessageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *discourseMessageDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d discourseMessageDo) Unscoped() *discourseMessageDo {
	return d.withDO(d.DO.Unscoped())
}

func (d discourseMessageDo) Create(values ...*model.DiscourseMessage) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d discourseMessageDo) CreateInBatches(values []*model.DiscourseMessage, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d discourseMessageDo) Save(values ...*model.DiscourseMessage) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d discourseMessageDo) First() (*model.DiscourseMessage, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseMessage), nil
	}
}

func (d discourseMessageDo) Take() (*model.DiscourseMessage, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseMessage), nil
	}
}

func (d discourseMessageDo) Last() (*model.DiscourseMessage, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseMessage), nil
	}
}

func (d discourseMessageDo) Find() ([]*model.DiscourseMessage, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiscourseMessage), err
}

func (d discourseMessageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiscourseMessage, err error) {
	buf := make([]*model.DiscourseMessage, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d discourseMessageDo) FindInBatches(result *[]*model.DiscourseMessage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d discourseMessageDo) Attrs(attrs ...field.AssignExpr) *discourseMessageDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d discourseMessageDo) Assign(attrs ...field.AssignExpr) *discourseMessageDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d discourseMessageDo) Joins(fields ...field.RelationField) *discourseMessageDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d discourseMessageDo) Preload(fields ...field.RelationField) *discourseMessageDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d discourseMessageDo) FirstOrInit() (*model.DiscourseMessage, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseMessage), nil
	}
}

func (d discourseMessageDo) FirstOrCreate() (*model.DiscourseMessage, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseMessage), nil
	}
}

func (d discourseMessageDo) FindByPage(offset int, limit int) (result []*model.DiscourseMessage, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d discourseMessageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d discourseMessageDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d discourseMessageDo) Delete(models ...*model.DiscourseMessage) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *discourseMessageDo) withDO(do gen.Dao) *discourseMessageDo {
	d.DO = *do.(*gen.DO)
	return d
}
