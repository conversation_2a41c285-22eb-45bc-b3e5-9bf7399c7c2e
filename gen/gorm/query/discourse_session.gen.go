// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiscourseSession(db *gorm.DB, opts ...gen.DOOption) discourseSession {
	_discourseSession := discourseSession{}

	_discourseSession.discourseSessionDo.UseDB(db, opts...)
	_discourseSession.discourseSessionDo.UseModel(&model.DiscourseSession{})

	tableName := _discourseSession.discourseSessionDo.TableName()
	_discourseSession.ALL = field.NewAsterisk(tableName)
	_discourseSession.ID = field.NewInt64(tableName, "id")
	_discourseSession.UserID = field.NewString(tableName, "user_id")
	_discourseSession.SessionID = field.NewString(tableName, "session_id")
	_discourseSession.Title = field.NewString(tableName, "title")
	_discourseSession.IntentionSession = field.NewString(tableName, "intention_session")
	_discourseSession.DiagnoseSession = field.NewString(tableName, "diagnose_session")
	_discourseSession.IsHistory = field.NewBool(tableName, "is_history")
	_discourseSession.StartDate = field.NewTime(tableName, "start_date")
	_discourseSession.SummarySession = field.NewString(tableName, "summary_session")

	_discourseSession.fillFieldMap()

	return _discourseSession
}

// discourseSession 用户对话会话状态表
type discourseSession struct {
	discourseSessionDo discourseSessionDo

	ALL              field.Asterisk
	ID               field.Int64  // 主键ID
	UserID           field.String // 用户id(email)
	SessionID        field.String // 会话
	Title            field.String // 第一条消息，用于历史列表展示（20字）
	IntentionSession field.String // 意图会话
	DiagnoseSession  field.String // 诊断会话
	IsHistory        field.Bool   // 是否关闭
	StartDate        field.Time   // 用户会话开始时间
	SummarySession   field.String // 总结会话 （无状态）

	fieldMap map[string]field.Expr
}

func (d discourseSession) Table(newTableName string) *discourseSession {
	d.discourseSessionDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d discourseSession) As(alias string) *discourseSession {
	d.discourseSessionDo.DO = *(d.discourseSessionDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *discourseSession) updateTableName(table string) *discourseSession {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.UserID = field.NewString(table, "user_id")
	d.SessionID = field.NewString(table, "session_id")
	d.Title = field.NewString(table, "title")
	d.IntentionSession = field.NewString(table, "intention_session")
	d.DiagnoseSession = field.NewString(table, "diagnose_session")
	d.IsHistory = field.NewBool(table, "is_history")
	d.StartDate = field.NewTime(table, "start_date")
	d.SummarySession = field.NewString(table, "summary_session")

	d.fillFieldMap()

	return d
}

func (d *discourseSession) WithContext(ctx context.Context) *discourseSessionDo {
	return d.discourseSessionDo.WithContext(ctx)
}

func (d discourseSession) TableName() string { return d.discourseSessionDo.TableName() }

func (d discourseSession) Alias() string { return d.discourseSessionDo.Alias() }

func (d discourseSession) Columns(cols ...field.Expr) gen.Columns {
	return d.discourseSessionDo.Columns(cols...)
}

func (d *discourseSession) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *discourseSession) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 9)
	d.fieldMap["id"] = d.ID
	d.fieldMap["user_id"] = d.UserID
	d.fieldMap["session_id"] = d.SessionID
	d.fieldMap["title"] = d.Title
	d.fieldMap["intention_session"] = d.IntentionSession
	d.fieldMap["diagnose_session"] = d.DiagnoseSession
	d.fieldMap["is_history"] = d.IsHistory
	d.fieldMap["start_date"] = d.StartDate
	d.fieldMap["summary_session"] = d.SummarySession
}

func (d discourseSession) clone(db *gorm.DB) discourseSession {
	d.discourseSessionDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d discourseSession) replaceDB(db *gorm.DB) discourseSession {
	d.discourseSessionDo.ReplaceDB(db)
	return d
}

type discourseSessionDo struct{ gen.DO }

func (d discourseSessionDo) Debug() *discourseSessionDo {
	return d.withDO(d.DO.Debug())
}

func (d discourseSessionDo) WithContext(ctx context.Context) *discourseSessionDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d discourseSessionDo) ReadDB() *discourseSessionDo {
	return d.Clauses(dbresolver.Read)
}

func (d discourseSessionDo) WriteDB() *discourseSessionDo {
	return d.Clauses(dbresolver.Write)
}

func (d discourseSessionDo) Session(config *gorm.Session) *discourseSessionDo {
	return d.withDO(d.DO.Session(config))
}

func (d discourseSessionDo) Clauses(conds ...clause.Expression) *discourseSessionDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d discourseSessionDo) Returning(value interface{}, columns ...string) *discourseSessionDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d discourseSessionDo) Not(conds ...gen.Condition) *discourseSessionDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d discourseSessionDo) Or(conds ...gen.Condition) *discourseSessionDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d discourseSessionDo) Select(conds ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d discourseSessionDo) Where(conds ...gen.Condition) *discourseSessionDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d discourseSessionDo) Order(conds ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d discourseSessionDo) Distinct(cols ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d discourseSessionDo) Omit(cols ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d discourseSessionDo) Join(table schema.Tabler, on ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d discourseSessionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d discourseSessionDo) RightJoin(table schema.Tabler, on ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d discourseSessionDo) Group(cols ...field.Expr) *discourseSessionDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d discourseSessionDo) Having(conds ...gen.Condition) *discourseSessionDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d discourseSessionDo) Limit(limit int) *discourseSessionDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d discourseSessionDo) Offset(offset int) *discourseSessionDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d discourseSessionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *discourseSessionDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d discourseSessionDo) Unscoped() *discourseSessionDo {
	return d.withDO(d.DO.Unscoped())
}

func (d discourseSessionDo) Create(values ...*model.DiscourseSession) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d discourseSessionDo) CreateInBatches(values []*model.DiscourseSession, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d discourseSessionDo) Save(values ...*model.DiscourseSession) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d discourseSessionDo) First() (*model.DiscourseSession, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseSession), nil
	}
}

func (d discourseSessionDo) Take() (*model.DiscourseSession, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseSession), nil
	}
}

func (d discourseSessionDo) Last() (*model.DiscourseSession, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseSession), nil
	}
}

func (d discourseSessionDo) Find() ([]*model.DiscourseSession, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiscourseSession), err
}

func (d discourseSessionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiscourseSession, err error) {
	buf := make([]*model.DiscourseSession, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d discourseSessionDo) FindInBatches(result *[]*model.DiscourseSession, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d discourseSessionDo) Attrs(attrs ...field.AssignExpr) *discourseSessionDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d discourseSessionDo) Assign(attrs ...field.AssignExpr) *discourseSessionDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d discourseSessionDo) Joins(fields ...field.RelationField) *discourseSessionDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d discourseSessionDo) Preload(fields ...field.RelationField) *discourseSessionDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d discourseSessionDo) FirstOrInit() (*model.DiscourseSession, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseSession), nil
	}
}

func (d discourseSessionDo) FirstOrCreate() (*model.DiscourseSession, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiscourseSession), nil
	}
}

func (d discourseSessionDo) FindByPage(offset int, limit int) (result []*model.DiscourseSession, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d discourseSessionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d discourseSessionDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d discourseSessionDo) Delete(models ...*model.DiscourseSession) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *discourseSessionDo) withDO(do gen.Dao) *discourseSessionDo {
	d.DO = *do.(*gen.DO)
	return d
}
