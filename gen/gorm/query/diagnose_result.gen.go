// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/volcengine-support/cloud-sherlock/gen/gorm/model"
)

func newDiagnoseResult(db *gorm.DB, opts ...gen.DOOption) diagnoseResult {
	_diagnoseResult := diagnoseResult{}

	_diagnoseResult.diagnoseResultDo.UseDB(db, opts...)
	_diagnoseResult.diagnoseResultDo.UseModel(&model.DiagnoseResult{})

	tableName := _diagnoseResult.diagnoseResultDo.TableName()
	_diagnoseResult.ALL = field.NewAsterisk(tableName)
	_diagnoseResult.ID = field.NewInt64(tableName, "id")
	_diagnoseResult.DiagnoseItemID = field.NewInt64(tableName, "diagnose_item_id")
	_diagnoseResult.DiagnoseTaskID = field.NewInt64(tableName, "diagnose_task_id")
	_diagnoseResult.InstanceID = field.NewString(tableName, "instance_id")
	_diagnoseResult.InstanceName = field.NewString(tableName, "instance_name")
	_diagnoseResult.Region = field.NewString(tableName, "region")
	_diagnoseResult.DiagnoseMessage = field.NewString(tableName, "diagnose_message")
	_diagnoseResult.DiagnoseResultLevel = field.NewString(tableName, "diagnose_result_level")
	_diagnoseResult.DiagnoseSuggestion = field.NewString(tableName, "diagnose_suggestion")
	_diagnoseResult.DiagnoseOperate = field.NewString(tableName, "diagnose_operate")
	_diagnoseResult.Status = field.NewString(tableName, "status")
	_diagnoseResult.ErrorInfo = field.NewString(tableName, "error_info")
	_diagnoseResult.DiagnoseTaskRunID = field.NewInt64(tableName, "diagnose_task_run_id")
	_diagnoseResult.DiagnoseResultLevelNum = field.NewInt32(tableName, "diagnose_result_level_num")

	_diagnoseResult.fillFieldMap()

	return _diagnoseResult
}

// diagnoseResult 诊断结果表
type diagnoseResult struct {
	diagnoseResultDo diagnoseResultDo

	ALL                    field.Asterisk
	ID                     field.Int64  // 自增ID
	DiagnoseItemID         field.Int64  // 诊断项ID
	DiagnoseTaskID         field.Int64  // 诊断任务ID
	InstanceID             field.String // 实例ID
	InstanceName           field.String // 实例名称
	Region                 field.String // 实例区域
	DiagnoseMessage        field.String // 诊断信息
	DiagnoseResultLevel    field.String // 诊断结果
	DiagnoseSuggestion     field.String // 诊断建议
	DiagnoseOperate        field.String // 诊断操作
	Status                 field.String // 诊断状态(Succeed,Running,Failed)
	ErrorInfo              field.String // 诊断错误信息
	DiagnoseTaskRunID      field.Int64  // 关联任务运行ID
	DiagnoseResultLevelNum field.Int32  // 结果等级num

	fieldMap map[string]field.Expr
}

func (d diagnoseResult) Table(newTableName string) *diagnoseResult {
	d.diagnoseResultDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d diagnoseResult) As(alias string) *diagnoseResult {
	d.diagnoseResultDo.DO = *(d.diagnoseResultDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *diagnoseResult) updateTableName(table string) *diagnoseResult {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.DiagnoseItemID = field.NewInt64(table, "diagnose_item_id")
	d.DiagnoseTaskID = field.NewInt64(table, "diagnose_task_id")
	d.InstanceID = field.NewString(table, "instance_id")
	d.InstanceName = field.NewString(table, "instance_name")
	d.Region = field.NewString(table, "region")
	d.DiagnoseMessage = field.NewString(table, "diagnose_message")
	d.DiagnoseResultLevel = field.NewString(table, "diagnose_result_level")
	d.DiagnoseSuggestion = field.NewString(table, "diagnose_suggestion")
	d.DiagnoseOperate = field.NewString(table, "diagnose_operate")
	d.Status = field.NewString(table, "status")
	d.ErrorInfo = field.NewString(table, "error_info")
	d.DiagnoseTaskRunID = field.NewInt64(table, "diagnose_task_run_id")
	d.DiagnoseResultLevelNum = field.NewInt32(table, "diagnose_result_level_num")

	d.fillFieldMap()

	return d
}

func (d *diagnoseResult) WithContext(ctx context.Context) *diagnoseResultDo {
	return d.diagnoseResultDo.WithContext(ctx)
}

func (d diagnoseResult) TableName() string { return d.diagnoseResultDo.TableName() }

func (d diagnoseResult) Alias() string { return d.diagnoseResultDo.Alias() }

func (d diagnoseResult) Columns(cols ...field.Expr) gen.Columns {
	return d.diagnoseResultDo.Columns(cols...)
}

func (d *diagnoseResult) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *diagnoseResult) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 14)
	d.fieldMap["id"] = d.ID
	d.fieldMap["diagnose_item_id"] = d.DiagnoseItemID
	d.fieldMap["diagnose_task_id"] = d.DiagnoseTaskID
	d.fieldMap["instance_id"] = d.InstanceID
	d.fieldMap["instance_name"] = d.InstanceName
	d.fieldMap["region"] = d.Region
	d.fieldMap["diagnose_message"] = d.DiagnoseMessage
	d.fieldMap["diagnose_result_level"] = d.DiagnoseResultLevel
	d.fieldMap["diagnose_suggestion"] = d.DiagnoseSuggestion
	d.fieldMap["diagnose_operate"] = d.DiagnoseOperate
	d.fieldMap["status"] = d.Status
	d.fieldMap["error_info"] = d.ErrorInfo
	d.fieldMap["diagnose_task_run_id"] = d.DiagnoseTaskRunID
	d.fieldMap["diagnose_result_level_num"] = d.DiagnoseResultLevelNum
}

func (d diagnoseResult) clone(db *gorm.DB) diagnoseResult {
	d.diagnoseResultDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d diagnoseResult) replaceDB(db *gorm.DB) diagnoseResult {
	d.diagnoseResultDo.ReplaceDB(db)
	return d
}

type diagnoseResultDo struct{ gen.DO }

func (d diagnoseResultDo) Debug() *diagnoseResultDo {
	return d.withDO(d.DO.Debug())
}

func (d diagnoseResultDo) WithContext(ctx context.Context) *diagnoseResultDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d diagnoseResultDo) ReadDB() *diagnoseResultDo {
	return d.Clauses(dbresolver.Read)
}

func (d diagnoseResultDo) WriteDB() *diagnoseResultDo {
	return d.Clauses(dbresolver.Write)
}

func (d diagnoseResultDo) Session(config *gorm.Session) *diagnoseResultDo {
	return d.withDO(d.DO.Session(config))
}

func (d diagnoseResultDo) Clauses(conds ...clause.Expression) *diagnoseResultDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d diagnoseResultDo) Returning(value interface{}, columns ...string) *diagnoseResultDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d diagnoseResultDo) Not(conds ...gen.Condition) *diagnoseResultDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d diagnoseResultDo) Or(conds ...gen.Condition) *diagnoseResultDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d diagnoseResultDo) Select(conds ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d diagnoseResultDo) Where(conds ...gen.Condition) *diagnoseResultDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d diagnoseResultDo) Order(conds ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d diagnoseResultDo) Distinct(cols ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d diagnoseResultDo) Omit(cols ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d diagnoseResultDo) Join(table schema.Tabler, on ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d diagnoseResultDo) LeftJoin(table schema.Tabler, on ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d diagnoseResultDo) RightJoin(table schema.Tabler, on ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d diagnoseResultDo) Group(cols ...field.Expr) *diagnoseResultDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d diagnoseResultDo) Having(conds ...gen.Condition) *diagnoseResultDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d diagnoseResultDo) Limit(limit int) *diagnoseResultDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d diagnoseResultDo) Offset(offset int) *diagnoseResultDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d diagnoseResultDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *diagnoseResultDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d diagnoseResultDo) Unscoped() *diagnoseResultDo {
	return d.withDO(d.DO.Unscoped())
}

func (d diagnoseResultDo) Create(values ...*model.DiagnoseResult) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d diagnoseResultDo) CreateInBatches(values []*model.DiagnoseResult, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d diagnoseResultDo) Save(values ...*model.DiagnoseResult) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d diagnoseResultDo) First() (*model.DiagnoseResult, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResult), nil
	}
}

func (d diagnoseResultDo) Take() (*model.DiagnoseResult, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResult), nil
	}
}

func (d diagnoseResultDo) Last() (*model.DiagnoseResult, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResult), nil
	}
}

func (d diagnoseResultDo) Find() ([]*model.DiagnoseResult, error) {
	result, err := d.DO.Find()
	return result.([]*model.DiagnoseResult), err
}

func (d diagnoseResultDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.DiagnoseResult, err error) {
	buf := make([]*model.DiagnoseResult, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d diagnoseResultDo) FindInBatches(result *[]*model.DiagnoseResult, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d diagnoseResultDo) Attrs(attrs ...field.AssignExpr) *diagnoseResultDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d diagnoseResultDo) Assign(attrs ...field.AssignExpr) *diagnoseResultDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d diagnoseResultDo) Joins(fields ...field.RelationField) *diagnoseResultDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d diagnoseResultDo) Preload(fields ...field.RelationField) *diagnoseResultDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d diagnoseResultDo) FirstOrInit() (*model.DiagnoseResult, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResult), nil
	}
}

func (d diagnoseResultDo) FirstOrCreate() (*model.DiagnoseResult, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.DiagnoseResult), nil
	}
}

func (d diagnoseResultDo) FindByPage(offset int, limit int) (result []*model.DiagnoseResult, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d diagnoseResultDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d diagnoseResultDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d diagnoseResultDo) Delete(models ...*model.DiagnoseResult) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *diagnoseResultDo) withDO(do gen.Dao) *diagnoseResultDo {
	d.DO = *do.(*gen.DO)
	return d
}
