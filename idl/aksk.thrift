include "base.thrift"
include "common.thrift"

struct CreateAppAkSkReq {
    1: required string AppID,
    2: optional string AppName,
    3: optional string OutDateTime,
    254: required string Action (api.query = "Action", api.const = "CreateAppAkSk"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct CreateAppAkSkResp {
    1: required CreateAppAkSkResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct CreateAppAkSkResult {
    1: required string AppKey,
    2: required string AppSecret,
}

struct DeleteAppAkSkReq {
    1: required string AppID,
    2: optional string AppName,
    254: required string Action (api.query = "Action", api.const = "DeleteAppAkSk"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct DeleteAppAkSkResp {
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryAppAkSkListReq {
    1: optional string AppID,
    2: optional string AppName,
    3: optional string OutDateTime,
    4: optional string AppKey,
    5: optional i32 PageNumber,
    6: optional i32 PageSize,
    254: required string Action (api.query = "Action", api.const = "QueryAppAkSkList"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryAppAkSkListResp {
    1: required QueryAppAkSkListResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryAppAkSkListResult {
    1: required list<AppAkSk> AppAkSkList,
    2: optional common.Pagination Pagination,
}
struct AppAkSk {
    1: required string AppID,
    2: optional string AppName,
    3: required string AppKey,
    4: required string AppSecret,
    5: optional string OutDateTime,
}

struct UpdateAppAkSkReq {
    1: required string AppID,
    2: optional string AppName,
    3: optional string OutDateTime,
    254: required string Action (api.query = "Action", api.const = "UpdateAppAkSk"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct UpdateAppAkSkResp {
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryAppAkSkReq {
    1: required string AppID,
    254: required string Action (api.query = "Action", api.const = "QueryAppAkSk"),
}
struct QueryAppAkSkResp {
    1: required QueryAppAkSkResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryAppAkSkResult {
    1: required AppAkSk AppAkSk,
}
