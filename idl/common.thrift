include "base.thrift"
/*
##参照火山通用API设计规范
若需要支持查看总页数、总数量并可向指定页码跳转,则建议使用如下参数:
请求参数:
   PageNumber:翻页页码,整数,起始值为1,默认值为1;
   PageSize:翻页每页的大小,整数,默认值为10,最大值为100;
响应参数:
   PageSize:原样返回请求参数值(请求参数未填写时则为参数默认
值);
   PageNumber:原样返回请求参数(请求参数未填写时则为为参数默认
值);
   TotalCount:查询结果的总数;
 */

struct Pagination{
	1: required i32         PageNumber   //分页页码
	2: required i32         PageSize     //分页的每页数量
	3: required i64         TotalCount   //总共数量
}

struct Order{
	1: required string FieldName   // 排序字段
	2: optional string Order   // DESC / ASC
}

struct StatItem{
	1: optional string StatName // 统计名称
	2: optional i64 Count // 数量
}

struct StatResult{
    1: optional i64 TotalCount // 总数量
    2: optional double MonthOnMonthRatio // 30天环比
    3: optional i64 MonthOnMonthIncreaseCount // 30天新增
    4: optional list<StatItem>  StatList // 波动曲线列表
}

struct EnumItem{
    1:optional string Label
    2:optional string Value
    3:optional bool IsHidden
    4:optional Extra Extra
    5:optional list<EnumItem> Children
}

struct Extra{
    1:optional string Type
    2:optional string Color
}

struct GetCommonMapConfigReq{
    1: required string Key
	254: required string Action   (api.query = "Action",api.const = "GetCommonMapConfig")
	255: optional string Version  (api.query = "Version",api.const = "2024-12-01")
}

struct GetCommonMapConfigResp{
	1:   optional map<string,string> Result
	255: optional base.ResponseMetadata                         ResponseMetadata
}