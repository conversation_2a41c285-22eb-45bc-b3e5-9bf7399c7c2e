include "base.thrift"


struct ListCustomerVolcAccountRequest  {
    1:optional string VolcAccountIDFuzz,
    2:optional list<string> CustomerNumber,
    3:optional list<string> VolcAccountID,
    4:optional i32 PageNumber,
    5:optional i32 PageSize,
    254: required string Action             (api.query = "Action",api.const = "ListCustomerVolcAccount")
    255: optional string Version            (api.query = "Version",api.const = "")
}

struct ListCustomerVolcAccountResponse {
    1: optional list<ListCustomerVolcAccountResult> Result
    2: optional base.ResponseMetadata ResponseMetadata
}

struct ListCustomerVolcAccountResult{
    1:optional string VolcAccountID,
    2:optional string VolcAccountName,
    3:optional string CustomerName,
    4:optional string CustomerNumber,
}

