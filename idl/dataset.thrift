typedef string QueryOp
const QueryOp Equal         = "equal"
const QueryOp Gte           = "gte"
const QueryOp Gt            = "gt"
const QueryOp Lte           = "lte"
const QueryOp Lt            = "lt"
const QueryOp Between       = "between"
const QueryOp NotEqual      = "not_equal"
const QueryOp NotNull       = "not_null"
const QueryOp IsNull        = "is_null"
const QueryOp Like          = "like"
const QueryOp And           = "and"
const QueryOp Or            = "or"
const QueryOp ArrayHasAny   = "array_has_any"
const QueryOp ArrayNotEmpty = "array_not_empty"
const QueryOp ArrayHasAll   = "array_has_all"

// 查询过滤条件
struct DataQueryQuery {
    1:  string                Field     // 字段名
    2:  QueryOp               Op        // 查询类型
    3:  list<string>          Val       // 查询值
    4:  string                Expr      // 原生SQL表达式
    5:  list<DataQueryQuery>  Children  // 当 QueryOp 为 And 或 Or 时的子查询条件
}

typedef string SortOrder
const SortOrder Asc  = "ASC"
const SortOrder Desc = "DESC"

struct DateQueryOrder {
    1:  string     Field  // 字段名
    2:  SortOrder  Order  // 顺序
}

struct DataQueryOptions {
    1:  i32  WeekOffset  // 周偏移值
}

struct DataQueryLod {
    1:  list<string>          Fields        // 展现字段
    2:  list<string>          Aggregations  // 聚合字段
    3:  list<DataQueryQuery>  Queries       // 筛选条件
}

typedef string DisplayType
const DisplayType Simple = "simple"
const DisplayType Sankey = "sankey"

struct DataQueryDisplay {
    1:  DisplayType  Type  // 展示类型
}

typedef string ComplexFieldOp
const ComplexFieldOp ComplexFieldOpSimple     = "simple"
const ComplexFieldOp ComplexFieldOpSum        = "sum"
const ComplexFieldOp ComplexFieldOpAvg        = "avg"
const ComplexFieldOp ComplexFieldOpMax        = "max"
const ComplexFieldOp ComplexFieldOpMin        = "min"
const ComplexFieldOp ComplexFieldOpCount      = "count"
const ComplexFieldOp ComplexFieldOpDcount     = "dcount"
const ComplexFieldOp ComplexFieldOpCollectSet = "collect_set"

struct ComplexField{
    1:  string  Field
    2:  string  Op
    3:  string  Alias
}

struct DataQueryPeriodCompare {
    1:  string                          TimeField        // 环比时间字段，当前只支持int类型
    2:  i32                             TimeFieldType    // 环比时间字段类型 0-毫秒 1-秒
    3:  list<string>                    CompareFields    // 环比计算的字段，只能数字类型
    4:  DataQueryPeriodCompareFunction  CompareFunction  // 字段展示
    5:  string                          Period           // 对比周期 支持y年 m月 w周 d天
}

struct DataQueryPeriodCompareFunction  {
    1:  bool  Ratio     // 原始值
    2:  bool  Diff      // 差异值
    3:  bool  Original  // 环比
}

struct DataQueryReq {
     1:  string                        Dataset              // 数据集名
     2:  list<DataQueryQuery>          Queries              // 筛选条件
     3:  list<string>                  Aggregations         // 聚合字段
     4:  string                        AggTimeCol           // 时间聚合字段
     5:  list<string>                  Fields               // 展现字段
     6:  list<DateQueryOrder>          Orders               // 排序
     7:  i32                           Limit          = 50  // 返回数量
     8:  i32                           Offset               // 偏移值
    12:  list<ComplexField>            ComplexFields        // 复杂字段
    13:  list<DataQueryPeriodCompare>  PeriodCompares       // 环比设置
    16:  list<DataQueryQuery>          Having               // 聚合后筛选
}

struct DataQueryResult {
    1:  string  Data   // 数据
    2:  i64     Total  // 总数
}