include "common.thrift"
include "base.thrift"


// 请求参数
struct DescribeInstanceInput {
	1: required string Dimension,
	2: required i64 AccountId,
	3: optional Resources Resources,
	4: required string Region,
	5: optional i32 PageSize,
	6: optional i32 PageNumber
}

// 返回参数
struct DescribeInstanceResponse {
	1: optional DescribeInstanceResult DescribeInstanceResult,
    2: optional base.ResponseMetadata ResponseMetadata,
}

struct DescribeInstanceResult{
   1: optional string Type,
   2: optional common.Pagination Pagination
   3: optional list<Instance> Instances
}

struct Instance {
	1: optional string InstanceName,
	2: optional string Id,
	3: optional string Status,
	4: optional string ImageId,
	5: optional string InstanceType,
	6: optional string Region,
}

struct Resources {
	1: required string Type,
	2: required string Product,
    3: required string SubProduct,
	4: optional list<string> Instances
}