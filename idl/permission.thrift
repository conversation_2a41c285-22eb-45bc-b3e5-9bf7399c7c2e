include "base.thrift"
struct ListPermissionRolesReq{
	254: required string        Action             (api.query = "Action",api.const = "ListPermissionRoles")
    255: optional string        Version            (api.query = "Version",api.const = "2024-08-01")
}

struct ListPermissionRolesResp{
	1:   required PermissionRolesResult Result
	255: optional base.ResponseMetadata ResponseMetadata
}

struct PermissionRolesResult{
	1: required list<string> RoleList
}




struct GetUserPermissionReq{
	254: required string        Action             (api.query = "Action",api.const = "GetUserPermission")
    255: optional string        Version            (api.query = "Version",api.const = "2024-08-01")
}

struct GetUserPermissionResp{
	1:   required list<UserPermission>  Result
	255: optional base.ResponseMetadata ResponseMetadata
}

struct UserPermission{
	1: required string       Resource
	2: required list<string> ActionList
}





struct CheckUserPermissionReq{
    1: required list<Rule> Rules // 必填

	254: required string        Action             (api.query = "Action",api.const = "CheckUserPermission")
    255: optional string        Version            (api.query = "Version",api.const = "2024-08-01")
}

struct Rule {
    1: required string ResourceKey
    2: required string ActionKey
}

struct CheckUserPermissionResp{
	1:   required CheckUserPermissionResult  Result
	255: optional base.ResponseMetadata ResponseMetadata
}


struct CheckUserPermissionResult{
	1:required list<CheckUserPermissionItem> List
}

struct CheckUserPermissionItem{
    1: required string ResourceKey // 资源key
	2: required map<string, bool> ActionAccessMap //操作Action对应的是否有权限：true-有，false-无
}


struct MenuItem{
    1:optional string MenuKey   // 菜单key
    2:optional string MenuName  // 菜单名称
    3:optional bool IsHidden // 是否隐藏
    4:optional list<Permission> OperablePermissionList // 可操作权限列表
    5:optional list<Permission> ViewablePermissionList // 可查看权限列表
    6:optional list<MenuItem> Children  // 子菜单列表
}

struct Permission{
	1: optional string ResourceKey   // 权限资源key
	2: optional string ActionKey     // 权限操作key
}

struct GetMenuPermissionConfigReq{
	254: required string Action   (api.query = "Action",api.const = "GetMenuPermissionConfig")
	255: optional string Version  (api.query = "Version",api.const = "2024-10-01")
}

struct GetMenuPermissionConfigResp{
	1:   optional GetMenuPermissionConfigResult Result
	255: optional base.ResponseMetadata   ResponseMetadata
}

struct GetMenuPermissionConfigResult{
	1: optional list<MenuItem> List
}

struct GetResourcesReq{
    1: required list<string> Resources
    254: required string Action   (api.query = "Action",api.const = "GetPermissionResource")
    255: optional string Version  (api.query = "Version",api.const = "2024-10-01")
}

struct GetResourcesResp{
    1: required string Key
    2: required string Name
    3: required string Description
    4: required list<string> ActionKeys
}