include "common.thrift"
include "base.thrift"


// 请求参数
struct DescribeResourceRequest {
	1: required string Dimension,
	2: optional string AccountId,
	3: required Resources Resources,
	4: optional string Region,
	5: optional i32 PageSize,
	6: optional i32 PageNumber
}

struct Resources {
	1: required string ResourceType,
    2: required string Product,
    3: required string SubProduct,
	4: required list<string> Instances
}

struct DescribeResourceResponse {
	1: required string ResourceType,
	2: required list<ResourceMetadataEntry> ResourceMetadata,
	3: optional list<ResourceNetworkEntry> ResourceNetworkInfo,
	4: CustomerInfo CustomerInfo,	
	6: required  string OriginMsg,
	7: optional string GrafanaLink,

	255: optional base.ResponseMetadata ResponseMetadata,
}



struct ResourceMetadataEntry {
	1: required string Key,
	2: required string Name,
	3: optional string GrafanaLink
} 


struct ResourceNetworkEntry {
	1: required string Key,
	2: required string Name
}

struct CustomerInfo {
	1: optional string AccountId,
	2: optional string CustomerName
}

struct DescribeResourceDependencyRequest {
	1: required string Dimension,
	2: optional string AccountId,
	3: required Resources Resources,
	4: optional string Region,
	5: optional i32 PageSize,
	6: optional i32 PageNumber
}
