include "base.thrift"
include "common.thrift"



struct GetCustomerInfoReq{
	1:   optional string CustomerNumber                                                       // 客户编号
	2:   optional string VolcAccountID                                                        // 火山账号ID
	254: required string Action          (api.query = "Action",api.const = "GetCustomerInfo")
	255: optional string Version         (api.query = "Version",api.const = "2024-08-01")
}

struct GetCustomerInfoResp{
	1:   optional GetCustomerInfoResult Result
	255: optional base.ResponseMetadata ResponseMetadata
}

struct CustomerSimpleInfo{
	1: required string CustomerName     // 客户名称
	2: required string CustomerNumber   // 客户编号
	3: required string ShortName        // 客户简称
	4: required string CustomerTier     // 客户等级
}

struct ListCustomerSimpleInfoReq{
	1:   optional string       SearchKeyword                                                               // 搜索词
	2:   optional list<string> CustomerNumber                                                              // 客户编号列表
	3:   optional i32          PageNumber                                                                  //    页码
	4:   optional i32          PageSize                                                                    //    页面大小
	254: required string       Action          (api.query = "Action",api.const = "ListCustomerSimpleInfo")
	255: optional string       Version         (api.query = "Version",api.const = "")
}

struct ListCustomerSimpleInfoResp{
	1:   optional list<CustomerSimpleInfo> Result
	255: optional base.ResponseMetadata    ResponseMetadata
}

struct GetCustomerInfoResult{
	1:  optional string                       CustomerName           // 客户名称
	2:  optional string                       CustomerNumber         // 客户编号
	3:  optional string                       ShortName              // 客户简称
	4:  optional string                       Province               // 客户省份编号
	5:  optional string                       ProvinceName           // 客户省份名称
	6:  optional string                       Industry               // 客户行业英文名
	7:  optional string                       IndustryName           // 客户行业中文名
	8:  optional string                       SubIndustry            // 客户二级行业英文名
	9:  optional string                       SubIndustryName        // 客户二级行业中文名
	10: optional string                       CustomerLevel          // 客户层级
	11: optional string                       CustomerLevelName      // 客户层级中文名
	12: optional string                       CustomerPLevel         // 客户P级
	13: optional string                       CustomerPLevelName     // 客户P级中文名
	14: optional string                       CustomerTier           // 客户等级
	15: optional string                       CustomerTierName       // 客户等级中文名
	16: optional string                       OwnerEmail             // 客户所有人邮箱
	17: optional string                       OwnerName              // 客户所有人名称
	19: optional string                       Region                 // 客户所在区域En
	20: optional string                       RegionName             // 客户所在区域Zh
	21: optional i64                          ProjectTotalCount      // 项目数量
	22: optional list<string>                 CSMList                // CSM email列表
	23: optional list<string>                 VolcanoAccountIDList   // 客户关联火山账号ID列表
	24: optional string                       GuaranteeStatus        // 客户重保状态
	25: optional string       CustomerSfID           // 客户SFID
}

// 获取客户服务团队信息接口
struct GetCustomerServiceTeamReq{
	1:   optional string CustomerNumber                                                              // 客户编号
	254: required string Action          (api.query = "Action",api.const = "GetCustomerServiceTeam")
	255: optional string Version         (api.query = "Version",api.const = "2024-08-01")
}

struct GetCustomerServiceTeamResp{
	1:   optional list<CustomerServiceInfo> result
	255: optional base.ResponseMetadata     ResponseMetadata
}

struct CustomerServiceInfo{
	1: optional string       RoleName        // 角色名称
	2: optional i32          Role            // 角色ID：1-客户服务负责人 2-客户服务
//	3: optional string       ActualRoleName
	4: optional list<string> UserEmailList   // 用户邮箱列表
}

struct ListCustomerVolcAccountReq{
	1:   optional string VolcAccountIDFuzz                                                               // 模糊搜索
	2:   optional string CustomerNumber                                                                  // 客户编号
	254: required string Action             (api.query = "Action",api.const = "ListCustomerVolcAccount")
	255: optional string Version            (api.query = "Version",api.const = "")
}

struct CustomerVolcAccount{
	1: optional string VolcAccountID
	2: optional string VolcAccountName
	3: optional string CustomerName
	4: optional string CustomerNumber
}

struct ListCustomerVolcAccountResp{
	1:   optional list<CustomerVolcAccount> Result
	255: optional base.ResponseMetadata     ResponseMetadata
}

struct SyncCustomerReq{
	1:   optional list<string> CustomerNumberList
	254: required string       Action              (api.query = "Action",api.const = "SyncCustomer")
	255: optional string       Version             (api.query = "Version",api.const = "2024-08-01")
}

struct SyncCustomerResp{
	1:   optional SyncCustomerResult    Result
	255: optional base.ResponseMetadata ResponseMetadata
}

struct SyncCustomerResult{
	1: optional list<string> SyncedCustomerNumberList   //已同步的客户编号
}
