include "base.thrift"
include "common.thrift"
include "product_category.thrift"

struct UserInfo{
    1: required string UserID
    2: required string UserName
}

struct RegisterDiagnoseTemplateRequest {
    1: required string DiagnoseTemplateName,
    2: required UserInfo UserInfo,
    3: required string TemplateDescription,
    4: required string Suggestion,
    5: optional list<Input> Inputs,
    6: required  string StateMachineInfo,
    7: required string Status,
    8: required string ResponsiblePerson,
    9: required list<i64> DiagnosesItemIDs,
    10: optional string Product,
    11: optional string SubProduct,
    12: optional string ResourceType,
    13: required i64 ShowLevel,
    14: required i64 ProductCategoryID,
    254: required string Action  (api.query = "Action",api.const = "RegisterDiagnoseTemplate")
    255: required string Version (api.query = "Version",api.const = ""),
}

struct Input {
    1: optional string Key,
    2: optional string Type,
    3: optional string DefaultValue,
    4: optional bool IsRequired,
    5: optional string InputDescription,
}


struct RegisterDiagnoseTemplateResponse {
     1: optional RegisterDiagnoseTemplateResult RegisterDiagnoseTemplateResult,
     2: optional base.ResponseMetadata ResponseMetadata,
}
struct RegisterDiagnoseTemplateResult{
    1: optional i64 DiagnoseTemplateID,
}

struct DeleteDiagnoseTemplateRequest {
    1: optional i64 DiagnoseTemplateID,
    2: optional UserInfo UserInfo,
    254: required string Action  (api.query = "Action",api.const = "DeleteDiagnoseTemplate")
    255: optional string Version (api.query = "Version",api.const = ""),
}

struct DeleteDiagnoseTemplateResponse {
     1: optional DeleteDiagnoseTemplateResult DeleteDiagnoseTemplateResult,
     2: optional base.ResponseMetadata ResponseMetadata,
}

struct DeleteDiagnoseTemplateResult{
    1: optional i64 DiagnoseTemplateID,
}

struct ListDiagnoseTemplateRequest{
    1: optional UserInfo UserInfo,
    2: optional list<i64> DiagnoseTemplateIDs,
    3: optional i64 PageNumber,
    4: optional i64 PageSize,
    5: optional string DiagnoseTemplateName,
    6: optional string Status,
    7: optional string ResponsiblePerson,
    8: optional list<i32> ProductCategoryIDs
    254: required string Action             (api.query = "Action",api.const = "ListDiagnoseTemplate")
    255: required string Version            (api.query = "Version",api.const = "")
}
struct ListDiagnoseTemplateResponse {
     1: optional ListDiagnoseTemplateResult ListDiagnoseTemplateResult,
     2: optional base.ResponseMetadata ResponseMetadata,
}

struct ListDiagnoseTemplateResult{
    1: optional common.Pagination Pagination
    2: optional list<DiagnoseTemplate> DiagnoseTemplates
}

struct DiagnoseTemplate {
    1: optional i64 DiagnoseTemplateID  //诊断模版ID
    2: optional string DiagnoseTemplateName //诊断模版名称
    3: optional list<i64> DiagnosesItemIDs
    4: optional list<i64> DiagnoseTaskIDs //绑定的诊断任务ID
    5: optional string UpdateTime //更新时间
    6: optional UserInfo Updater //更新人ID
    7: optional string CreateTime //创建时间
    8: optional UserInfo Creator //创建人ID
    9: optional string TemplateDescription,
    10: optional string Suggestion,
    11: optional list<i64> DiagnoseItemIDs,
    12: optional  string StateMachineInfo,//状态机信息
    13: optional string Status,
    14: optional string ResponsiblePerson,
    15: optional string Product,
    16: optional string SubProduct,
    17: optional string ResourceType,
    18: optional i32 ShowLevel,
    19: optional list<Input> Inputs,
    20: required product_category.ProductCategory ProductCategory
}

struct UpdateDiagnoseTemplateRequest{
    1: required UserInfo UserInfo
    2: optional i64 DiagnoseTemplateID
    3: optional string DiagnoseTemplateName
    4: optional string TemplateDescription
    5: optional string suggestion
    6: optional list<i64> DiagnosesItemIDs,
    7: optional  string StateMachineInfo
    8: optional string Status
    254: required string Action             (api.query = "Action",api.const = "UpdatetDiagnoseTemplate")
    255: optional string Version            (api.query = "Version",api.const = "")
}

struct UpdateDiagnoseTemplateResponse{
     1: optional UpdateDiagnoseTemplateResult UpdateDiagnoseTemplateResult,
     2: optional base.ResponseMetadata ResponseMetadata,
}

struct UpdateDiagnoseTemplateResult{
    1: optional i64 DiagnoseTemplateID
}