include "base.thrift"
include "common.thrift"


struct SubmitDiagnoseTaskFeedbackReq {
    1: optional i64 DiagnoseTaskID   // 任务ID
    2: required i64 DiagnoseTaskRunID // 任务运行ID
    3: required bool Resolved           // 是否解决
    4:optional list<ProblemItem> ProblemItems // 问题项
    5: optional string Description         // 问题描述
    6: optional string FeedbackUserID      // 评价人ID
    254: required string Action         (api.query = "Action",api.const = "SubmitDiagnoseTaskFeedback")
    255: optional string Version        (api.query = "Version",api.const = "2024-12-01")
}

struct ProblemItem{
    1: required string ProblemCode
    2: required string ProblemName
}
struct SubmitDiagnoseTaskFeedbackResp{
	255: optional base.ResponseMetadata ResponseMetadata
}

