include "base.thrift"
include "common.thrift"
include 'product_category.thrift'

struct UserInfo{
    1: required string UserID
    2: required string UserName
}

struct RegisterDiagnoseItemRequest {
    1: required UserInfo UserInfo   //发起人
    2: required string DiagnoseItemName //诊断项名称
    3: required string DiagnoseItemCode //诊断项编码
    4: required string Status //诊断项状态
    5: required string Product //诊断项产品分类
    6: required string Subproduct //诊断项子产品分类
    7: required string AccessType //接入类型
    8: required string AccessResponsiblePerson //接入负责人
    9: required string AccessPath //接入路径
    10: required string InterAction //
    11: required string InterVersion //接口版本
    12: required string Suggestion //处理建议
    13: required string SuggestionLink //处理建议的链接
    14: required i32 Timeout //超时时间
    15: required map<string,string> InterParams //接口参数
    16:required string Activity
    17:required string ResourceType
    18:required i64 ProductCategoryID
    19:optional string DiagnoseItemDescription //诊断项描述
    254: required string Action             (api.query = "Action",api.const = "RegisterDiagnoseItme")
    255: required string Version            (api.query = "Version",api.const = "")
}
struct RegisterDiagnoseItemResponse {
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional RegisterDiagnoseItemResult Result
}
struct RegisterDiagnoseItemResult{
    1: optional i64 DiagnoseItemID  //诊断项ID
}


struct ListDiagnoseItemRequest {
    1: required UserInfo UserInfo   //发起人
    2: required i64 PageNumber //页码
    3: required i64 PageSize //一页的数量
    4: optional list<i64> DiagnoseItemIDs //诊断项ID
    5: optional string DiagnoseItemName //诊断项名称
    6: optional string Status //状态
    7: optional list<i64> ProductCategoryIDs //
    10: optional string AccessResponsiblePerson //接入负责人
    254: required string Action             (api.query = "Action",api.const = "ListDiagnoseItme")
    255: optional string Version            (api.query = "Version",api.const = "")
}
struct ListDiagnoseItemResponse {
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional ListDiagnoseItemResult Result
}
struct ListDiagnoseItemResult{
    1: optional common.Pagination Pagination
    2: optional list<DiagnoseItem> DiagnoseItems
}
struct DiagnoseItem{
    1: optional i64 DiagnoseItemID  //诊断项ID
    2: optional string DiagnoseItemName //诊断项名称
    3: optional string CreateTime //创建时间
    4: optional UserInfo Creator //创建人
    5: optional string UpdateTime //更新时间
    6: optional UserInfo Updater //更新人ID
    7: optional string DiagnoseItemCode //诊断项编码
    8: optional string Status //状态
    9: optional string Product //产品分类
    10: optional string Subproduct //子产品分类
    11: optional string AccessType //接入类型
    12: optional string AccessResponsiblePerson //接入负责人
    13: optional string AccessPath //接入路径
    14: optional string InterAction //接口
    15: optional string InterVersion //接口版本
    16: optional string Suggestion //处理建议
    17: optional string SuggestionLink //处理建议的链接
   18: optional i32 Timeout // 超时时间
    19: optional list<i64> DiagnoseTemplateIDs
    20: optional map<string,string> InterParams
    21: optional list<i64> DiagnoseTaskIDs
    22:optional string ResourceType
    23:optional string Activity
    24:optional i64 ProductCategoryID
    25:optional string DiagnoseItemDescription //诊断项描述
    26:required product_category.ProductCategory ProductCategory
}


struct UpdateDiagnoseItemRequest {
    1: optional UserInfo UserInfo
    2: optional i64 DiagnoseItemID //诊断项ID
    3: optional string DiagnoseItemName //诊断项名称
    4: optional string DiagnoseItemCode //诊断项编码
    5: optional string Status //状态
    6: optional string Product //产品分类
    7: optional string Subproduct //子产品分类
    8: optional string AccessType //接入类型
    9: optional string AccessResponsiblePerson //接入负责人
    10: optional string AccessPath //接入路径
    11: optional string InterAction //接口
    12: optional string InterVersion //接口版本
    13: optional string Suggestion //处理建议
    14: optional string SuggestionLink //处理建议的链接
    15: optional i32 Timeout //超时时间
    16: optional string DiagnoseItemDescription //诊断项描述
    17: optional string Activity
    254: required string Action             (api.query = "Action",api.const = "UpdateDiagnoseItme")
    255: optional string Version            (api.query = "Version",api.const = "")
}
struct UpdateDiagnoseItemResponse {
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional UpdateDiagnoseItemResult Result
}
struct UpdateDiagnoseItemResult{
    1: optional string IsSuccess //是否成功
}


struct DeleteDiagnoseItemRequest {
    1: optional UserInfo UserInfo //发起人
    2: optional i64 DiagnoseItemID //诊断项ID
    254: required string Action             (api.query = "Action",api.const = "DeleteDiagnoseItme")
    255: optional string Version            (api.query = "Version",api.const = "")
}
struct DeleteDiagnoseItemResponse {
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional DeleteDiagnoseItemResult Result
}
struct DeleteDiagnoseItemResult{
    1: optional string IsSuccess //是否成功
}

struct GetAllDiagnoseItemWithProductRequest{
    1: optional UserInfo UserInfo
    254: required string Action             (api.query = "Action",api.const = " GetAllDiagnoseItemWithProduct")
    255: optional string Version            (api.query = "Version",api.const = "")
}

struct GetAllDiagnoseItemWithProductResponse{
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional GetAllDiagnoseItemWithProductResult Result
}
struct GetAllDiagnoseItemWithProductResult{
    1: optional map<string,SubProduct> Product
}
struct SubProduct {
    1: optional map<string,list<DiagnoseItem>> SubProduct
}

