include "common.thrift"
include "base.thrift"


// 客户事故列表接口
struct ListCustomerFailureReq{
	1: optional i32 PageSize
	2: optional i32 PageNumber
	3: optional string CustomerNumber

	254: required string        Action             (api.query = "Action",api.const = "ListCustomerFailure")
    255: optional string        Version            (api.query = "Version",api.const = "2024-08-01")
}

struct ListCustomerFailureResp{
	1: optional ListCustomerFailureResult Result
	255: optional base.ResponseMetadata ResponseMetadata
}

struct ListCustomerFailureResult{
	1: optional list<ListCustomerFailureItem> List
	2: optional common.Pagination Pagination
}


struct ListCustomerFailureItem{
    1: optional string FailureID
    2: optional list<i64> VolcAccountIDList                  // 火山账号id
    3: optional list<string> ProductIDList                    // 产品id数组
    4: optional list<string> ProductNameList                  // 影响客户产品名称
    5: optional string FailureName                             // 故障名称
    6: optional i32 FailureStatus                              // 故障状态
    7: optional string FailureStatusName                      // 故障状态中文
    8: optional string FailureEntity                          // 故障方
    9: optional list<string> AccountTeamMemberList         // 相关客户团队邮箱列表
    10: optional list<string> ReleatedTicketIDList           // 相关工单编号
    11: optional map<string,string> TicketSystemURLMap       // 相关工单编号对应跳转链接
    12: optional i64 FailureTime                            // 故障时间
}
