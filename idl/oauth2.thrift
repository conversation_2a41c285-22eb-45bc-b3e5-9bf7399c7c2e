include "base.thrift"
include "common.thrift"

// auth url
struct AuthUrlReq {
    1: required string OriginalPath // 用户访问的原始接口 path
    254: required string Action         (api.query = "Action",api.const = "AuthUrlReq")
    255: optional string Version        (api.query = "Version",api.const = "2025-03-12")

}

struct AuthUrlResult {
    1: required string AuthUrl // 授权码申请地址
}

struct AuthUrlResp {
    1: optional AuthUrlResult Result
    255: optional base.ResponseMetadata ResponseMetadata
}

// token
struct TokenReq {
    1: required string Code   // 授权码
    2: required string State  // 状态码，防止 CSRF 攻击
    254: required string Action         (api.query = "Action",api.const = "TokenReq")
    255: optional string Version        (api.query = "Version",api.const = "2025-03-12")
} 

struct TokenResult {
    1: required string OriginalPath // 用户访问的原始接口 path
    2: required string Token // token
    3: required string Username // 用户名
    4: required string Email // 邮箱
    5: required Department Department // 部门
} 

struct TokenResp {
    1: optional TokenResult Result
    255: optional base.ResponseMetadata ResponseMetadata
}

// userinfo
struct UserInfoReq {
    1: required string Token // 提供的 token
    254: required string Action         (api.query = "Action",api.const = "UserInfoReq")
    255: optional string Version        (api.query = "Version",api.const = "2025-03-12")
}

struct UserInfoResult {
    1: required string Username // 用户名
    2: required string Email // 邮箱
    3: required Department Department // 部门

}

struct Department {
    1: required string Name // 中文名称
    2: required string en_name // 英文名称
}

struct UserInfoResp {
    1: optional UserInfoResult Result
    255: optional base.ResponseMetadata ResponseMetadata
}