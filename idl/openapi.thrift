include "base.thrift"
include "common.thrift"
include "diagnose_item.thrift"
include "diagnose_template.thrift"
include "product_category.thrift"
include "diagnose_result.thrift"

struct UserInfo{
    1: required string UserID
    2: required string UserName
}

struct ListDiagnoseItemRequest {
    2: required i64 PageNumber //页码
    3: required i64 PageSize //一页的数量
    4: optional list<i64> DiagnoseItemIDs //诊断项ID
    5: optional string DiagnoseItemName //诊断项名称
    6: optional string Status //状态
    7: optional list<i64> ProductCategoryIDs //
    10: optional string AccessResponsiblePerson //接入负责人
    254: required string Action             (api.query = "Action",api.const = "ListDiagnoseItem")
    255: optional string Version            (api.query = "Version",api.const = "")
}

struct ListDiagnoseItemResponse {
    1: optional ListDiagnoseItemResult Result
    2: optional base.ResponseMetadata ResponseMetadata
}

struct ListDiagnoseItemResult{
    1: optional list<diagnose_item.DiagnoseItem> DiagnoseItems
    2: optional common.Pagination Pagination
}

struct ListDiagnoseTemplateRequest {
    2: optional list<i64> DiagnoseTemplateIDs,
    3: required i64 PageNumber,
    4: required i64 PageSize,
    5: optional string DiagnoseTemplateName,
    6: optional string Status,
    7: optional string ResponsiblePerson,
    8: optional list<i32> ProductCategoryIDs
    254: required string Action             (api.query = "Action",api.const = "ListDiagnoseTemplate")
    255: required string Version            (api.query = "Version",api.const = "")
}

struct ListDiagnoseTemplateResponse {
    1: optional ListDiagnoseTemplateResult ListDiagnoseTemplateResult,
    2: optional base.ResponseMetadata ResponseMetadata,
}
struct ListDiagnoseTemplateResult{
    1: optional list<diagnose_template.DiagnoseTemplate> DiagnoseTemplates
    2: optional common.Pagination Pagination
}

struct ListProductCategoryRequest {
    1: optional i64 ID,
       2: optional string Product,
       3: optional string SubProduct,
       4: optional string ResourceType,
       5: optional string ProductCn,
       6: optional string SubProductCn,
       7: optional string ResourceTypeCn,
       254: required string Action (api.query = "Action", api.const = "ListProductCategory"),
       255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct ListProductCategoryResponse {
    1: optional ListProductCategoryResult ListProductCategoryResult,
    2: optional base.ResponseMetadata ResponseMetadata,
}
struct ListProductCategoryResult{
    1: optional list<product_category.ProductCategory> ProductCategoryList
}

struct CreateDiagnoseTaskReq {
    1: optional string TicketID,
    2: optional i64 DiagnoseTemplateID,
    3: required string Dimension,
    6: optional list<i64> DiagnoseItemIDs,
    7: required list<diagnose_result.DiagnoseResource> DiagnoseResources,
    8: required string CreateUserID,
    9: optional string CreateUserName,
    10: optional string AccountID,
    11: required i64 DiagnoseStartTime,
    12: required i64 DiagnoseEndTime,
    13: required i32 DiagnoseType,
    15: required list<i64> ProductCategoryIDs,
    16: optional i32 Origin,
    17: optional string OriginName,
    18: required string Name,
    254: required string Action (api.query = "Action", api.const = "CreateDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct CreateDiagnoseTaskResp {
    1: optional CreateDiagnoseTaskResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct CreateDiagnoseTaskResult {
    1: required i64 DiagnoseTaskID,
}

struct QueryDiagnoseTaskResultRequest {
    1: required i64 DiagnoseTaskRunID,
    2: optional list<string> Levels,
    3: optional i64 DiagnoseItemID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskResult"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskResultResponse {
    1: optional QueryDiagnoseTaskResultResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskResultResult {
    1: required list<diagnose_result.DiagnoseResult> DiagnoseResults,
}

struct UpdateDiagnoseItemNameRequest {
    1: required UserInfo UserInfo
    2: required i64 DiagnoseItemID
    3: required string DiagnoseItemName
    254: required string Action             (api.query = "Action",api.const = "UpdateDiagnoseItemName")
    255: optional string Version            (api.query = "Version",api.const = "")
}

struct  UpdateDiagnoseItemNameResult{
    1: optional string Result
}

struct UpdateDiagnoseItemNameResponse {
    1: optional base.ResponseMetadata ResponseMetadata
    2: optional UpdateDiagnoseItemNameResult UpdateDiagnoseItemNameResult
}
