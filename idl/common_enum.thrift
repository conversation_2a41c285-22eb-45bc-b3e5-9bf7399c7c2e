include "common.thrift"
include "base.thrift"
struct GetCommonEnumConfigReq{
	254: required string Action   (api.query = "Action",api.const = "GetCommonEnumConfig")
	255: optional string Version  (api.query = "Version",api.const = "2024-08-01")
}

struct GetCommonEnumConfigResp{
	1:   optional map<string,map<string,list<common.EnumItem>>> Result
	255: optional base.ResponseMetadata                         ResponseMetadata
}

struct GetUploadTokenReq{
	254: required string Action   (api.query = "Action",api.const = "GetUploadToken")
	255: optional string Version  (api.query = "Version",api.const = "")
}

struct UploadToken{
	1:  string AccessKeyId
	2:  string SecretAccessKey
	3:  string SessionToken
	4:  string ExpiredTime
	5:  string CurrentTime
}

struct GetUploadTokenResp{
	1:   optional UploadToken           Result
	255: optional base.ResponseMetadata ResponseMetadata
}

