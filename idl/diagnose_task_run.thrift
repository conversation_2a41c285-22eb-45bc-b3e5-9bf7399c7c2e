include "base.thrift"
include "common.thrift"
include "diagnose_item.thrift"
include "feedback.thrift"
include "diagnose_result.thrift"
include "product_category.thrift"
include "diagnose_task_v2.thrift"
include "diagnose_template.thrift"

//struct DiagnoseItem {
//    1: required i64 ID,
//    2: optional string Name,
//    3: required i64 ProductCategoryID,
//}

//运行诊断任务
struct RunDiagnoseTaskReq {
    1: required i64 DiagnoseTaskID,
    2: required string RunUserID,
    3: optional list<diagnose_result.DiagnoseResource> DiagnoseResources,
    4: required i64 DiagnoseStartTime,
    5: required i64 DiagnoseEndTime,
    6: optional string Name,
    7: optional string AccountID,
    254: required string Action (api.query = "Action", api.const = "RunDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct RunDiagnoseTaskResp {
    1: required RunDiagnoseTaskResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct RunDiagnoseTaskResult {
    1: required i64 DiagnoseTaskRunID,
}

//查询任务执行列表
struct QueryDiagnoseTaskRunListReq {
    1: optional i64 DiagnoseTaskID,
    2: optional list<i64> DiagnoseTaskRunIDs,
    3: optional string Status,
    4: optional string StartTime,
    5: optional string EndTime,
    6: optional bool AscSortByEndTime,
    7: optional string DiagnoseTaskRunName,
    8: optional i32 PageNumber,
    9: optional i32 PageSize,
    10: optional string TicketID,
    11: optional list<i64> DiagnoseTemplateIDs,
    12: optional list<string> DiagnoseResultLevels,
    13: optional list<i64>ProductCategoryIDs,
    14: optional i32 Origin,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunList"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskRunListResp {
    1: required QueryDiagnoseTaskRunListResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryDiagnoseTaskRunListResult {
    1: required list<DiagnoseTaskRun> DiagnoseTaskRunList,
    2: optional common.Pagination Pagination,
}
struct DiagnoseTaskRun {
    1: required i64 ID,
    2: required i64 DiagnoseTaskID,
    3: optional string Name,
    4: optional i32 DiagnoseType,
    5: optional string Status,
    6: optional string StartTime,
    7: optional string EndTime,
    9: optional string RunUserID,
    10: optional string TicketID,
    11: optional diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    12: optional bool Feedback,
    13: optional i64 DiagnoseStartTime,
    14: optional i64 DiagnoseEndTime,
    15: optional string DiagnoseTemplateName,
    16: required i32 Origin,
    17: optional list<product_category.ProductCategory> ProductCategory,
}


//删除执行任务
struct DeleteDiagnoseTaskRunReq {
    1: required i64 ID,
    254: required string Action (api.query = "Action", api.const = "DeleteDiagnoseTaskRun"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct DeleteDiagnoseTaskRunResp {
    255: optional base.ResponseMetadata ResponseMetadata,
}

//查询执行任务详情
struct QueryDiagnoseTaskRunDetailReq {
    1: required i64 DiagnoseTaskRunID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunDetail"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskRunDetailResp {
    1: required QueryDiagnoseTaskRunDetailResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryDiagnoseTaskRunDetailResult {
    1: required DiagnoseTaskRun Information,
    2: required DiagnoseTaskRunByteFlow DiagnoseTaskRunByteFlow,
}
struct DiagnoseTaskRunByteFlow {
    1: optional string StateMachineInfo,
    3: required map<i64,DiagnoseItemStatus> DiagnoseItemStatusList,
}
struct DiagnoseItemStatus {
    1: required diagnose_task_v2.DiagnoseItem DiagnoseItem,
    2: required string Status,
    3: optional string StartTime,
    4: optional string EndTime,
    5: optional diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    6: optional list<diagnose_result.DiagnoseResource> DiagnoseResources,
}

//查询执行结果中某个诊断项详情
struct QueryDiagnoseTaskRunItemDetailReq {
    1: required i64 DiagnoseTaskRunID,
    2: required i64 DiagnoseItemID,
    3: optional list<string> DiagnoseResultLevel,
    4: optional list<string> InstanceID,
    5: optional i32 PageNumber,
    6: optional i32 PageSize,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunItemDetail"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskRunItemDetailResp {
    1: required DiagnoseTaskRunItemDetailResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct DiagnoseTaskRunItemDetailResult {
    1: required diagnose_task_v2.DiagnoseItem DiagnoseItem,
    2: required diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    3: required list<diagnose_result.DiagnoseResult> DiagnoseResults,
    4: optional common.Pagination Pagination,
}

struct QueryDiagnoseTaskRunItemsReq {
    1: required i64 DiagnoseTaskRunID,
    2: optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunItems"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskRunItemsResp {
    1: optional QueryDiagnoseTaskRunItemsResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskRunItemsResult {
    1: required list<diagnose_task_v2.DiagnoseItem> DiagnoseItems,
}

struct QueryDiagnoseTaskRunResourceReq {
    1: required i64 DiagnoseTaskRunID,
    2: optional list<i64> DiagnoseItemIDs,
    3: optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunResource"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskRunResourceResp {
    1: optional QueryDiagnoseTaskRunResourceResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryDiagnoseTaskRunResourceResult {
    1: required list<diagnose_result.DiagnoseResource> DiagnoseResources,
}
struct QueryDiagnoseTaskRunCategoryReq {
    1: required i64 DiagnoseTaskRunID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunCategory"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskRunCategoryResp {
    1: optional QueryDiagnoseTaskRunCategoryResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskRunCategoryResult {
    1: required list<diagnose_result.DiagnoseTaskCategory> DiagnoseTaskRunCategoryList,
}

struct QueryDiagnoseTaskRunSummaryReq {
    1: required i64 DiagnoseTaskRunID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskRunSummary"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskRunSummaryResp {
    1: optional QueryDiagnoseTaskRunSummaryResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskRunSummaryResult {
    1: required diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    2: optional string DiagnoseTemplateName,
    3: optional string StartTime,
    4: optional string EndTime,
    5: optional string CreateUserID,
    6: optional string CreateUserName,
    7: required i32 Progress,
    8: required bool Feedback,
    9: required i64 DiagnoseStartTime,
    10: required i64 DiagnoseEndTime,
    11: required list<ProductCategoryLevel> ProductCategoryLevels,
}

struct ProductCategoryLevel {
    1: required product_category.ProductCategory ProductCategory,
    2: diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
}




