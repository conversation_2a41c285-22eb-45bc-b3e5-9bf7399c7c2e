include "base.thrift"
struct QueryProductCategoryListReq {
    1: optional i64 ID,
    2: optional string Product,
    3: optional string SubProduct,
    4: optional string ResourceType,
    5: optional string ProductCn,
    6: optional string SubProductCn,
    7: optional string ResourceTypeCn,
    254: required string Action (api.query = "Action", api.const = "QueryProductCategoryList"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryProductCategoryListResp {
    1: optional QueryProductCategoryListResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryProductCategoryListResult {
    1: required list<ProductCategory> ProductCategoryList,
}
struct ProductCategory {
    1: required string Product,
    2: required string SubProduct,
    3: required string ResourceType,
    4: required string ProductCn,
    5: required string SubProductCn,
    6: required string ResourceTypeCn,
    7: required i64 ID,
    8: required list<Argument> Arguments,
    9: optional string GrafanaTemplate,
    10: optional i64 MaxDurationHour,
    11: optional i64 MaxStartTimeBeforeNowHour,
}

struct Argument{
    1: required string Key,
    2: required string Name,
}

struct QueryProductCategoryListByIDsReq {
    1: required list<i64> IDs,
    254: required string Action (api.query = "Action", api.const = "QueryProductCategoryListByIDs"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
