include "base.thrift"
include "common.thrift"
include "diagnose_item.thrift"
include "diagnose_result.thrift"
include "diagnose_task_run.thrift"
include "diagnose_task_v2.thrift"

struct DescribeTaskDiagnoseItemReq {
    1: required i64 DiagnoseTaskID,
    2: required i64 DiagnoseItemID,
    3: optional list<string> DiagnoseResultLevel,
    4: optional list<string> InstanceID,
    5: optional i32 PageNumber,
    6: optional i32 PageSize,
    254: required string Action (api.query = "Action", api.const = "DescribeTaskDiagnoseItem"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct DescribeTaskDiagnoseItemResp {
    1: optional DescribeTaskDiagnoseItemResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct DescribeTaskDiagnoseItemResult {
    1: required diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    2: required list<diagnose_result.DiagnoseResult> DiagnoseResults,
    3: optional common.Pagination Pagination,
}

struct QueryDiagnoseTaskCategoryReq {
    1: required i64 DiagnoseTaskID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskCategory"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskCategoryResp {
    1: optional QueryDiagnoseTaskCategoryResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskCategoryResult {
    1: required list<diagnose_result.DiagnoseTaskCategory> DiagnoseTaskCategoryList,
}

struct QueryDiagnoseTaskSummaryReq {
    1: required i64 DiagnoseTaskID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskSummary"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskSummaryResp {
    1: optional QueryDiagnoseTaskSummaryResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskSummaryResult {
    1: required diagnose_result.DiagnoseResultLevel DiagnoseResultLevel,
    2: optional string DiagnoseTemplateName,
    3: optional string StartTime,
    4: optional string EndTime,
    5: optional string CreateUserID,
    6: optional string CreateUserName,
    7: required i32 Progress,
    8: required bool Feedback,
    9: required i64 DiagnoseStartTime,
    10: required i64 DiagnoseEndTime,
}

struct QueryDiagnoseTaskResourcesReq {
    1: required i64 DiagnoseTaskID,
    2: optional list<i64> DiagnoseItemIDs,
    3: optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskResource"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskResourcesResp {
    1: optional QueryDiagnoseTaskResourcesResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryDiagnoseTaskResourcesResult {
    1: required list<diagnose_result.DiagnoseResource> DiagnoseResources,
}

struct QueryDiagnoseTaskItemsReq {
    1: required i64 DiagnoseTaskID,
    2: optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "QueryTaskDiagnoseItems"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct QueryDiagnoseTaskItemsResp {
    1: optional QueryDiagnoseTaskItemsResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct QueryDiagnoseTaskItemsResult {
    1: required list<diagnose_task_v2.DiagnoseItem> DiagnoseItems,
}

struct ListTicketDiagnoseTaskReq {
    1: required string TicketID,
    2: optional i32 PageNumber,
    3: optional i32 PageSize,
    4: optional bool AscSortByEndTime,
    5: optional string Status,
    254: required string Action (api.query = "Action", api.const = "ListTicketDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct ListTicketDiagnoseTaskResp {
    1: optional ListTicketDiagnoseTaskResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct ListTicketDiagnoseTaskResult {
    1: required list<DiagnoseTask> DiagnoseTaskList,
    2: optional common.Pagination Pagination,
}

struct DiagnoseTask {
    1: required i64 DiagnoseTaskID,
    2: optional string DiagnoseTemplateName,
    3: optional string Status,
    4: optional string StartTime,
    5: optional string EndTime,
    6: optional string CreateUserID,
    7: optional string CreateUserName,
    8: optional string TicketID,
    9: optional bool Feedback,
    10: optional i64 DiagnoseStartTime,
    11: optional i64 DiagnoseEndTime,
}
struct ListDiagnoseTaskReq {
    1: optional string TicketID,
    2: optional list<i64> DiagnoseTemplateIDs,
    3: optional string Status,
    5: optional string StartTime,
    6: optional string EndTime,
    8: optional i32 PageNumber,
    9: optional i32 PageSize,
    10: optional bool AscSortByEndTime,
    254: required string Action (api.query = "Action", api.const = "ListDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}

struct ListDiagnoseTaskResp {
    1: optional ListDiagnoseTaskResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

struct ListDiagnoseTaskResult {
    1: required list<DiagnoseTask> DiagnoseTaskList,
    2: optional common.Pagination Pagination,
}

struct CreateDiagnoseTaskReq {
    1: required string TicketID,
    2: optional i64 DiagnoseTemplateID,
    3: required string Dimension,
    6: optional list<i64> DiagnoseItemIDs,
    7: required list<diagnose_result.DiagnoseResource> DiagnoseResources,
    8: required string CreateUserID,
    9: optional string CreateUserName,
    10: optional string AccountID,
    11: required i64 DiagnoseStartTime,
    12: required i64 DiagnoseEndTime,
    13: required i32 DiagnoseType,
    15: required list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "CreateDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct CreateDiagnoseTaskResp {
    1: optional CreateDiagnoseTaskResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct CreateDiagnoseTaskResult {
    1: required i64 DiagnoseTaskID,
}