include "base.thrift"
include "common.thrift"
include "diagnose_item.thrift"
include "diagnose_result.thrift"
include "product_category.thrift"

//查询诊断任务列表
struct QueryDiagnoseTaskListReq {
    1: optional string Name,
    2: optional i32 Origin,
    3: optional i64 DiagnoseTemplateID,
    4: optional i32 PageNumber,
    5: optional i32 PageSize,
    6: optional bool AscSortByLastRunTime,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskList"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskListResp  {
    1: optional QueryDiagnoseTaskListResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}
struct QueryDiagnoseTaskListResult  {
    1: required list<DiagnoseTask> DiagnoseTaskList,
    2: optional common.Pagination Pagination,
}
struct DiagnoseTask {
    1: required i64 ID,
    2: optional string Name,
    3: optional i32 DiagnoseType,// 诊断类型 1-原子 2-编排
    4: optional string UpdateUserID,
    5: optional string UpdateTime,
    6: optional string LastRunTime,
    7: optional string CreateTime,
    8: optional string CreateUserID,
    9: optional i32 Origin,
    10: optional list<DiagnoseItem> DiagnoseItems,
    11: optional list<product_category.ProductCategory> ProductCategorys,
    12: optional string Dimension,// 诊断维度,资源/账号
    13: optional string TicketID,
    14: optional i64 DiagnoseTemplateID,
}
struct DiagnoseItem {
    1: required i64 ID,
    2: optional string Name,
    3: required product_category.ProductCategory ProductCategory,
}


//删除诊断任务
struct DeleteDiagnoseTaskReq {
    1: required i64 ID,
    254: required string Action (api.query = "Action", api.const = "DeleteDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct DeleteDiagnoseTaskResp {
    255: optional base.ResponseMetadata ResponseMetadata,
}

//查询诊断任务详情
struct QueryDiagnoseTaskDetailReq {
    1: required i64 ID,
    254: required string Action (api.query = "Action", api.const = "QueryDiagnoseTaskDetail"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct QueryDiagnoseTaskDetailResult{
    1: required DiagnoseTask DiagnoseTaskInfo,
}
struct QueryDiagnoseTaskDetailResp {
    1: required QueryDiagnoseTaskDetailResult Result,
    255: optional base.ResponseMetadata ResponseMetadata,
}

//创建诊断任务
struct CreateDiagnoseTaskV2Req {
    1: required string Name,
    2: required i32 DiagnoseType,// 诊断类型 1-原子 2-编排
    3: optional i64 DiagnoseTemplateID,//诊断编排模板ID
    4: optional list<i64> DiagnoseItemIDs,
    6: required string Dimension,// 诊断维度,资源/账号
    7: required string CreateUserID,
    8: optional string TicketID,
    9: optional i32 Origin,
    10:optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "CreateDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct CreateDiagnoseTaskV2Resp {
    1: required i64 ID,
    255: optional base.ResponseMetadata ResponseMetadata,
}

//更新诊断任务
struct UpdateDiagnoseTaskReq {
    1: required i64 ID,
    2: optional string Name,
    3: optional i32 DiagnoseType,// 诊断类型 1-原子 2-编排
    4: optional i64 DiagnoseTemplateID,//诊断编排模板ID
    5: optional list<i64> DiagnoseItemIDs,
    7: optional string Dimension,// 诊断维度,资源/账号
    8: required string UpdateUserID,
    9: optional string TicketID,
    10:optional list<i64> ProductCategoryIDs,
    254: required string Action (api.query = "Action", api.const = "UpdateDiagnoseTask"),
    255: optional string Version (api.query = "Version", api.const = "2024-12-01"),
}
struct UpdateDiagnoseTaskResp {
    255: optional base.ResponseMetadata ResponseMetadata,
}

