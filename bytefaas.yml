id: 1ys39i5h
handler: index.handler
initializer: index.initializer
name: cloud_sherlock_worker
env_name: prod
runtime: golang/v1
protocol: http
origin: bytefaas
owner: zhuruiqing
admins: zhuruiqing
psm: volcengine.support.cloud_sherlock_worker
psm_parent_id: 1729536
category: event
source: volcengine/support/cloud_sherlock:*******
source_type: scm
authorizers: xiejing.x
service_level: P2
service_purpose: convention
clusters:
- cluster: faas-cn-boe2
  region: cn-boe2
  initializer_sec: 100
  max_concurrency: 100
  auth_enable: false
  zti_enable: true
  cors_enable: false
  exclusive_mode: false
  is_ipv_6_only: true
  latency_sec: 5
  resource_limit:
    memmb: 1024
    cpumilli: 500
    diskmb: 0
    gpu:
      partitions: 0
      accelerator: ""
    socket: 0
  async_mode: false
  is_this_zone_disabled:
    boe2: false
  code_revision_id: ""
  code_revision_number: ""
  format_envs:
  - env_key: BYTEFAAS_ENABLE_SIDECAR
    env_value: "False"
  - env_key: BYTEFAAS_MONOIO_ENABLE
    env_value: "true"
  - env_key: TCE_ENABLE_HTTP_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_HTTP_SIDECAR_INGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_MESH_ACL
    env_value: "False"
  - env_key: TCE_ENABLE_MONGO_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_MYSQL_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_REDIS_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_SIDECAR_INGRESS
    env_value: "False"
  - env_key: TCE_ENABLE_TOS_SIDECAR_EGRESS
    env_value: "False"
  - env_key: TCE_INSTALL_SIDECAR
    env_value: "False"
  - env_key: TZ
    env_value: ""